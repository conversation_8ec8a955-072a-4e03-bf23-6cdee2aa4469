@php
    // デバッグ用
    if (!isset($data)) {
        $data = [];
    } elseif (!is_array($data)) {
        \Log::error('$data is not array: ' . gettype($data) . ' - ' . var_export($data, true));
        $data = [];
    }
@endphp

@if (Admin::user()->permission_type == 3 || Admin::user()->id == 1)
    <div class="box box-default">
        <div class="box-header with-border">
            <h3 class="box-title">eBay連携情報</h3>

            <div class="box-tools pull-right">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            </div>
            <span class="help-block"><i class="fa fa-info-circle"></i>eBayアカウントとの紐付けとツール利用開始方法は▶<a target="_blank"
                    href="https://www.youtube.com/watch?v=8rMXbD4nFbU">こちら</a></span>
            <label></label>
        </div>

        <!-- eBay認証処理用の隠しiframe -->
        <iframe id="ebayAuthFrame" name="ebayAuthFrame" style="display:none;"></iframe>

        <script>
            // グローバル変数
            let currentAuthCode = null;
            let authWindow = null;

            function oauth() {
                // 有料会員でない場合は何もしない
                @if (Admin::user()->member_type != 1)
                    return false;
                @endif

                // $dataの存在確認を追加
                @if (isset($data) && is_array($data) && isset($data['url']))
                    // eBay認証ウィンドウを開く
                    authWindow = window.open('{!! $data['url'] !!}', 'ebay_auth', 'width=500,height=400');
                @else
                    alert('eBay認証URLが設定されていません');
                    return false;
                @endif

                // 認証完了を監視
                const checkClosed = setInterval(function() {
                    if (authWindow.closed) {
                        clearInterval(checkClosed);
                        // 認証完了後、コールバック処理を実行
                        setTimeout(function() {
                            handleEbayAuthCompletion();
                        }, 1000);
                    }
                }, 1000);

                return false;
            }

            // eBay認証完了後の処理（コールバック用）
            function handleEbayAuthSuccess(authCode) {
                console.log('eBay auth success callback triggered', authCode);
                currentAuthCode = authCode;

                // 事前チェックを実行
                performPreCheck(authCode);
            }

            // eBay認証完了後の処理（ポーリング用フォールバック）
            function handleEbayAuthCompletion() {
                console.log('eBay auth completion check triggered');

                // まず現在のユーザー情報を確認
                fetch('/admin/auth/user-info', {
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success' && data.ebay_id) {
                            // 新しく連携された場合
                            location.reload();
                        } else {
                            // まだ連携されていない場合（通常はコールバックで処理される）
                            console.log('No new eBay connection detected');
                        }
                    })
                    .catch(error => {
                        console.error('Error checking user info:', error);
                    });
            }

            // 事前チェック実行
            function performPreCheck(authCode) {
                if (!authCode) {
                    console.error('No auth code provided');
                    return;
                }

                // ローディング表示
                showProcessingIndicator('eBayアカウント情報を確認中...');

                fetch('/admin/ebay/pre-check', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        body: JSON.stringify({
                            auth_code: authCode
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideProcessingIndicator();

                        console.log('Pre-check response:', data);

                        if (data.status === 'success') {
                            if (data.will_cancel_trial) {
                                // 無料期間キャンセルの警告を表示
                                showTrialCancellationWarning(data, authCode);
                            } else {
                                // 問題なし、連携を確定
                                confirmEbayConnection(authCode, false);
                            }
                        } else {
                            alert('eBayアカウント情報の取得に失敗しました: ' + (data.message || '不明なエラー'));
                        }
                    })
                    .catch(error => {
                        hideProcessingIndicator();
                        console.error('Pre-check error:', error);
                        alert('eBayアカウント確認中にエラーが発生しました。再度お試しください。');
                    });
            }

            // 無料期間キャンセル警告を表示
            function showTrialCancellationWarning(ebayData, authCode) {
                // 警告内容を動的に更新
                $('#warningEbayUsername').text(ebayData.ebay_username);

                // モーダルを表示
                $('#ebayTrialWarningModal').modal({
                    backdrop: 'static',
                    keyboard: false
                });

                // 続行ボタンのクリックイベント設定
                $('#proceedEbayLink').off('click').on('click', function() {
                    $('#ebayTrialWarningModal').modal('hide');
                    confirmEbayConnection(authCode, true);
                });
            }

            // eBay連携確定処理
            function confirmEbayConnection(authCode, confirmedTrialCancellation) {
                showProcessingIndicator('eBay連携を確定しています...');

                fetch('/admin/ebay/confirm-connection', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        body: JSON.stringify({
                            auth_code: authCode,
                            confirmed_trial_cancellation: confirmedTrialCancellation
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideProcessingIndicator();

                        console.log('Confirm connection response:', data);

                        if (data.status === 'success') {
                            let message = 'eBay連携が完了しました。';
                            if (data.trial_cancelled) {
                                message += '\n無料期間は終了し、通常料金での請求が開始されます。';
                            }
                            alert(message);
                            location.reload();
                        } else if (data.status === 'confirmation_required') {
                            // 確認が必要な場合（通常は事前チェックで処理済み）
                            if (confirm('このeBayアカウントは過去に使用されています。無料期間が終了しますが、続行しますか？')) {
                                confirmEbayConnection(authCode, true);
                            }
                        } else {
                            alert('eBay連携に失敗しました: ' + (data.message || '不明なエラー'));
                        }
                    })
                    .catch(error => {
                        hideProcessingIndicator();
                        console.error('Confirm connection error:', error);
                        alert('eBay連携中にエラーが発生しました。再度お試しください。');
                    });
            }

            // 処理中インジケーター表示
            function showProcessingIndicator(message) {
                $('#processingMessage').text(message || '処理中...');
                $('#processingModal').modal('show');
            }

            // 処理中インジケーター非表示
            function hideProcessingIndicator() {
                $('#processingModal').modal('hide');
            }

            // 既存のeBay連携をクリアする関数
            function clearEbayConnection() {
                if (!confirm('eBay連携を解除しますか？\n※出品中の商品には影響しません')) {
                    return;
                }

                showProcessingIndicator('eBay連携を解除しています...');

                fetch('/admin/ebay/disconnect', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideProcessingIndicator();
                        if (data.status === 'success') {
                            alert('eBay連携を解除しました');
                            location.reload();
                        } else {
                            alert('連携解除に失敗しました: ' + (data.message || '不明なエラー'));
                        }
                    })
                    .catch(error => {
                        hideProcessingIndicator();
                        console.error('連携解除エラー:', error);
                        alert('連携解除中にエラーが発生しました');
                    });
            }
        </script>
        <!-- /.box-header -->
        <div class="box-body">
            <div class="table-responsive">
                <table class="table table-striped">

                    <tr>
                        <td width="180px"></td>
                        <td>
                            @if (Admin::user()->member_type != 1)
                                <button disabled title="有料会員のみ利用可能です" class="btn btn-default" style="margin-top: 0px;">
                                    <i class="fa fa-lock"></i> eBay連携（有料会員限定）
                                </button>
                            @elseif (isset($data) && is_array($data) && !empty($data['username']))
                                <div class="ebay-action-buttons">
                                    <button onclick="oauth();" class="btn btn-warning">
                                        <i class="fa fa-refresh"></i> eBay再連携
                                    </button>
                                    <button onclick="clearEbayConnection();" class="btn btn-danger"
                                        style="margin-top: 0px;">
                                        <i class="fa fa-unlink"></i> 連携解除
                                    </button>
                                </div>
                            @else
                                <button onclick="oauth();" class="btn btn-success" style="margin-top: 0px;">
                                    <i class="fa fa-link"></i> eBay連携
                                </button>
                            @endif
                        </td>
                    </tr>

                    <tr>
                        <td width="180px">連携済みユーザID</td>
                        <td>
                            @if (isset($data) && is_array($data) && !empty($data['username']))
                                <span class="text-success">
                                    <i class="fa fa-check-circle"></i> {{ $data['username'] }}
                                </span>
                            @else
                                <span class="text-muted">未連携</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td width="180px">トークン有効期限</td>
                        <td>
                            @if (isset($data) && is_array($data) && !empty($data['expires_in']))
                                @php
                                    $expires = strtotime($data['expires_in']);
                                    $now = time();
                                    $days_left = ceil(($expires - $now) / 86400);
                                @endphp
                                @if ($days_left > 7)
                                    <span class="text-success">{{ $data['expires_in'] }} (残り{{ $days_left }}日)</span>
                                @elseif ($days_left > 0)
                                    <span class="text-warning">{{ $data['expires_in'] }} (残り{{ $days_left }}日)</span>
                                @else
                                    <span class="text-danger">{{ $data['expires_in'] }} (期限切れ)</span>
                                @endif
                            @else
                                <span class="text-muted">未設定</span>
                            @endif
                        </td>
                    </tr>

                    <!-- 無料期間への影響を表示（強化版） -->
                    @if (Admin::user()->member_type == 1 && isset($data) && is_array($data) && !empty($data['username']))
                        <tr id="trial-impact-row" style="display: none;">
                            <td width="180px">無料期間への影響</td>
                            <td id="trial-impact-text">確認中...</td>
                        </tr>
                    @endif

                </table>
            </div>
            <!-- /.table-responsive -->
        </div>
        <!-- /.box-body -->
    </div>

    <!-- 処理中モーダル -->
    <div class="modal fade" id="processingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status" style="margin-bottom: 15px;">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p id="processingMessage">処理中...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- eBay連携時の無料期間警告モーダル（強化版） -->
    <div class="modal fade" id="ebayTrialWarningModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #d9534f; color: white;">
                    <h4 class="modal-title">
                        <i class="fa fa-exclamation-triangle"></i> 無料期間終了のお知らせ
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" style="color: white;">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <strong><i class="fa fa-warning"></i> このeBayアカウントは過去に使用されています</strong>
                    </div>
                    <div class="ebay-account-info">
                        <p><strong>eBayアカウント:</strong> <span id="warningEbayUsername"></span></p>
                    </div>
                    <p>連携を続行すると以下の変更が<strong>即座に</strong>適用されます：</p>
                    <ul class="list-unstyled">
                        <li><i class="fa fa-times text-danger"></i> <strong>2ヶ月間の無料期間が即座に終了</strong></li>
                        <li><i class="fa fa-calendar text-warning"></i> 次回請求日が本日に変更</li>
                        <li><i class="fa fa-credit-card text-info"></i> 以降は通常料金での請求開始</li>
                    </ul>
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        <strong>なぜこの制限があるのか？</strong><br>
                        同一eBayアカウントでの重複した無料期間利用を防ぐためです。
                    </div>
                    <p class="text-center"><strong>本当に連携を続行しますか？</strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times"></i> キャンセル
                    </button>
                    <button type="button" class="btn btn-danger" id="proceedEbayLink">
                        <i class="fa fa-link"></i> 無料期間を終了して連携を続行
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // ページ読み込み時に無料期間への影響をチェック
            @if (Admin::user()->member_type == 1 && isset($data) && is_array($data) && !empty($data['username']))
                checkCurrentEbayTrialImpact();
            @endif
        });

        // 現在のeBay連携の無料期間への影響をチェック（強化版）
        function checkCurrentEbayTrialImpact() {
            const ebayId = '{{ isset($data) && is_array($data) ? $data['ebay_id'] ?? '' : '' }}';
            if (!ebayId) return;

            fetch('/admin/ebay/trial-impact-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    body: JSON.stringify({
                        ebay_id: ebayId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        $('#trial-impact-row').show();
                        if (data.is_returning_ebay) {
                            $('#trial-impact-text').html(`
                                <span class="text-danger">
                                    <i class="fa fa-exclamation-triangle"></i>
                                    過去利用済み（無料期間対象外）
                                </span>
                            `);
                        } else {
                            $('#trial-impact-text').html(`
                                <span class="text-success">
                                    <i class="fa fa-check-circle"></i>
                                    影響なし（2ヶ月無料期間継続）
                                </span>
                            `);
                        }
                    }
                })
                .catch(error => {
                    console.error('無料期間への影響確認エラー:', error);
                });
        }
    </script>

    <style>
        .btn {
            margin-right: 5px;
        }

        .text-success {
            color: #5cb85c !important;
        }

        .text-warning {
            color: #f0ad4e !important;
        }

        .text-danger {
            color: #d9534f !important;
        }

        .text-muted {
            color: #777 !important;
        }

        /* モーダルのスタイル強化 */
        .modal-content {
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        .alert {
            margin-bottom: 15px;
        }

        .list-unstyled li {
            padding: 5px 0;
            font-size: 14px;
        }

        #proceedEbayLink {
            font-weight: bold;
        }

        .ebay-account-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }

        .ebay-action-buttons {
            display: flex;
            gap: 10px;
            align-items: center;
        }


        /* 処理中モーダル用スタイル */
        .spinner-border {
            width: 3rem;
            height: 3rem;
            border-width: 0.3em;
        }

        /* CSS Spinner for older browsers */
        .spinner-border:not(.spinner-border) {
            display: inline-block;
            width: 3rem;
            height: 3rem;
            border: 0.3em solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border .75s linear infinite;
        }

        @keyframes spinner-border {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
@endif

<h4>{{$label}}</h4>
@include('admin::form.help-block')
<div class="form-group ">
    <label for="purchase_price" class="control-label col-sm-4">仕入れ価格<br>(円)</label>
    <div class="col-sm-6">
        <div class="input-group">
			<input type="text" class="form-control" id="purchase_price" placeholder="仕入れ価格"></input>
		</div>
		<span class="help-block">
			<i class="fa fa-info-circle"></i>&nbsp;仕入れに伴う消費税･送料･手数料等込で入力してください。
		</span>
    </div>
</div>
<div class="form-group ">
    <label for="selling_price" class="control-label col-sm-4">販売価格<br>(USD)</label>
    <div class="col-sm-6">
        <div class="input-group">
			<input type="text" class="form-control" id="selling_price" placeholder="販売価格"></input>
		</div>
    </div>
</div>

<div class="form-group ">
    <label for="selling_category" class="control-label col-sm-4">販売<br>カテゴリー</label>
    <select id="selling_category" class="col-sm-6"></select>
</div>

<div class="form-group ">
    <label for="overseas_clearing_fee" class="control-label col-sm-4">海外販売<br>手数料</label>
    <select id="overseas_clearing_fee" class="col-sm-6"></select>
</div>

<div class="form-group ">
    <label for="exchange_market_price" class="control-label col-sm-4">為替相場<br>(円/USD)</label>
    <div class="col-sm-6">
        <div class="input-group">
			<input type="text" class="form-control" id="exchange_market_price" placeholder="為替相場" value="150"></input>
		</div>
    </div>
</div>

<div class="form-group ">
    <label for="selling_region" class="control-label col-sm-4">発送地域</label>
    <select id="selling_region" class="col-sm-6"></select>
</div>

<div class="form-group ">
    <label for="size_weight" class="control-label col-sm-4">容積重量/<br>実重量</label>
    <div class="col-sm-8">
        <div class="input-group" style="">
			<div style="display:flex;flex-direction: row;flex-wrap: nowrap;align-items: center;justify-content: space-between;">
				<label>縦(cm)</label>
				<input type="text" class="form-control" id="size_v" placeholder="縦(cm)" value="10" style="width:70%;max-width:150px;"></input>
			</div>
			<div style="display:flex;flex-direction: row;flex-wrap: nowrap;align-items: center;justify-content: space-between;">
				<label>横(cm)</label>
				<input type="text" class="form-control" id="size_h" placeholder="横(cm)" value="10" style="width:70%;max-width:150px;"></input>
			</div>
			<div style="display:flex;flex-direction: row;flex-wrap: nowrap;align-items: center;justify-content: space-between;">
				<label>高さ(cm)</label>
				<input type="text" class="form-control" id="size_height" placeholder="高さ(cm)" value="10" style="width:70%;max-width:150px;"></input>
			</div>
			<div style="display:flex;flex-direction: row;flex-wrap: nowrap;align-items: center;justify-content: space-between;">
				<label>重量(kg)</label>
				<select class="form-control" id="size_weight" placeholder="重量(kg)" style="width:50%;max-width:150px;"></select>
			</div>
			<!-- <input type="number" class="form-control" id="size_weight" placeholder="重量(kg)" value=""></input> -->
		</div>
    </div>
</div>


<div class="form-group ">
    <label class="col-sm-2  control-label"></label>
    <div class="col-sm-8">
        <input type="button" value="粗利試算" class="btn get_ebay_gross_profit">
    </div>
</div>

<h5>計算結果</h5>
<div class="form-group ">
    <label for="total_calculating_fee" class="control-label col-sm-4">総手数料(USD)</label>
    <div class="col-sm-8">
        <div class="input-group">
			<input type="number" class="form-control" id="total_calculating_fee" placeholder="総手数料" disabled></input>
		</div>
    </div>
</div>

<div class="form-group ">
    <label for="delivery_charge" class="control-label col-sm-4">配送料(円)</label>
    <div class="col-sm-8">
        <div class="input-group">
			<input type="number" class="form-control" id="delivery_charge" placeholder="配送料" disabled></input>
		</div>
		<span class="help-block">
			<i class="fa fa-info-circle"></i>&nbsp;デフォルトではFedEx（eLogi）のUS向け配送料となります。

		</span>
    </div>
</div>

<div class="form-group ">
    <label for="expected_profit" class="control-label col-sm-4">予想利益(円)</label>
    <div class="col-sm-8">
        <div class="input-group">
			<input type="number" class="form-control" id="expected_profit" placeholder="予想利益" disabled></input>
		</div>
    </div>
</div>

<hr />
<script>

let gross_profit_categories = [
	'Antiques',
	'Art NFTs',
	'Most Art categories',
	'Baby',
	'Books & Magazines',
	'Heavy Equipment / Commercial Printing Presses / Food Trucks, Trailers & Carts',
	'Most Business & Industrial categories',
	'Camera, Drone & Photo Accessories (except Memory Cards) / Replacement Parts & Tools / Tripods & Supports / Other Cameras & Photo',
	'Most Cameras & Photo categories',
	'Cell Phone Accessories (except Memory Cards)',
	'Most Cell Phones & Accessories categories',
	"Women's Bags & Handbags",
	'Athletic Shoes',
	'Most Clothing, Shoes & Accessories categories',
	'Bullion',
	'Most Coins & Paper Money categories',
	'Emerging NFTs / Non-Sport Trading Card NFTs',
	'Most Collectibles categories',
	"Desktops & All-In-Ones / Laptops & Netbooks / Computer Components & Parts / Hard Drives (HDD, SSD & NAS) / Monitors / Printers",
	"3D Printers & Supplies / Computer Cables & Connectors / Keyboards, Mice & Pointers / Laptop & Desktop Accessories / Other Computers & Networking / Power Protection, Distribution / Tablet & eBook Reader Accs (except Memory Card & USB Adapters)",
	'Most Computers/Tablets & Networking categories',
	"Multipurpose Batteries & Power / Portable Audio Accessories / TV, Video & Home Audio / Vehicle Electronics & GPS / Virtual Reality",
	'Most Consumer Electronics categories',
	'Crafts',
	'Dolls & Bears',
	'Entertainment Memorabilia',
	'Health & Beauty',
	'Home & Garden',
	'Watches, Parts & Accessories',
	'Most Jewelry & Watches categories',
	'Car & Truck Parts & Accessories > Wheels, Tires & Parts > Tires',
	'Apparel, Protective Gear & Merchandise',
	'In-Car Technology, GPS & Security',
	'Most eBay Motors',
	'Movie NFTs',
	'Most Movies & TV categories',
	'Vinyl Records',
	'Music NFTs',
	'Most Music categories',
	'DJ Equipment / Pro Audio Equipment',
	'Guitars & Basses',
	'Most Musical Instruments & Gear categories',
	'Pet Supplies',
	'Pottery & Glass',
	'Specialty Services',
	'Sporting Goods',
	'Sport Trading Card NFTs',
	'Most Sports Mem, Cards & Fan Shop categories',
	'Stamps',
	'CCG NFTs',
	'Most Toys & Hobbies categories',
	"Video Game Accessories / Replacement Parts & Tools / Video Games",
	'Video Game Consoles',
	'Most Video Games & Consoles categories',
	'All other categories'
];

let overseas_clearing_fee = [
	{name:'$3,000未満',val:1.35},
	{name:'$3,000以上$10,000未満',val:1.20},
	{name:'$10,000以上$50,000未満',val:0.95},
	{name:'$50,000以上$100,000未満',val:0.70},
	{name:'$100,000以上',val:0.40}
];

let selling_region = {
	'us' : "アメリカ",
	'eu' : "ヨーロッパ(イギリス･ドイツ等)",
	'ap' : "アジア(シンガポール等)",
	'sa' : "南米(ブラジル等)",
	'af' : "アフリカ(ナイジェリア等)",
}

let delivery_charge_list = {
	0.5		: 2600 ,
	1.0		: 2900 ,
	2.0		: 3200 ,
	3.0		: 4100 ,
	4.0		: 4700 ,
	5.0		: 5700 ,
	6.0		: 7200 ,
	7.0		: 7600 ,
	8.0		: 7900 ,
	9.0		: 8200 ,
	10.0	: 10700,
	11.0	: 11100,
	12.0	: 11500,
	13.0	: 13800,
	14.0	: 14200,
	15.0	: 14700,
	16.0	: 17300,
	17.0	: 17900,
	18.0	: 18400,
	19.0	: 18900,
	20.0	: 19400,
};

let weight_chage_list_us = {
	"0.5"  : 2800,
	"1.0"  : 3100,
	"1.5"  : 3300,
	"2.0"  : 3300,
	"2.5"  : 3700,
	"3.0"  : 4300,
	"3.5"  : 4300,
	"4.0"  : 4900,
	"4.5"  : 5400,
	"5.0"  : 6000,
	"5.5"  : 7300,
	"6.0"  : 7500,
	"6.5"  : 7700,
	"7.0"  : 7900,
	"7.5"  : 8100,
	"8.0"  : 8200,
	"8.5"  : 8400,
	"9.0"  : 8600,
	"9.5"  : 10900,
	"10.0" : 11100,
	"10.5" : 11300,
	"11.0" : 11500,
	"11.5" : 11700,
	"12.0" : 11900,
	"12.5" : 14100,
	"13.0" : 14300,
	"13.5" : 14600,
	"14.0" : 14800,
	"14.5" : 15000,
	"15.0" : 15300,
	"15.5" : 15500,
	"16.0" : 18000,
	"16.5" : 18300,
	"17.0" : 18600,
	"17.5" : 18800,
	"18.0" : 19100,
	"18.5" : 19400,
	"19.0" : 19700,
	"19.5" : 19900,
	"20.0" : 20200,
}

let weight_chage_list_eu = {
	"1.0"	: 3100,
	"0.5"	: 2800,
	"1.5"	: 3300,
	"2.0"	: 3300,
	"2.5"	: 3700,
	"3.0"	: 4200,
	"3.5"	: 4200,
	"4.0"	: 4600,
	"4.5"	: 5000,
	"5.0"	: 5400,
	"5.5"	: 6400,
	"6.0"	: 6600,
	"6.5"	: 6800,
	"7.0"	: 7000,
	"7.5"	: 7200,
	"8.0"	: 7400,
	"8.5"	: 7600,
	"9.0"	: 7800,
	"9.5"	: 9800,
	"10.0"	: 10100,
	"10.5"	: 10200,
	"11.0"	: 10400,
	"11.5"	: 10500,
	"12.0"	: 10700,
	"12.5"	: 12800,
	"13.0"	: 13000,
	"13.5"	: 13200,
	"14.0"	: 13300,
	"14.5"	: 13500,
	"15.0"	: 13700,
	"15.5"	: 13900,
	"16.0"	: 16500,
	"16.5"	: 16700,
	"17.0"	: 16900,
	"17.5"	: 17100,
	"18.0"	: 17300,
	"18.5"	: 17500,
	"19.0"	: 17700,
	"19.5"	: 17900,
	"20.0"	: 18100,
}

let weight_chage_list_ap = {
	"0.5"	: 2500,
	"1.0"	: 2700,
	"1.5"	: 3000,
	"2.0"	: 3300,
	"2.5"	: 3600,
	"3.0"	: 3700,
	"3.5"	: 4000,
	"4.0"	: 4400,
	"4.5"	: 4800,
	"5.0"	: 5200,
	"5.5"	: 5200,
	"6.0"	: 5300,
	"6.5"	: 5500,
	"7.0"	: 5600,
	"7.5"	: 5700,
	"8.0"	: 5900,
	"8.5"	: 6000,
	"9.0"	: 6100,
	"9.5"	: 6200,
	"10.0"	: 6400,
	"10.5"	: 11600,
	"11.0"	: 11800,
	"11.5"	: 12000,
	"12.0"	: 12100,
	"12.5"	: 12300,
	"13.0"	: 12500,
	"13.5"	: 12600,
	"14.0"	: 12800,
	"14.5"	: 13000,
	"15.0"	: 13100,
	"15.5"	: 13300,
	"16.0"	: 13500,
	"16.5"	: 13600,
	"17.0"	: 13800,
	"17.5"	: 14000,
	"18.0"	: 14100,
	"18.5"	: 14300,
	"19.0"	: 14400,
	"19.5"	: 14600,
	"20.0"	: 14800,
}

let weight_chage_list_sa = {
	"0.5" 	: 4700,
	"1.0" 	: 5500,
	"1.5" 	: 6800,
	"2.0" 	: 8100,
	"2.5" 	: 9400,
	"3.0" 	: 10300,
	"3.5" 	: 11500,
	"4.0" 	: 12800,
	"4.5" 	: 14000,
	"5.0" 	: 15200,
	"5.5" 	: 18200,
	"6.0" 	: 18600,
	"6.5" 	: 19000,
	"7.0" 	: 19300,
	"7.5" 	: 19700,
	"8.0" 	: 20100,
	"8.5" 	: 20400,
	"9.0" 	: 20800,
	"9.5" 	: 21200,
	"10.0" 	: 21500,
	"10.5" 	: 39200,
	"11.0" 	: 39900,
	"11.5" 	: 40500,
	"12.0" 	: 41200,
	"12.5" 	: 41900,
	"13.0" 	: 42500,
	"13.5" 	: 43200,
	"14.0" 	: 43900,
	"14.5" 	: 44500,
	"15.0" 	: 45200,
	"15.5" 	: 45800,
	"16.0" 	: 46500,
	"16.5" 	: 47200,
	"17.0" 	: 47800,
	"17.5" 	: 48500,
	"18.0" 	: 49200,
	"18.5" 	: 49800,
	"19.0" 	: 50500,
	"19.5" 	: 51200,
	"20.0" 	: 51800,
}

let weight_chage_list_af = {
	"0.5"	: 4000,
	"1.0"	: 4400,
	"1.5"	: 5400,
	"2.0"	: 6400,
	"2.5"	: 7500,
	"3.0"	: 7700,
	"3.5"	: 8700,
	"4.0"	: 9600,
	"4.5"	: 10600,
	"5.0"	: 11500,
	"5.5"	: 12500,
	"6.0"	: 12800,
	"6.5"	: 13000,
	"7.0"	: 13300,
	"7.5"	: 13500,
	"8.0"	: 13800,
	"8.5"	: 14100,
	"9.0"	: 14300,
	"9.5"	: 14600,
	"10.0"	: 14900,
	"10.5"	: 39100,
	"11.0"	: 39800,
	"11.5"	: 40500,
	"12.0"	: 41200,
	"12.5"	: 41900,
	"13.0"	: 42600,
	"13.5"	: 43300,
	"14.0"	: 43900,
	"14.5"	: 44600,
	"15.0"	: 45300,
	"15.5"	: 46000,
	"16.0"	: 46700,
	"16.5"	: 47400,
	"17.0"	: 48100,
	"17.5"	: 48800,
	"18.0"	: 49500,
	"18.5"	: 50200,
	"19.0"	: 50900,
	"19.5"	: 51500,
	"20.0"	: 52200,
}

$('#purchase_price').inputmask({"alias":"currency","radixPoint":".","prefix":"","removeMaskOnSubmit":true,"digits":0});
$('#selling_price').inputmask({"alias":"currency","radixPoint":".","prefix":"","removeMaskOnSubmit":true,"digits":2});
$('#exchange_market_price').inputmask({"alias":"currency","radixPoint":".","prefix":"","removeMaskOnSubmit":true,"digits":2});
$('#size_v').inputmask({"alias":"currency","radixPoint":".","prefix":"","removeMaskOnSubmit":true,"digits":2});
$('#size_h').inputmask({"alias":"currency","radixPoint":".","prefix":"","removeMaskOnSubmit":true,"digits":2});
$('#size_height').inputmask({"alias":"currency","radixPoint":".","prefix":"","removeMaskOnSubmit":true,"digits":2});

$.each(weight_chage_list_us, function(idx,row){
	$('#size_weight').append($('<option>').attr({value:idx}).text(idx+'kg'));
});
$('#size_weight').select2({
	allowClear: false,
	language: 'ja',
	placeholder: '',
});

$.each(selling_region, function(idx,row){
	$('#selling_region').append($('<option>').attr({value:idx}).text(row));
});
$('#selling_region').select2({
	allowClear: false,
	language: 'ja',
	placeholder: '',
});

$.each(gross_profit_categories,function(idx,row){
	$('#selling_category').append($('<option>').attr({value:row}).text(row));
});
$('#selling_category').select2({
	allowClear: false,
	language: 'ja',
	placeholder: '',
});
$.each(overseas_clearing_fee,function(idx,row){
	$('#overseas_clearing_fee').append($('<option>').attr({value:row.val}).text('前々月の売上が'+row.name + '(' + row.val + '%)'));
});
$('#overseas_clearing_fee').select2({
	allowClear: false,
	language: 'ja',
	placeholder: '',
});

$('input.get_ebay_gross_profit').on("click",function(){
	let p = $('#purchase_price').val() ? Number($('#purchase_price').val().replace(/,/g, '')) : 0;
	let s = $('#selling_price').val() ? Number($('#selling_price').val().replace(/,/g, '')) : 0;
	let e = $('#exchange_market_price').val() ? Number($('#exchange_market_price').val().replace(/,/g, '')) : 150;
	let c = $('select#selling_category').val();
	let o = $('select#overseas_clearing_fee').val();
	
	/* 総手数料 */
	let bf = 0;
	let ocf = 0;
	switch(c){
		case 'Antiques':
		case 'Most Art categories':
		case 'Baby':
		case 'Most Business & Industrial categories':
		case 'Camera, Drone & Photo Accessories (except Memory Cards) / Replacement Parts & Tools / Tripods & Supports / Other Cameras & Photo':
		case 'Most Cameras & Photo categories':
		case 'Cell Phone Accessories (except Memory Cards)':
		case 'Most Cell Phones & Accessories categories':
		case 'Most Clothing, Shoes & Accessories categories':
		case 'Most Coins & Paper Money categories':
		case 'Most Collectibles categories':
		case "Desktops & All-In-Ones / Laptops & Netbooks / Computer Components & Parts / Hard Drives (HDD, SSD & NAS) / Monitors / Printers":
		case "3D Printers & Supplies / Computer Cables & Connectors / Keyboards, Mice & Pointers / Laptop & Desktop Accessories / Other Computers & Networking / Power Protection, Distribution / Tablet & eBook Reader Accs (except Memory Card & USB Adapters)":
		case 'Most Computers/Tablets & Networking categories':
		case "Multipurpose Batteries & Power / Portable Audio Accessories / TV, Video & Home Audio / Vehicle Electronics & GPS / Virtual Reality":
		case 'Most Consumer Electronics categories':
		case 'Crafts':
		case 'Dolls & Bears':
		case 'Entertainment Memorabilia':
		case 'Health & Beauty':
		case 'Home & Garden':
		case 'Car & Truck Parts & Accessories > Wheels, Tires & Parts > Tires':
		case 'Apparel, Protective Gear & Merchandise':
		case 'In-Car Technology, GPS & Security':
		case 'Most eBay Motors':
		case 'Vinyl Records':
		case 'DJ Equipment / Pro Audio Equipment':
		case 'Most Musical Instruments & Gear categories':
		case 'Pet Supplies':
		case 'Pottery & Glass':
		case 'Specialty Services':
		case 'Sporting Goods':
		case 'Most Sports Mem, Cards & Fan Shop categories':
		case 'Stamps':
		case 'Most Toys & Hobbies categories':
		case "Video Game Accessories / Replacement Parts & Tools / Video Games":
		case 'Video Game Consoles':
		case 'Most Video Games & Consoles categories':
			if(s <= 7500){
				bf = s * 0.1325;
			} else {
				bf = 7500 * 0.1325 + (s - 7500) * 0.0235;
			}
			break;
		case 'Art NFTs':
		case 'Emerging NFTs / Non-Sport Trading Card NFTs':
		case 'Movie NFTs':
		case 'Music NFTs':
		case 'Sport Trading Card NFTs':
		case 'CCG NFTs':
			bf = s * 0.05;
			break;
		case 'Books & Magazines':
			if(s <= 7500){
				bf = s * 0.1495;
			} else {
				bf = 7500 * 0.1495 + (s - 7500) * 0.0235;
			}
			break;
		case 'Heavy Equipment / Commercial Printing Presses / Food Trucks, Trailers & Carts':
			if(s <= 15000){
				bf = s * 0.03;
			} else {
				bf = 15000 * 0.03 + (s - 15000) * 0.005;
			}
			break;
		case "Women's Bags & Handbags":
			if(s <= 2000){
				bf = s * 0.15;
			} else {
				bf = s * 0.09;
			}
			break;
		case 'Athletic Shoes':
			if(s <= 100){
				bf = s * 0.1325;
			} else {
				bf = s * 0.08;
			}
			break;
		case 'Bullion':
			if(s <= 7500){
				bf = s * 0.1325;
			} else {
				bf = s * 0.07;
			}
			break;
		case 'Watches, Parts & Accessories':
			if(s <= 1000){
				bf = s * 0.15;
			} else if(s <= 7500){
				bf = 1000 * 0.15 + (s - 1000) * 0.065;
			} else {
				bf = 1000 * 0.15 + 6500 * 0.065 + (s - 7500) * 0.03;
			}
			break;
		case 'Most Jewelry & Watches categories':
			if(s <= 5000){
				bf = s * 0.15;
			} else {
				bf = s * 0.09;
			}
			break;
		case 'Most Movies & TV categories':
		case 'Most Music categories':
			if(s <= 7500){
				bf = s * 0.146;
			} else {
				bf = 7500 * 0.146 + (s - 7500) * 0.0235;
			}
			break;
		case 'Guitars & Basses':
			if(s <= 7500){
				bf = s * 0.0635;
			} else {
				bf = 7500 * 0.0635 + (s - 7500) * 0.0235;
			}
			break;
			break;
			break;
			break;
		default:
			if(s <= 7500){
				bf = s * 0.1325;
			} else {
				bf = 7500 * 0.1325 + (s - 7500) * 0.0235;
			}
			break;
	}
	ocf = s * (o / 100);
	f = (bf + 0.30 + ocf) * (1 + 0.02) * (1.10);
	f = f.toFixed(2);
	$('#total_calculating_fee').val(f);
	
	/* 配送手数料 */
	let sv = $('#size_v').val();
	let sh = $('#size_h').val();
	let she = $('#size_height').val();
	let sw = $('#size_weight').val();
	let w = (sv*sh*she)/5000;
	if(w < sw){
		w = sw;
	}
	if(w<0.5){
		w = 0.5
	} else {
		w = Math.ceil(w);
	}
	if(w >= 20){
		w = 20
	}
	
	let weight_chage_list;
	switch($('#selling_region').val()){
		case 'eu':
			weight_chage_list = weight_chage_list_eu;
			break;
		case 'ap':
			weight_chage_list = weight_chage_list_ap;
			break;
		case 'sa':
			weight_chage_list = weight_chage_list_sa;
			break;
		case 'af':
			weight_chage_list = weight_chage_list_af;
			break;
		default:
			weight_chage_list = weight_chage_list_us;
	}
	
	let dc = delivery_charge_list[w];
	//let dc = $('#size_weight').val();
	let dc2 = weight_chage_list[sw];
	if(dc < dc2){
		dc = dc2;
	}
	$('#delivery_charge').val(dc);
	
	/* 予想価格 */
	let gp = Math.ceil((s-f)*e)-p-dc;
	$('#expected_profit').val(gp);
});	
</script>

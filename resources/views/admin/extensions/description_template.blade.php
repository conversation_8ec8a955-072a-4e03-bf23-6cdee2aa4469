<div class="form-group {!! !$errors->has($label) ?: 'has-error' !!}">
	<div class="row mb-sm-1">
		<div class="col-sm-1">
		</div>
		<div class="col-sm-10">
			<label for="{{$id}}-before" class="control-label">{{$label}}</label>
		</div>
	</div>
	<div class="row mb-sm-1">
		<div class="col-sm-1">
		</div>
		<div class="col-sm-10">
			<select ></select>
		</div>
	</div>

    <div class="row mb-sm-1">
		<div class="col-sm-1">
		</div>
		<div class="col-sm-4">
			<button type="button" id="{{$id}}-on" >翻訳</button>
		</div>
	</div>
	<div class="row mb-sm-1">
		<div class="col-sm-1">
		</div>
		<div class="col-sm-10">
			<textarea class="form-control" id="{{$id}}-after" placeholder="翻訳" {!! $attributes !!} ></textarea>
		</div>
	</div>
<script>
$(function(){
	$('button#{{$id}}-on').on("click",function(){
		let before = $('textarea#{{$id}}-before').val();
		$.ajax({
			type: 'POST',
			url: '/api/translation',
			dataType: 'json',
			contentType: 'application/json',
			data: JSON.stringify({text:before}),
			beforeSend: function(){
				$('button#{{$id}}-on').text('翻訳中...');
				$('button#{{$id}}-on').prop("disabled", true);
			}
		}).done(function(data){
			$('button#{{$id}}-on').text('翻訳');
			$('button#{{$id}}-on').prop("disabled", false);
			$('textarea#{{$id}}-after').val(data.text);
		});
		
	});
});
</script>
</div>
<h4>カテゴリーID検索</h4>
<div class="form-group">
	<label for="search-category" class="col-sm-1 control-label"></label>
	<select id="search-category" class="col-sm-8"></select>
</div>
<div class="form-group">
	<label for="search-category" class="col-sm-2 control-label">結果</label>
	<div class="col-sm-8">
		<div class="input-group">
			<input style="" type="text" id="search-category-result" class="form-control" placeholder="カテゴリーID検索結果">
		</div>
	</div>
</div>

<script>
$.ajax({
	type: 'POST',
	url: '/api/categories',
	dataType: 'json',
	contentType: 'application/json',
}).done(function(data){
	if(data.result == 'success'){
		$.each(data.data, function(idx,row){
			$('#search-category').append($('<option>').attr({value:row.id}).text(row.parent + '/' + row.name));
		});
		$('#search-category').select2({
			
		});
	}
});

$('#search-category').on('change',function(){
	let val = $(this).val();
	if(val){
		$('#search-category-result').val(val);
	}
});
</script>

<hr />
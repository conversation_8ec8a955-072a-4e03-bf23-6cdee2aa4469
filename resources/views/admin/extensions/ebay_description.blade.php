<h4>{{$label}}</h4>
<div class="form-group  ">
    <label for="ebay_description_id" class="col-sm-1 control-label"></label>
    <div class="col-sm-8">
        <div class="input-group">
			<span class="input-group-addon"><i class="fa fa-pencil fa-fw"></i></span>
            <input type="text" id="ebay_description_id" value="" class="form-control ebay_description_id" placeholder="ITEM IDもしくは出品URL">
        </div>
	@include('admin::form.help-block')
    </div>
</div>

<div class="form-group ">
    <label class="col-sm-1 control-label"></label>
    <div class="col-sm-8">
        <input type="button" value="商品詳細取得" class="btn get_ebay_description">
    </div>
</div>
<script>
$('.get_ebay_description').on("click",function(){
	refreshUserToken().then((token) => {
		$.post('/api/ebay_item/',{
			id: $('input#ebay_description_id').val(),
			token: token,
		}, function(data) {
			console.log(data);
			if (data.result == 'success') {
				$('#category_id').val(data.data.category_id);
				$('#display_title').val(data.data.title);
				appendAspects(data.data.category_id,token,$('select[name=delivery_source_area]').val(),0,data.data.localized_aspects);
			} else {
				alert('eBayからの情報取得に失敗しました。\n' + data.data.errors[0].message);
			}
		});
	}).catch((error) => {
		alert('eBayからの情報取得に失敗しました。eBayとの認証を再実行してください。');
	});
});
</script>


<hr />

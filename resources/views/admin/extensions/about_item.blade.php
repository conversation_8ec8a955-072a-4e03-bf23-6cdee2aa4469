
<h4>商品詳細</h4>
<hr />
<div class="form-group {!! !$errors->has($label) ?: 'has-error' !!}">

	<label for="category_id" class="category_id col-sm-2 control-label asterisk">カテゴリーID</label>

    <div class="col-sm-8">

        @include('admin::form.error')

        <div class="input-group">
			<span class="input-group-addon"><i class="fa fa-pencil fa-fw"></i></span>
            <input required="1" style="width: 130px; text-align: right;" type="text" id="category_id" name="form_category_id" value="" class="form-control category_id" placeholder="入力 カテゴリーID" required>
        </div>

        @include('admin::form.help-block')

    </div>
</div>

<div class="form-group {!! !$errors->has($label) ?: 'has-error' !!} condition">
	<label for="condition" class="condition col-sm-2 control-label">コンディション</label>
    <div class="col-sm-8">
        @include('admin::form.error')
        <div class="input-group">
            <select id="condition" class="form-control condition" name="form_condition"></select>
			<span class="help-block">
				<i class="fa fa-info-circle"></i>&nbsp;コンディションの選択肢はカテゴリーに応じて異なるため、画面右上のeBay「商品情報取得」ボタンを押した後に選択肢が表示されます
			</span>
        </div>
    </div>
</div>

<div class="form-group {!! !$errors->has($label) ?: 'has-error' !!} tcg-condition">
	<label for="condition" class="condition col-sm-2 control-label">コンディション(追加情報)</label>
    <div class="col-sm-8">
        @include('admin::form.error')
        <div class="input-group" id="additional-conditions">
            
        </div>
    </div>
</div>

<script>
/* 編集時 */
if($('input[name=category_id]').val()){
	$('#category_id').val($('input[name=category_id]').val());
	let category_id = $('input[name=category_id]').val();
	let condition = $('input[name=condition]').val();
	let aspects = JSON.parse( $('input[name=default_form_aspects]').val() ? $('input[name=default_form_aspects]').val() : "{}");
	refreshUserToken().then((token)=>{
		appendAspects(category_id, token,'EBAY_US', condition, aspects);
	}).catch((error) => {
		alert('eBayからの情報取得に失敗しました。eBayとの認証を再実行してください。');
	});
}

$('input[name=form_category_id]').on('change', function(){
	let category_id = $(this).val();
	refreshUserToken().then((token)=>{
		appendAspects(category_id, token,'EBAY_US');
	}).catch((error) => {
		alert('eBayからの情報取得に失敗しました。eBayとの認証を再実行してください。');
	});
});
</script>

<style>
.tcg-condition {
	display:none;
}
</style>

<hr />
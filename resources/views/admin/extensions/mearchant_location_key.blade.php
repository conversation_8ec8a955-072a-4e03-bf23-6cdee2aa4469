<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>{{config('admin.title')}} | {{ trans('admin.mearchant_location_key') }}</title>
	<!-- Tell the browser to be responsive to screen width -->
	<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
	
	@if(!is_null($favicon = Admin::favicon()))
	<link rel="shortcut icon" href="{{$favicon}}">
	@endif

	<!-- Bootstrap 3.3.5 -->
	<link rel="stylesheet" href="{{ admin_asset('vendor/laravel-admin/AdminLTE/bootstrap/css/bootstrap.min.css') }}">
	<link rel="stylesheet" href="{{ admin_asset('vendor/laravel-admin/font-awesome/css/font-awesome.min.css') }}">
	<link rel="stylesheet" href="{{ admin_asset('vendor/laravel-admin/AdminLTE/dist/css/AdminLTE.min.css') }}">
	<link rel="stylesheet" href="{{ admin_asset('vendor/laravel-admin/laravel-admin/laravel-admin.css') }}">
	<link rel="stylesheet" href="{{ admin_asset('vendor/laravel-admin/AdminLTE/plugins/iCheck/square/blue.css') }}">
	<link rel="stylesheet" href="{{ admin_asset('vendor/laravel-admin/AdminLTE/plugins/select2/select2.min.css') }}">

	<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
	<script src="//oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
	<script src="//oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
	<![endif]-->
<style>
.content-wrapper, .right-side, .main-footer {
	margin-left: 0;
}

</style></head>
<body class="hold-transition login-page" @if(config('admin.login_background_image'))style="background: url({{config('admin.login_background_image')}}) no-repeat;background-size: cover;"@endif>
<div class="wrapper">
	<div class="content-wrapper" id="pjax-container">
		<div id="app">
			<section class="content">
				<div class="row"><div class="col-md-12"><div class="box box-info">
					<div class="box-body">
						<form>
						@csrf
							<div class="fields-group">
								<div class="form-group col-sm-12">
									<label class="col-sm-2  control-label">{{ __('admin.mearchant_location_keys') }}</label>
									<div class="col-sm-8">
										<select class="form-control " name="mearchnat_location_keys"></select>
									</div>
								</div>
								<div class="form-group col-sm-12">
									<label class="col-sm-2 asterisk control-label">{{ __('admin.mearchant_location_key') }}</label>
									<div class="col-sm-8">
										<label class="control-label error location" for="inputError"><i class="fa fa-times-circle-o"></i> {{ __('admin.mearchant_location_key') }}を入力してください。</label><br/>
										<div class="input-group">
											<span class="input-group-addon"><i class="fa fa-pencil fa-fw"></i></span>
											<input type="text" id="mearchant_location_key" name="mearchant_location_key" value="" class="form-control mearchant_location_key" maxlength="36" pattern="^[0-9A-Za-z]+$" placeholder="入力 {{ __('admin.mearchant_location_key') }}" required>
										</div>
										<span class="help-block">
											<i class="fa fa-info-circle"></i>&nbsp;Tokyo など半角英文字36文字まで
										</span>
									</div>
								</div>
								<div class="form-group col-sm-12">
									<label class="col-sm-2 asterisk control-label">{{ __('admin.mearchant_location_country') }}</label>
									<div class="col-sm-8">
										<label class="control-label error country" for="inputError"><i class="fa fa-times-circle-o"></i> 国コードを選択してください。</label><br/>
										<select class="form-control " name="country" required></select>
									</div>
								</div>
								<div class="form-group col-sm-12">
									<label class="col-sm-2 asterisk control-label">{{ __('admin.mearchant_location_city') }}</label>
									<div class="col-sm-8">
										<label class="control-label error city" for="inputError"><i class="fa fa-times-circle-o"></i> 地域を入力してください。</label><br/>
										<div class="input-group">
											<span class="input-group-addon"><i class="fa fa-pencil fa-fw"></i></span>
											<input type="text" id="city" name="city" value="" class="form-control city" placeholder="入力 {{ __('admin.mearchant_location_city') }}" required>
										</div>
										<span class="help-block">
											<i class="fa fa-info-circle"></i>&nbsp;Tokyo/Osaka/Nagoya など英文字45文字まで
										</span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="box-footer">
						<div class="col-md-8">
							<div class="btn-group pull-right">
								<button type="button" class="btn btn-primary" name="submit-button">保存</button>
							</div>
						</div>
					</div>
				</div></div></div>
			</section>
		</div>
	</div>
</div>
</body>
<!-- jQuery 2.1.4 -->
<script src="{{ admin_asset('vendor/laravel-admin/AdminLTE/plugins/jQuery/jQuery-2.1.4.min.js')}}"></script>
<!-- Bootstrap 3.3.5 -->
<script src="{{ admin_asset('vendor/laravel-admin/AdminLTE/bootstrap/js/bootstrap.min.js')}}"></script>
<!-- iCheck -->
<script src="{{ admin_asset('vendor/laravel-admin/AdminLTE/plugins/iCheck/icheck.min.js')}}"></script>

<script src="/vendor/laravel-admin/AdminLTE/plugins/select2/select2.full.min.js"></script>
<script>
	$(function () {
		$('input').iCheck({
			checkboxClass: 'icheckbox_square-blue',
			radioClass: 'iradio_square-blue',
			increaseArea: '20%' // optional
		});
	});
	
var countryCodes = {
	'アイスランド' : 'IS', 
	'アイルランド' : 'IE', 
	'アゼルバイジャン' : 'AZ', 
	'アフガニスタン' : 'AF', 
	'アメリカ合衆国' : 'US', 
	'アメリカ領ヴァージン諸島' : 'VI', 
	'アメリカ領サモア' : 'AS', 
	'アラブ首長国連邦' : 'AE', 
	'アルジェリア' : 'DZ', 
	'アルゼンチン' : 'AR', 
	'アルバ' :     'AW', 
	'アルバニア' : 'AL', 
	'アルメニア' :     'AM', 
	'アンギラ' :     'AI', 
	'アンゴラ' :     'AO', 
	'アンティグア・バーブーダ' : 'AG', 
	'アンドラ' :     'AD', 
	'イエメン' :     'YE', 
	'イギリス' :     'GB', 
	'イギリス領インド洋地域' : 'IO', 
	'イギリス領ヴァージン諸島' : 'VG', 
	'イスラエル' :     'IL', 
	'イタリア' :     'IT', 
	'イラク' :     'IQ', 
	'イラン|イラン・イスラム共和国' : 'IR', 
	'インド' :     'IN', 
	'インドネシア' : 'ID', 
	'ウォリス・フツナ' : 'WF', 
	'ウガンダ' :     'UG', 
	'ウクライナ' :     'UA', 
	'ウズベキスタン' : 'UZ', 
	'ウルグアイ' :     'UY', 
	'エクアドル' :     'EC', 
	'エジプト' :     'EG', 
	'エストニア' :     'EE', 
	'エチオピア' :     'ET', 
	'エリトリア' :     'ER', 
	'エルサルバドル' : 'SV', 
	'オーストラリア' : 'AU', 
	'オーストリア' : 'AT', 
	'オーランド諸島' : 'AX', 
	'オマーン' :     'OM', 
	'オランダ' :     'NL', 
	'ガーナ' :     'GH', 
	'カーボベルデ' : 'CV', 
	'ガーンジー' : 'GG', 
	'ガイアナ' :     'GY', 
	'カザフスタン' : 'KZ', 
	'カタール' :     'QA', 
	'合衆国領有小離島' : 'UM', 
	'カナダ' :     'CA', 
	'ガボン' :     'GA', 
	'カメルーン' :     'CM', 
	'ガンビア' :     'GM', 
	'カンボジア' : 'KH', 
	'北マリアナ諸島' : 'MP', 
	'ギニア' :     'GN', 
	'ギニアビサウ' : 'GW', 
	'キプロス' :     'CY', 
	'キューバ' :     'CU', 
	'キュラソー島|キュラソー' : 'CW', 
	'ギリシャ' :     'GR', 
	'キリバス' :     'KI', 
	'キルギス' :     'KG', 
	'グアテマラ' :     'GT', 
	'グアドループ' : 'GP', 
	'グアム' :     'GU', 
	'クウェート' :     'KW', 
	'クック諸島' : 'CK', 
	'グリーンランド' : 'GL', 
	'クリスマス島 (オーストラリア)|クリスマス島' : 'CX', 
	'グルジア' :     'GE', 
	'グレナダ' :     'GD', 
	'クロアチア' :     'HR', 
	'ケイマン諸島' : 'KY', 
	'ケニア' :     'KE', 
	'コートジボワール' : 'CI', 
	'ココス諸島|ココス（キーリング）諸島' : 'CC', 
	'コスタリカ' :     'CR', 
	'コモロ' :     'KM', 
	'コロンビア' :     'CO', 
	'コンゴ共和国' : 'CG', 
	'コンゴ民主共和国' : 'CD', 
	'サウジアラビア' : 'SA', 
	'サウスジョージア・サウスサンドウィッチ諸島' : 'GS', 
	'サモア' :     'WS', 
	'サントメ・プリンシペ' : 'ST', 
	'サン・バルテルミー島|サン・バルテルミー' : 'BL', 
	'ザンビア' :     'ZM', 
	'サンピエール島・ミクロン島' : 'PM', 
	'サンマリノ' :     'SM', 
	'サン・マルタン (西インド諸島)|サン・マルタン（フランス領）' : 'MF', 
	'シエラレオネ' : 'SL', 
	'ジブチ' :     'DJ', 
	'ジブラルタル' : 'GI', 
	'ジャージー' : 'JE', 
	'ジャマイカ' :     'JM', 
	'シリア|シリア・アラブ共和国' : 'SY', 
	'シンガポール' : 'SG', 
	'シント・マールテン|シント・マールテン（オランダ領）' : 'SX', 
	'ジンバブエ' : 'ZW', 
	'スイス' :     'CH', 
	'スウェーデン' : 'SE', 
	'スーダン' :     'SD', 
	'スヴァールバル諸島およびヤンマイエン島' : 'SJ', 
	'スペイン' :     'ES', 
	'スリナム' :     'SR', 
	'スリランカ' :     'LK', 
	'スロバキア' : 'SK', 
	'スロベニア' :     'SI', 
	'スワジランド' : 'SZ', 
	'セーシェル' : 'SC', 
	'赤道ギニア' : 'GQ', 
	'セネガル' :     'SN', 
	'セルビア' :     'RS', 
	'セントクリストファー・ネイビス' : 'KN', 
	'セントビンセント・グレナディーン|セントビンセントおよびグレナディーン諸島' : 'VC', 
	'セントヘレナ・アセンションおよびトリスタンダクーニャ' : 'SH', 
	'セントルシア' : 'LC', 
	'ソマリア' :     'SO', 
	'ソロモン諸島' : 'SB', 
	'タークス・カイコス諸島' : 'TC', 
	'タイ王国|タイ' : 'TH', 
	'大韓民国' : 'KR', 
	'台湾' :     'TW', 
	'タジキスタン' : 'TJ', 
	'タンザニア' :     'TZ', 
	'チェコ' :     'CZ', 
	'チャド' :     'TD', 
	'中央アフリカ共和国' : 'CF', 
	'中華人民共和国|中国' : 'CN', 
	'チュニジア' :     'TN', 
	'朝鮮民主主義人民共和国' : 'KP', 
	'チリ' :         'CL', 
	'ツバル' :     'TV', 
	'デンマーク' :     'DK', 
	'ドイツ' :     'DE', 
	'トーゴ' :     'TG', 
	'トケラウ' :     'TK', 
	'ドミニカ共和国' : 'DO', 
	'ドミニカ国' :     'DM', 
	'トリニダード・トバゴ' : 'TT', 
	'トルクメニスタン' : 'TM', 
	'トルコ' :     'TR', 
	'トンガ' :     'TO', 
	'ナイジェリア' : 'NG', 
	'ナウル' :     'NR', 
	'ナミビア' :     'NA', 
	'南極' :     'AQ', 
	'ニウエ' :     'NU', 
	'ニカラグア' :     'NI', 
	'ニジェール' :     'NE', 
	'日本' :     'JP', 
	'西サハラ' :     'EH', 
	'ニューカレドニア' : 'NC', 
	'ニュージーランド' : 'NZ', 
	'ネパール' :     'NP', 
	'ノーフォーク島' : 'NF', 
	'ノルウェー' :     'NO', 
	'ハード島とマクドナルド諸島' : 'HM', 
	'バーレーン' : 'BH', 
	'ハイチ' :     'HT', 
	'パキスタン' :     'PK', 
	'バチカン|バチカン市国' : 'VA', 
	'パナマ' :     'PA', 
	'バヌアツ' :     'VU', 
	'バハマ' :     'BS', 
	'パプアニューギニア' : 'PG', 
	'バミューダ諸島|バミューダ' : 'BM', 
	'パラオ' :     'PW', 
	'パラグアイ' :     'PY', 
	'バルバドス' : 'BB', 
	'パレスチナ' : 'PS', 
	'ハンガリー' :     'HU', 
	'バングラデシュ' : 'BD', 
	'東ティモール' : 'TL', 
	'ピトケアン諸島|ピトケアン' : 'PN', 
	'フィジー' :     'FJ', 
	'フィリピン' :     'PH', 
	'フィンランド' : 'FI', 
	'ブータン' :     'BT', 
	'ブーベ島' :     'BV', 
	'プエルトリコ' : 'PR', 
	'フェロー諸島' : 'FO', 
	'フォークランド諸島|フォークランド（マルビナス）諸島' : 'FK', 
	'ブラジル' :     'BR', 
	'フランス' :     'FR', 
	'フランス領ギアナ' : 'GF', 
	'フランス領ポリネシア' : 'PF', 
	'フランス領南方・南極地域' : 'TF', 
	'ブルガリア' :     'BG', 
	'ブルキナファソ' : 'BF', 
	'ブルネイ|ブルネイ・ダルサラーム' : 'BN', 
	'ブルンジ' :     'BI', 
	'ベトナム' :     'VN', 
	'ベナン' :     'BJ', 
	'ベネズエラ|ベネズエラ・ボリバル共和国' : 'VE', 
	'ベラルーシ' : 'BY', 
	'ベリーズ' :     'BZ', 
	'ペルー' :     'PE', 
	'ベルギー' :     'BE', 
	'ポーランド' :     'PL', 
	'ボスニア・ヘルツェゴビナ' : 'BA', 
	'ボツワナ' :     'BW', 
	'BES諸島|ボネール、シント・ユースタティウスおよびサバ' : 'BQ', 
	'ボリビア|ボリビア多民族国' : 'BO', 
	'ポルトガル' : 'PT', 
	'香港' :     'HK', 
	'ホンジュラス' : 'HN', 
	'マーシャル諸島' : 'MH', 
	'マカオ' :     'MO', 
	'マケドニア共和国|マケドニア旧ユーゴスラビア共和国' : 'MK', 
	'マダガスカル' : 'MG', 
	'マヨット' :     'YT', 
	'マラウイ' :     'MW', 
	'マリ共和国|マリ' : 'ML', 
	'マルタ' :     'MT', 
	'マルティニーク' : 'MQ', 
	'マレーシア' : 'MY', 
	'マン島' :     'IM', 
	'ミクロネシア連邦' : 'FM', 
	'南アフリカ共和国|南アフリカ' : 'ZA', 
	'南スーダン' : 'SS', 
	'ミャンマー' :     'MM', 
	'メキシコ' :     'MX', 
	'モーリシャス' : 'MU', 
	'モーリタニア' : 'MR', 
	'モザンビーク' : 'MZ', 
	'モナコ' :     'MC', 
	'モルディブ' :     'MV', 
	'モルドバ|モルドバ共和国' : 'MD', 
	'モロッコ' :     'MA', 
	'モンゴル国|モンゴル' : 'MN', 
	'モンテネグロ' : 'ME', 
	'モントセラト' : 'MS', 
	'ヨルダン' :     'JO', 
	'ラオス|ラオス人民民主共和国' : 'LA', 
	'ラトビア' :     'LV', 
	'リトアニア' :     'LT', 
	'リビア' :     'LY', 
	'リヒテンシュタイン' : 'LI', 
	'リベリア' :     'LR', 
	'ルーマニア' : 'RO', 
	'ルクセンブルク' : 'LU', 
	'ルワンダ' :     'RW', 
	'レソト' :     'LS', 
	'レバノン' :     'LB', 
	'レユニオン' :     'RE', 
	'ロシア|ロシア連邦' : 'RU', 

};

var details = {};
$(function(){
    
	$('label.error').hide();
	$.each(countryCodes,function(idx,row){
		let opt = $('<option>').attr({value: row}).text(idx + '('+row+')');
		if(row == 'JP'){
			opt.attr('selected','selected');
		}
		$('select[name=country]').append(opt);
	});
	
	$('select[name=country]').select2({"allowClear":false});
	
	$.ajax({
		type: 'GET',
		url: '/admin/exhibits/merchant_location_keys',
	}).done(function(result){
		$.each(result.data,function(idx,row){
			if(idx == ""){
				row = "新地域ポリシー作成";
			}
			$('select[name=mearchnat_location_keys]').append($('<option>').attr({value: idx}).text(row));
		});
		$('select[name=mearchnat_location_keys]').select2({"allowClear":false});
		
		details = result.details;
	});

	$('input[name=mearchant_location_key]').on("input", function(){
		let str = this.value;
		while(str.match(/[^A-Z^a-z\d]/)){
			str=str.replace(/[^A-Z^a-z\d]/,"");
		}
		this.value = str;
	});
	$('input[name=city]').on("input", function(){
		let str = this.value;
		while(str.match(/[^A-Z^a-z\d\s\-,`+#$%&.]/)){
			str=str.replace(/[^A-Z^a-z\d\s\-,`+#$%&.]/,"");
		}
		this.value = str;
	});
		
	$('select[name=mearchnat_location_keys]').on("change",function(){
		if($(this).val() != ""){
			$('input[name=mearchant_location_key]').attr({'disabled':true});
			$('input[name=mearchant_location_key]').val($(this).val());
			
			$('select[name=country]').val(details[$(this).val()]["country"]).trigger('change');
			$('input[name=city]').val(details[$(this).val()]["city"]);
			
			$('select[name=country]').attr({'disabled':true});
			$('input[name=city]').attr({'disabled':true});
			
			$('button[name=submit-button]').attr({'disabled':true});
		} else {
			$('input[name=mearchant_location_key]').attr({'disabled':false});
			$('input[name=mearchant_location_key]').val('');
			$('select[name=country]').attr({'disabled':false});
			$('input[name=city]').attr({'disabled':false});
			$('button[name=submit-button]').attr({'disabled':false});
		}
	});
	
	$('button[name=submit-button]').on('click',function(){
		let key = $('input[name=mearchant_location_key]').val();
		let cd = $('select[name=country]').val();
		let city = $('input[name=city]').val();
		let is_error = false;
		if(!key) {
			$('label.error.location').show();
			is_error = true;
		} else {
			$('label.error.location').hide();
		}
		if(!cd){
			$('label.error.country').show();
			is_error = true;
		} else {
			$('label.error.country').hide();
		}
		if(!city){
			$('label.error.city').show();
			is_error = true;
		} else {
			$('label.error.city').hide();
		}
		
		if(is_error){
			return;
		}
		
		$.ajax({
			type: 'POST',
			url: '/admin/exhibits/merchant_location_key/'+key,
			data: {
				country: cd,
				city: city,
			},
			headers: {
				'X-CSRF-TOKEN': $('input[name=_token]').val(),
			}
		}).done(function(result){
			document.location.reload()
		}).fail(function(){
			alert("登録に失敗しました");
		});
	});
});
</script>

</body>
</html>

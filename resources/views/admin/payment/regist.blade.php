@extends('admin.layout.payment')

@section('content')
<div style="width:100%; text-align: center;">

有料会員登録を行います。<br />
<br />
「購入する」を押下して、クレジットカード情報を入力してください。<br />
</div>
<div style="width:100%; text-align: center;">
    <form id="mainform" action="/admin/payment" method="post">
@csrf
        <!-- input要素としてtknの追加をします -->
        <input id="tkn" name="tkn" type="hidden" value="">
        <!-- ポップアップ表示用の要素としてCARD_INPUT_FORMを追加をします -->
        <div id="CARD_INPUT_FORM"></div>
        <!-- 3Dセキュアポップアップ表示用としてEMV3DS_INPUT_FORMの追加をします -->
        <div id="EMV3DS_INPUT_FORM"></div>
        <input type="button" value="購入する" onclick="doPurchase()" />
    </form>
</div>

<div style="width:100%; text-align: center;">
またカード情報ご登録後（＝購入後）1ヶ月間は無料お試し期間になります。 <br />
ご登録いただいたクレジットカードでの決済は１ヶ月後から開始されます。<br />
それ以降については一ヶ月毎の自動更新となります。
</div>

<script>

window.resizeTo(700, 800);



/**
 * ３．決済処理の実行
 * 決済実行用の関数を準備します。
 * 引数はresultCode, errMsgの2つを受け取れるようにします。
 */
function execPurchase(resultCode, errMsg) {
    if (resultCode != "Success") {
        // 戻り値がSuccess以外の場合はエラーメッセージを表示します
        window.alert(errMsg);
    } else {
      //  window.alert("PAYMENT SUCCESS!!");
      // 加盟店様サーバーに決済リクエストを実行する処理の実装をします。
        // 以下はサンプルです。
        $("#mainform").submit();
    }
}
/**
 * ２．3Dセキュア2.0の認証を実行
 * 3Dセキュア2認証用関数の準備をします。
 * 引数はresultCode, errMsgの2つを受け取れるようします。
 */
function execAuth(resultCode, errMsg) {
    if (resultCode != "Success") {
        // 戻り値がSuccess以外の場合はエラーメッセージを表示します
        window.alert(errMsg);
    } else {
        // 3Dセキュア2.0認証処理（商品登録なし）を呼び出します。
        ThreeDSAdapter.authenticate({
            tkn: $("#tkn").val(), // トークン作成後にtkn要素に値が入力されます
            aid: '128543', // 店舗IDを設定します。
            iid: 'ebayproduct01',   // 商品IDを設定
        }, execPurchase);  // 決済実行用の関数をコールバックにセットします。
    }
}
/**
 * １．クレジットカードトークンの作成
 * トークン作成用の関数を準備します。
 */
function doPurchase() {
    // トークン作成処理を呼び出します。
    CPToken.CardInfo({
        aid: '128543' // 店舗IDを設定します。
    }, execAuth); // 3Dセキュア2.0認証用関数をコールバックにセットします。
}



/**
 * ２．決済処理の実行
 * 決済実行用の関数を準備します。
 * 引数はresultCode, errMsgの2つを受け取れるようにします。
 */
/*function execPurchase(resultCode, errMsg) {
    if (resultCode != "Success") {
        // 戻り値がSuccess以外の場合はエラーメッセージを表示します
        window.alert(errMsg);
    } else {
      //  window.alert("PAYMENT SUCCESS!!");
      // 加盟店様サーバーに決済リクエストを実行する処理の実装をします。
        $("#mainform").submit();
    }
}*/

/**
 * １．クレジットカードトークンの作成
 * トークン作成用の関数を準備します。
 */
/*function doPurchase() {
    // トークン作成処理を呼び出します。
    CPToken.CardInfo({
        aid: '128543' // 店舗IDを設定します。
    }, execPurchase);
}*/
</script>
@endsection

@extends('admin.layout.app')

@section('content')

<div class="container">
	<div class="row">
		<div class="col-md-8 col-md-offset-2">
			<div class="panel panel-default">
				<div class="panel-heading" style="text-align: center;font-size: x-large;font-weight: bold;">ログイン</div>
				<div class="panel-body">
					<form class="form-horizontal" role="form" method="POST" action="{{ admin_url('auth/login') }}">
					{{ csrf_field() }}
						<div class="mb-3 text-center">
							<img src='/images/logo-transparent.png' style="width:50%;">
						</div>
						<div class="input-group mb-3 mb-3 form-group{{ $errors->has('username') ? ' has-error' : '' }}">
							<label for="username" class="col-md-4 control-label">ユーザID（メールアドレス）</label>
							<div class="col-md-6">
								<input id="username" type="username" class="form-control" placeholder="{{ trans('admin.username') }}" name="username" value="{{ old('username') }}" required>

								@if ($errors->has('username'))
									@foreach($errors->get('username') as $message)
										<span class="help-block">
											<strong>{{ $message }}</strong>
										</span>
									@endforeach
									
								@endif
							</div>
						</div>
						
						<div class="input-group mb-3 mb-3 form-group{{ $errors->has('password') ? ' has-error' : '' }}">
							<label for="password" class="col-md-4 control-label">パスワード</label>
							<div class="col-md-6">
								<input id="password" type="password" class="form-control" placeholder="{{ trans('admin.password') }}" name="password" value="{{ old('password') }}" required>

								@if ($errors->has('password'))
									@foreach($errors->get('password') as $message)
									<span class="help-block">
										<strong>{{ $message }}</strong>
									</span>
									@endforeach
									
								@endif
							</div>

						</div>
						<div class="input-group mb-3 mb-3 form-group">
							<label class="col-md-4 control-label"></label>
							<div class="form-check mb-3 form-group">
								@if(config('admin.auth.remember'))
								<label>
									<input type="checkbox" name="remember" value="1" {{ (!old('username') || old('remember')) ? 'checked' : '' }}>
									{{ trans('admin.remember_me') }}
								</label>
								@endif
							</div>
						</div>
						
						<div class="form-group">
							<div class="col-md-6 col-md-offset-4">
								<button type="submit" class="btn" style="background-color: #49CABD;color: white;font-weight: bold;">
									ログインする
								</button>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-md-offset-4">
								<label>
									パスワードを忘れた方は<a href="{{ route('contact.index') }}">こちら</a>
								</label>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-md-offset-4">
								<label>
									<a href="/users/register">新規登録はこちら</a>
								</label>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6 col-md-offset-4">
								<label>
									サービス詳細は<a target="_blank" href="https://saocha-pro.hp.peraichi.com">こちら</a>
								</label>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

@endsection

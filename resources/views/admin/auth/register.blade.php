@extends('admin.layout.app')

@section('content')
<div class="container">
	<div class="row">
		<div class="col-md-8 col-md-offset-2">
			<div class="panel panel-default">
				<div class="panel-heading" style="text-align: center;font-size: x-large;font-weight: bold;">新規登録</div>
				<div class="panel-body">
					<form class="form-horizontal" role="form" method="POST" action="{{ route('user.post.register') }}">
						{{ csrf_field() }}

						<div class="input-group mb-3 mb-3 form-group{{ $errors->has('email') ? ' has-error' : '' }}">
							<label for="email" class="col-md-4 control-label">メールアドレス</label>

							<div class="col-md-6">
								<input id="email" type="email" class="form-control" name="email" value="{{ old('email') }}" required>

								@if ($errors->has('email'))
									<span class="help-block">
										<strong>{{ $errors->first('email') }}</strong>
									</span>
								@endif
							</div>
						</div>
						<div class="input-group mb-3 form-group{{ $errors->has('name') ? ' has-error' : '' }}">
							<label for="name" class="col-md-4 control-label">名前</label>

							<div class="col-md-6">
								<input id="name" type="text" class="form-control" name="name" value="{{ old('name') }}" autofocus required>

								@if ($errors->has('name'))
									<span class="help-block">
										<strong>{{ $errors->first('name') }}</strong>
									</span>
								@endif
							</div>
						</div>

						<div class="input-group mb-3 form-group{{ $errors->has('password') ? ' has-error' : '' }}">
							<label for="password" class="col-md-4 control-label">パスワード</label>

							<div class="col-md-6">
								<input id="password" type="password" class="form-control" name="password" required>

								@if ($errors->has('password'))
									<span class="help-block">
										<strong>{{ $errors->first('password') }}</strong>
									</span>
								@endif
							</div>
						</div>

						<div class="input-group mb-3 form-group{{ $errors->has('password_confirmation') ? ' has-error' : '' }}">
							<label for="password-confirm" class="col-md-4 control-label">パスワード(確認用)</label>
							<div class="col-md-6">
								<input id="password-confirm" type="password" class="form-control" name="password_confirmation" required>
								@if ($errors->has('password_confirmation'))
									<span class="help-block">
										<strong>{{ $errors->first('password_confirmation') }}</strong>
									</span>
								@endif
							</div>
						</div>
						
						<div class="form-check mb-3 form-group{{ $errors->has('terms') ? ' has-error' : '' }}">
							<label class="col-md-4 control-label"></label>
							<div class="col-md-6">
								<input id="terms" type="checkbox" class="form-check-input" name="terms" value="" required>
								<label for="terms" class="form-check-label"><a href="/terms_of_service" target="_blank">利用規約</a>に同意する</label>
							</div>
						</div>

						<div class="form-group">
							<div class="col-md-6 col-md-offset-4">
								<button type="submit" class="btn" style="background-color: #49CABD;color: white;font-weight: bold;">
									登録する
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const login = document.querySelector('#navbarSupportedContent > ul.navbar-nav.ms-auto > li:nth-child(1) > a');
	const register = document.querySelector('#navbarSupportedContent > ul.navbar-nav.ms-auto > li:nth-child(2) > a');
    if (login) {
        login.style.backgroundColor = '#49CABD'; 
    }
    if (register) {
        register.style.backgroundColor = '#E6E5E5'; 
    }
});
</script>
@endsection

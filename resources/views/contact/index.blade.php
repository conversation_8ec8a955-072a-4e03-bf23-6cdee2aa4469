@extends('admin.layout.app')

@section('content')
<script>
function getUrlQueryParam(name, url) {
    if (!url) url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
        results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, " "));
}
window.onload = (event) =>{
	let email = getUrlQueryParam('email');
	if(email){
		let elem = document.querySelector('input[name="email"]');
		elem.value = email;
		const url = new URL(window.location.href);
		history.replaceState(null,'',url.pathname);
	}
}
</script>
<div class="container">
	<div class="row">
		<div class="col-md-8 col-md-offset-2">
			<div class="panel panel-default">
				<div class="panel-heading">お問い合わせ</div>
				<div class="panel-body">
					<form class="form-horizontal" role="form" method="POST" action="{{ route('contact.confirm') }}">
					{{ csrf_field() }}
						<div class="input-group mb-3 mb-3 form-group{{ $errors->has('email') ? ' has-error' : '' }}">
							<label for="email" class="col-md-4 control-label">メールアドレス</label>

							<div class="col-md-6">
								<input id="email" type="email" class="form-control" name="email" value="{{ old('email') }}" required>

								@if ($errors->has('email'))
									<span class="help-block">
										<strong>{{ $errors->first('email') }}</strong>
									</span>
								@endif
							</div>
						</div>
						<div class="input-group mb-3 form-group{{ $errors->has('title') ? ' has-error' : '' }}">
							<label for="title" class="col-md-4 control-label">ご要件</label>

							<div class="col-md-6">
								<select id="title" name="title"  class="form-select" aria-label="Default select example" required>
									<option value="">-</option>
									<option type="text" @if("資料請求" == old('title')) selected @endif value="資料請求">資料請求</option>
									<option type="text" @if("登録について" == old('title')) selected @endif value="登録について">登録について</option>
									<option type="text" @if("ログインについて" == old('title')) selected @endif value="ログインについて">ログインについて</option>
									<option type="text" @if("ご利用について" == old('title')) selected @endif value="ご利用について">ご利用について</option>
									<option type="text" @if("退会したい" == old('title')) selected @endif value="退会したい">退会したい</option>
									<option type="text" @if("その他" == old('title')) selected @endif value="その他">その他</option>
								</select>

								@if ($errors->has('title'))
									<span class="help-block">
										<strong>{{ $errors->first('title') }}</strong>
									</span>
								@endif
							</div>
						</div>
						<div class="input-group mb-3 form-group{{ $errors->has('body') ? ' has-error' : '' }}">
							<label for="body" class="col-md-4 control-label">お問い合わせ内容</label>

							<div class="col-md-6">
								<textarea id="body" class="form-control" rows="3" name="body" required>{{ old('body') }}</textarea>

								@if ($errors->has('body'))
									<span class="help-block">
										<strong>{{ $errors->first('body') }}</strong>
									</span>
								@endif
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-6 col-md-offset-4">
								<button type="submit" class="btn btn-primary">
									確認画面へ進む
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
@endsection
@extends('admin.layout.app')

@section('content')
<div class="container">
	<div class="row">
		<div class="col-md-8 col-md-offset-2">
			<div class="panel panel-default">
				<div class="panel-heading">お問い合わせ 内容確認</div>
				<div class="panel-body">
					<form class="form-horizontal" role="form" method="POST" action="{{ route('contact.send') }}">
					{{ csrf_field() }}
						<div class="input-group mb-3 mb-3 form-group{{ $errors->has('email') ? ' has-error' : '' }}">
							<label for="email" class="col-md-4 control-label">メールアドレス</label>

							<div class="col-md-6">
								{{ $inputs['email'] }}
								<input name="email" value="{{ $inputs['email'] }}" type="hidden">
							</div>
						</div>
						<div class="input-group mb-3 form-group{{ $errors->has('title') ? ' has-error' : '' }}">
							<label for="title" class="col-md-4 control-label">ご要件</label>

							<div class="col-md-6">
								{{ $inputs['title'] }}
								<input name="title" value="{{ $inputs['title'] }}" type="hidden">
							</div>
						</div>
						<div class="input-group mb-3 form-group{{ $errors->has('body') ? ' has-error' : '' }}">
							<label for="body" class="col-md-4 control-label">お問い合わせ内容</label>

							<div class="col-md-6">
								{{ $inputs['body'] }}
								<input name="body" value="{{ $inputs['body'] }}" type="hidden">
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-6 col-md-offset-4">
								<button type="button" name="action" class="btn btn-secondary" value="back" onclick="history.back()">
									入力内容修正
								</button>
								<button type="submit" name="action" class="btn btn-primary" value="submit">
									送信する
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
@endsection
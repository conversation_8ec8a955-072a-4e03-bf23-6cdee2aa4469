@php
    // デバッグ用
    if (!isset($data)) {
        $data = [];
    } elseif (!is_array($data)) {
        \Log::error('$data is not array: ' . gettype($data) . ' - ' . var_export($data, true));
        $data = [];
    }
@endphp

@if (Admin::user()->permission_type == 3 || Admin::user()->id == 1)
    <div class="box box-default">
        <div class="box-header with-border">
            <h3 class="box-title">ご利用状況</h3>
        </div>
        <!-- /.box-header -->
        <div class="box-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <tr>
                        <td width="180px">現在のプラン</td>
                        <td>
                            @if (Admin::user()->member_type == 1)
                                <span class="contact_status ok">有料</span>
                            @else
                                <span class="contact_status ng">無料</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td width="180px">次の支払日</td>
                        <td>
                            @if (Admin::user()->member_type == 1 && Admin::user()->next_payment_date)
                                {{ date('Y年m月d日', strtotime(Admin::user()->next_payment_date)) }}
                            @else
                                未定
                            @endif
                        </td>
                    </tr>
                    <!-- 2ヶ月無料期間表示行 -->
                    @if (Admin::user()->member_type == 1)
                        <tr id="trial-status-row" style="display: none;">
                            <td width="180px">2ヶ月無料期間</td>
                            <td id="trial-status-text">確認中...</td>
                        </tr>
                    @endif
                    <!-- eBayアカウント影響表示行 -->
                    @if (Admin::user()->member_type == 1 && Admin::user()->ebay_id)
                        <tr id="ebay-impact-row" style="display: none;">
                            <td width="180px">eBay連携影響</td>
                            <td id="ebay-impact-text">確認中...</td>
                        </tr>
                    @endif
                    <tr>
                        <td colspan="2" class="text-center">
                            @if (Admin::user()->member_type == 1)
                                <a href="https://billing.stripe.com/p/login/test_7sI9CnfJD4Aa1S8000" target="_blank"
                                    class="btn btn-danger" style="margin-top: 0px;">プラン解約</a>
                            @else
                                <!-- 有料プランへの登録ボタン -->
                                <div class="upgrade-buttons">
                                    <!-- 元のPOSTフォーム -->
                                    <form id="checkout-form" method="GET"
                                        action="{{ url('/admin/stripe/create-checkout-session') }}"
                                        style="display: inline;">
                                        @csrf
                                        <button type="submit" class="btn btn-success" id="checkout-button" style="margin-top: 0px;">
                                            <span id="button-text"><i class="fa fa-credit-card"></i> 有料プランに登録</span>
                                            <span id="button-loader" style="display: none;">処理中...</span>
                                        </button>
                                    </form>

                                    <div class="trial-info" style="margin-top: 10px; font-size: 12px; color: #666;">
                                        <i class="fa fa-info-circle"></i>
                                        <span id="trial-info-text">2ヶ月間の無料期間が適用されます</span>
                                        <span id="ebay-warning" style="display: none; color: #d9534f;">
                                            <br><i class="fa fa-warning"></i>
                                            連携中のeBayアカウントは過去に使用されているため、無料期間は適用されません
                                        </span>
                                    </div>
                                </div>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
            <!-- /.table-responsive -->
        </div>
        <!-- /.box-body -->
    </div>

    <!-- 有料プラン登録前の確認モーダル（eBay影響確認付き） -->
    <div class="modal fade" id="upgradeConfirmModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h4 class="modal-title">
                        <i class="fa fa-credit-card"></i> 有料プラン登録の確認
                    </h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="trial-warning-section" style="display: none;">
                        <div class="alert alert-warning">
                            <strong><i class="fa fa-exclamation-triangle"></i> 重要なお知らせ</strong>
                        </div>
                        <p id="ebay-impact-message"></p>
                    </div>
                    <div id="normal-upgrade-section">
                        <p><strong>有料プランに登録しますか？</strong></p>
                        <ul class="list-unstyled">
                            <li><i class="fa fa-check text-success"></i> 2ヶ月間の無料期間</li>
                            <li><i class="fa fa-check text-success"></i> eBay連携機能</li>
                            <li><i class="fa fa-check text-success"></i> 全機能利用可能</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times"></i> キャンセル
                    </button>
                    <button type="button" class="btn btn-success" id="proceedUpgrade" style="margin-top: 0px;">
                        <i class="fa fa-credit-card"></i> 有料プランに登録
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 決済フォーム送信前の確認処理
            $('#checkout-form').on('submit', function(e) {
                e.preventDefault(); // デフォルトの送信を停止

                // eBayアカウントが連携されている場合は事前チェック
                @if (Admin::user()->ebay_id)
                    checkEbayImpactBeforeUpgrade();
                @else
                    showUpgradeConfirmModal(false);
                @endif
            });

            // 実際の送信処理
            $('#proceedUpgrade').on('click', function() {
                $('#upgradeConfirmModal').modal('hide');

                const button = $('#checkout-button');
                const buttonText = $('#button-text');
                const buttonLoader = $('#button-loader');

                button.prop('disabled', true);
                buttonText.hide();
                buttonLoader.show();

                // フォームを実際に送信
                $('#checkout-form')[0].submit();
            });

            // 有料ユーザーの場合、無料期間ステータスをチェック
            @if (Admin::user()->member_type == 1 && Admin::user()->stripe_customer_id)
                checkTrialStatus();
            @endif

            // 無料ユーザーでeBayアカウントが連携されている場合、影響をチェック
            @if (Admin::user()->member_type == 0 && Admin::user()->ebay_id)
                checkEbayImpactForFreeUser();
            @endif
        });

        // 有料プラン登録前のeBay影響チェック
        function checkEbayImpactBeforeUpgrade() {
            const ebayId = '{{ Admin::user()->ebay_id ?? '' }}';
            if (!ebayId) {
                showUpgradeConfirmModal(false);
                return;
            }

            fetch('/admin/ebay/trial-impact-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    body: JSON.stringify({
                        ebay_id: ebayId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showUpgradeConfirmModal(data.is_returning_ebay);
                    } else {
                        // エラーの場合は通常の確認モーダルを表示
                        showUpgradeConfirmModal(false);
                    }
                })
                .catch(error => {
                    console.error('eBay影響チェックエラー:', error);
                    showUpgradeConfirmModal(false);
                });
        }

        // アップグレード確認モーダル表示
        function showUpgradeConfirmModal(hasEbayImpact) {
            if (hasEbayImpact) {
                $('#trial-warning-section').show();
                $('#normal-upgrade-section').hide();
                $('#ebay-impact-message').html(`
                    <p>現在連携中のeBayアカウントは過去に使用されているため、<strong>2ヶ月間の無料期間は適用されません</strong>。</p>
                    <p>登録と同時に通常料金での請求が開始されます。</p>
                `);
                $('#proceedUpgrade').html('<i class="fa fa-credit-card"></i> 無料期間なしで登録');
                $('#proceedUpgrade').removeClass('btn-success').addClass('btn-warning');
            } else {
                $('#trial-warning-section').hide();
                $('#normal-upgrade-section').show();
                $('#proceedUpgrade').html('<i class="fa fa-credit-card"></i> 有料プランに登録');
                $('#proceedUpgrade').removeClass('btn-warning').addClass('btn-success');
            }

            $('#upgradeConfirmModal').modal('show');
        }

        // 2ヶ月無料期間ステータス確認
        function checkTrialStatus() {
            fetch('/admin/stripe/trial-status', {
                    method: 'GET',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.has_trial) {
                        $('#trial-status-row').show();

                        // 残り日数に応じて表示色を変更
                        let statusClass = 'contact_status';
                        let statusColor = '#f39c12'; // デフォルトはオレンジ
                        let statusText = '無料期間中';

                        if (data.trial_days_remaining > 45) {
                            statusColor = '#5cb85c'; // 緑（1.5ヶ月以上残り）
                        } else if (data.trial_days_remaining > 14) {
                            statusColor = '#f39c12'; // オレンジ（2週間〜1.5ヶ月残り）
                        } else if (data.trial_days_remaining > 0) {
                            statusColor = '#d9534f'; // 赤（2週間未満）
                            statusText = '無料期間終了間近';
                        } else {
                            statusColor = '#999'; // グレー（終了済み）
                            statusText = '無料期間終了済み';
                        }

                        $('#trial-status-text').html(`
                            <span class="${statusClass}" style="background: ${statusColor}; color: white;">
                                ${statusText} (残り${data.trial_days_remaining}日)
                            </span>
                        `);
                    }
                })
                .catch(error => {
                    console.error('無料期間の確認に失敗しました:', error);
                });
        }

        // 無料ユーザーのeBay影響チェック
        function checkEbayImpactForFreeUser() {
            const ebayId = '{{ Admin::user()->ebay_id ?? '' }}';
            if (!ebayId) return;

            fetch('/admin/ebay/trial-impact-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    body: JSON.stringify({
                        ebay_id: ebayId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.is_returning_ebay) {
                        // 過去に使用されたeBayアカウントの場合、警告を表示
                        $('#ebay-warning').show();
                        $('#trial-info-text').hide();
                    }
                })
                .catch(error => {
                    console.error('eBay影響チェックに失敗しました:', error);
                });
        }

        // 有料ユーザーのeBay影響表示
        @if (Admin::user()->member_type == 1 && Admin::user()->ebay_id)
            function checkCurrentEbayImpact() {
                const ebayId = '{{ Admin::user()->ebay_id }}';

                fetch('/admin/ebay/trial-impact-status', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        body: JSON.stringify({
                            ebay_id: ebayId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            $('#ebay-impact-row').show();
                            if (data.is_returning_ebay) {
                                $('#ebay-impact-text').html(`
                                    <span class="text-danger">
                                        <i class="fa fa-exclamation-triangle"></i>
                                        過去利用済み（無料期間短縮適用済み）
                                    </span>
                                `);
                            } else {
                                $('#ebay-impact-text').html(`
                                    <span class="text-success">
                                        <i class="fa fa-check-circle"></i>
                                        影響なし（2ヶ月無料期間継続）
                                    </span>
                                `);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('eBay影響確認エラー:', error);
                    });
            }

            // ページ読み込み時に実行
            setTimeout(checkCurrentEbayImpact, 1000);
        @endif
    </script>

    <style>
        .contact_status {
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: 700;
        }

        .contact_status.ok {
            background: green;
            color: white;
        }

        .contact_status.ng {
            background: yellow;
            color: black;
        }

        .btn-success {
            margin-top: 10px;
            font-weight: bold;
        }

        .btn-danger {
            margin-top: 10px;
            background-color: #d9534f;
            border-color: #d43f3a;
            color: white;
        }

        #button-loader {
            font-size: 14px;
        }

        .upgrade-buttons {
            text-align: center;
        }

        .trial-info {
            max-width: 400px;
            margin: 10px auto 0;
            padding: 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
            border-left: 3px solid #5cb85c;
        }

        #ebay-warning {
            border-left-color: #d9534f !important;
        }

        .text-success {
            color: #5cb85c !important;
        }

        .text-warning {
            color: #f0ad4e !important;
        }

        .text-danger {
            color: #d9534f !important;
        }

        .text-muted {
            color: #777 !important;
        }

        /* モーダルのスタイル */
        .modal-content {
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            border-bottom: 1px solid #e5e5e5;
        }

        .alert {
            margin-bottom: 15px;
        }

        .list-unstyled li {
            padding: 5px 0;
            font-size: 14px;
        }

        .modal-footer .btn {
            margin-left: 10px;
        }

        .modal-footer {
            align-items: center;
        }

    </style>
@endif

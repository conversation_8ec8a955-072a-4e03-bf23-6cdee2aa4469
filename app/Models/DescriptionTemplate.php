<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DescriptionTemplate extends Model
{
    use HasFactory;
	
	protected $table = 'description_templates';
	protected $primaryKey = 'id';
	protected $guarded = ['created_at', 'updated_at'];
	
	
	public function __construct($attributes = []){
		parent::__construct($attributes);
	}
}

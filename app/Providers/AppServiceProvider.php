<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Member;
use App\Models\Administrator;
use App\Observers\MemberObserver;
use App\Observers\AdministratorObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        \URL::forceScheme('https');
		Member::observe(MemberObserver::class);
		Administrator::observe(AdministratorObserver::class);
    }
}

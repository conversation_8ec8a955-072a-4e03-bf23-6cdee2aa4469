<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class EbayHybridApiService
{
    private $client;
    private $browseApiUrl;
    private $tradingApiUrl;
    private $tradingApiLevel;

    public function __construct()
    {
        $this->client = new Client();
        $this->browseApiUrl = config('ebay.api_endpoint');
        $this->tradingApiUrl = config('ebay.traditional.api');
        $this->tradingApiLevel = config('ebay.traditional.level');
    }

    /**
     * ハイブリッドアプローチで商品情報を取得
     * Browse API -> Trading API の順でフォールバック
     */
    public function getItemData($itemId, $token)
    {
        Log::setDefaultDriver('exhibits');
        Log::info("Starting hybrid item fetch for ID: {$itemId}");

        // URLからアイテムIDを抽出
        $cleanItemId = $this->extractItemId($itemId);

        if (!$cleanItemId) {
            return $this->createErrorResponse('Invalid item ID format', $itemId);
        }

        // キャッシュキーを生成
        $cacheKey = "ebay_item_{$cleanItemId}";

        // キャッシュから取得を試行（5分間）
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            Log::info("Returning cached data for item: {$cleanItemId}");
            return $cachedData;
        }

        // 1. Browse APIで取得を試行
        $browseResult = $this->fetchFromBrowseApi($cleanItemId, $token);

        if ($browseResult['success']) {
            Log::info("Browse API successful for item: {$cleanItemId}");

            // Browse APIで不足している情報をチェック
            $missingData = $this->identifyMissingData($browseResult['data']);

            if (!empty($missingData)) {
                Log::info("Missing data detected, supplementing with Trading API: " . implode(', ', $missingData));

                // Trading APIで不足データを補完
                $tradingResult = $this->fetchFromTradingApi($cleanItemId, $token);

                if ($tradingResult['success']) {
                    $browseResult['data'] = $this->mergeItemData($browseResult['data'], $tradingResult['data']);
                    $browseResult['data']['api_sources'] = ['browse', 'trading'];
                } else {
                    $browseResult['data']['api_sources'] = ['browse'];
                    $browseResult['warnings'] = ['Trading API supplementation failed'];
                }
            } else {
                $browseResult['data']['api_sources'] = ['browse'];
            }

            // 成功した結果をキャッシュ
            Cache::put($cacheKey, $browseResult, 300); // 5分間キャッシュ
            return $browseResult;
        }

        Log::info("Browse API failed, falling back to Trading API for item: {$cleanItemId}");

        // 2. Browse APIが失敗した場合、Trading APIで取得
        $tradingResult = $this->fetchFromTradingApi($cleanItemId, $token);

        if ($tradingResult['success']) {
            $tradingResult['data']['api_sources'] = ['trading'];
            Cache::put($cacheKey, $tradingResult, 300);
            return $tradingResult;
        }

        // 両方のAPIが失敗した場合
        Log::error("Both APIs failed for item: {$cleanItemId}");
        return $this->createErrorResponse(
            'Both Browse API and Trading API failed',
            $cleanItemId,
            [
                'browse_error' => $browseResult['error'] ?? 'Unknown error',
                'trading_error' => $tradingResult['error'] ?? 'Unknown error'
            ]
        );
    }

    /**
     * Browse APIから商品情報を取得
     */
    private function fetchFromBrowseApi($itemId, $token)
    {
        try {
            $response = $this->client->request(
                'GET',
                $this->browseApiUrl . '/buy/browse/v1/item/v1|' . $itemId . '|0',
                [
                    'http_errors' => false,
                    'headers' => [
                        'Authorization' => 'Bearer ' . $token,
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                        'Content-Language' => 'en-US',
                        'X-EBAY-C-MARKETPLACE-ID' => 'EBAY_US',
                        'X-EBAY-C-ENDUSERCTX' => 'contextualLocation=country=US',
                    ],
                ]
            );

            $responseBody = $response->getBody()->getContents();
            $data = json_decode($responseBody, true);

            Log::info("Browse API Response Status: " . $response->getStatusCode());

            if ($response->getStatusCode() !== 200) {
                return [
                    'success' => false,
                    'error' => $data['errors'][0]['message'] ?? 'Browse API request failed',
                    'raw_response' => $data
                ];
            }

            return [
                'success' => true,
                'data' => $this->parseBrowseApiResponse($data)
            ];
        } catch (\Exception $e) {
            Log::error("Browse API Exception: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Browse API exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Trading APIから商品情報を取得
     */
    private function fetchFromTradingApi($itemId, $token)
    {
        try {
            $xmlRequest = $this->buildTradingApiRequest($itemId, $token);

            $response = $this->client->request('POST', $this->tradingApiUrl, [
                'http_errors' => false,
                'headers' => [
                    'X-EBAY-API-COMPATIBILITY-LEVEL' => $this->tradingApiLevel,
                    'X-EBAY-API-DEV-NAME' => config('ebay.dev_id'),
                    'X-EBAY-API-APP-NAME' => config('ebay.app_id'),
                    'X-EBAY-API-CERT-NAME' => config('ebay.cert_id'),
                    'X-EBAY-API-CALL-NAME' => 'GetItem',
                    'X-EBAY-API-SITEID' => '0',
                    'Content-Type' => 'text/xml',
                ],
                'body' => $xmlRequest,
            ]);

            $responseBody = $response->getBody()->getContents();

            Log::info("Trading API Response Status: " . $response->getStatusCode());

            if ($response->getStatusCode() !== 200) {
                return [
                    'success' => false,
                    'error' => 'Trading API HTTP error: ' . $response->getStatusCode()
                ];
            }

            $xml = simplexml_load_string($responseBody);

            if (!$xml || (string)$xml->Ack !== 'Success') {
                $errorMessage = 'Trading API returned error';
                if ($xml && isset($xml->Errors)) {
                    $errorMessage = (string)$xml->Errors->LongMessage;
                }
                return [
                    'success' => false,
                    'error' => $errorMessage
                ];
            }

            return [
                'success' => true,
                'data' => $this->parseTradingApiResponse($xml->Item)
            ];
        } catch (\Exception $e) {
            Log::error("Trading API Exception: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Trading API exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Browse APIレスポンスをパース
     */
    private function parseBrowseApiResponse($data)
    {
        $parsed = [
            'item_id' => $data['itemId'] ?? '',
            'category_id' => $data['categoryId'] ?? '',
            'title' => $data['title'] ?? '',
            'condition' => $data['conditionId'] ?? '',
            'condition_display_name' => $data['condition'] ?? '',
            'condition_descriptors' => $data['conditionDescriptors'] ?? [],
            'localized_aspects' => [],
            'description' => '',
            'images' => [],
            'price' => null,
            'currency' => 'USD',
            'seller_info' => [],
            'shipping_info' => []
        ];

        // 説明文を取得
        if (isset($data['description'])) {
            $parsed['description'] = $data['description'];
        } elseif (isset($data['shortDescription'])) {
            $parsed['description'] = $data['shortDescription'];
        }

        // アスペクト情報を取得
        if (isset($data['localizedAspects'])) {
            foreach ($data['localizedAspects'] as $aspect) {
                $parsed['localized_aspects'][$aspect['name']] = [$aspect['value']];
            }
        }

        // プロダクトアスペクトも取得
        if (isset($data['product']['aspects'])) {
            foreach ($data['product']['aspects'] as $aspect) {
                $name = $aspect['name'];
                $values = $aspect['values'] ?? [];
                if (!isset($parsed['localized_aspects'][$name])) {
                    $parsed['localized_aspects'][$name] = $values;
                }
            }
        }

        // 画像情報を取得
        if (isset($data['image']['imageUrl'])) {
            $parsed['images'][] = $data['image']['imageUrl'];
        }
        if (isset($data['additionalImages'])) {
            foreach ($data['additionalImages'] as $img) {
                $parsed['images'][] = $img['imageUrl'];
            }
        }

        // 価格情報を取得
        if (isset($data['price']['value'])) {
            $parsed['price'] = (float)$data['price']['value'];
            $parsed['currency'] = $data['price']['currency'] ?? 'USD';
        }

        // 販売者情報を取得
        if (isset($data['seller'])) {
            $parsed['seller_info'] = [
                'username' => $data['seller']['username'] ?? '',
                'feedback_percentage' => $data['seller']['feedbackPercentage'] ?? 0,
                'feedback_score' => $data['seller']['feedbackScore'] ?? 0
            ];
        }

        return $parsed;
    }

    /**
     * Trading APIレスポンスをパース
     */
    private function parseTradingApiResponse($item)
    {
        $parsed = [
            'item_id' => (string)$item->ItemID,
            'category_id' => (string)$item->PrimaryCategory->CategoryID,
            'title' => (string)$item->Title,
            'condition' => (string)$item->ConditionID,
            'condition_display_name' => (string)$item->ConditionDisplayName,
            'condition_descriptors' => [],
            'localized_aspects' => [],
            'description' => (string)$item->Description,
            'images' => [],
            'price' => null,
            'currency' => 'USD',
            'seller_info' => [],
            'shipping_info' => []
        ];

        // ItemSpecificsから商品詳細を取得
        if (isset($item->ItemSpecifics->NameValueList)) {
            foreach ($item->ItemSpecifics->NameValueList as $spec) {
                $name = (string)$spec->Name;
                $values = [];

                if (isset($spec->Value)) {
                    if (is_array($spec->Value)) {
                        foreach ($spec->Value as $value) {
                            $values[] = (string)$value;
                        }
                    } else {
                        $values[] = (string)$spec->Value;
                    }
                }
                $parsed['localized_aspects'][$name] = $values;
            }
        }

        // 画像情報を取得
        if (isset($item->PictureDetails->PictureURL)) {
            if (is_array($item->PictureDetails->PictureURL)) {
                foreach ($item->PictureDetails->PictureURL as $url) {
                    $parsed['images'][] = (string)$url;
                }
            } else {
                $parsed['images'][] = (string)$item->PictureDetails->PictureURL;
            }
        }

        // 価格情報を取得
        if (isset($item->SellingStatus->CurrentPrice)) {
            $parsed['price'] = (float)$item->SellingStatus->CurrentPrice;
            $parsed['currency'] = (string)$item->SellingStatus->CurrentPrice['currencyID'] ?? 'USD';
        }

        // 販売者情報を取得
        if (isset($item->Seller)) {
            $parsed['seller_info'] = [
                'username' => (string)$item->Seller->UserID,
                'feedback_percentage' => (float)($item->Seller->PositiveFeedbackPercent ?? 0),
                'feedback_score' => (int)($item->Seller->FeedbackScore ?? 0)
            ];
        }

        return $parsed;
    }

    /**
     * 不足しているデータを特定
     */
    private function identifyMissingData($data)
    {
        $missing = [];

        // 重要なフィールドをチェック
        $criticalFields = [
            'description' => 'description',
            'localized_aspects' => 'item_specifics',
            'images' => 'images',
            'condition_descriptors' => 'condition_details'
        ];

        foreach ($criticalFields as $field => $label) {
            if (
                empty($data[$field]) ||
                (is_array($data[$field]) && count($data[$field]) === 0)
            ) {
                $missing[] = $label;
            }
        }

        return $missing;
    }

    /**
     * Browse APIとTrading APIのデータをマージ
     */
    private function mergeItemData($browseData, $tradingData)
    {
        $merged = $browseData;

        // 説明文の補完
        if (empty($merged['description']) && !empty($tradingData['description'])) {
            $merged['description'] = $tradingData['description'];
        }

        // アスペクトの補完
        if (!empty($tradingData['localized_aspects'])) {
            foreach ($tradingData['localized_aspects'] as $key => $value) {
                if (
                    !isset($merged['localized_aspects'][$key]) ||
                    empty($merged['localized_aspects'][$key])
                ) {
                    $merged['localized_aspects'][$key] = $value;
                }
            }
        }

        // 画像の補完
        if (!empty($tradingData['images'])) {
            $existingImages = array_flip($merged['images']);
            foreach ($tradingData['images'] as $image) {
                if (!isset($existingImages[$image])) {
                    $merged['images'][] = $image;
                }
            }
        }

        // コンディション情報の補完
        if (
            empty($merged['condition_descriptors']) &&
            !empty($tradingData['condition_descriptors'])
        ) {
            $merged['condition_descriptors'] = $tradingData['condition_descriptors'];
        }

        // 価格情報の補完
        if (is_null($merged['price']) && !is_null($tradingData['price'])) {
            $merged['price'] = $tradingData['price'];
            $merged['currency'] = $tradingData['currency'];
        }

        return $merged;
    }

    /**
     * Trading API用のXMLリクエストを構築
     */
    private function buildTradingApiRequest($itemId, $token)
    {
        return '<?xml version="1.0" encoding="utf-8"?>
        <GetItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
            <RequesterCredentials>
                <eBayAuthToken>' . htmlspecialchars($token) . '</eBayAuthToken>
            </RequesterCredentials>
            <ItemID>' . htmlspecialchars($itemId) . '</ItemID>
            <DetailLevel>ReturnAll</DetailLevel>
            <IncludeItemSpecifics>true</IncludeItemSpecifics>
            <IncludeItemCompatibilityList>true</IncludeItemCompatibilityList>
        </GetItemRequest>';
    }

    /**
     * URLまたはIDからアイテムIDを抽出
     */
    private function extractItemId($input)
    {
        // すでに数値のIDの場合
        if (is_numeric($input)) {
            return $input;
        }

        // URLの場合
        if (filter_var($input, FILTER_VALIDATE_URL)) {
            $patterns = [
                '/\/itm\/(\d+)/',
                '/\/itm\/[^\/]+\/(\d+)/',
                '/item=(\d+)/',
                '/\/p\/(\d+)/',
            ];

            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $input, $matches)) {
                    return $matches[1];
                }
            }
        }

        // パスからファイル名を抽出
        $path = parse_url($input, PHP_URL_PATH);
        if ($path) {
            $filename = pathinfo($path, PATHINFO_FILENAME);
            if (is_numeric($filename)) {
                return $filename;
            }
        }

        return null;
    }

    /**
     * エラーレスポンスを作成
     */
    private function createErrorResponse($message, $itemId, $additionalData = [])
    {
        return [
            'success' => false,
            'error' => $message,
            'item_id' => $itemId,
            'additional_data' => $additionalData
        ];
    }

    /**
     * APIの利用可能性をチェック
     */
    public function checkApiAvailability($token)
    {
        $results = [
            'browse_api' => false,
            'trading_api' => false,
            'timestamp' => now()->toISOString()
        ];

        // Browse APIのテスト
        try {
            $response = $this->client->request(
                'GET',
                $this->browseApiUrl . '/buy/browse/v1/item_summary/search?q=test&limit=1',
                [
                    'http_errors' => false,
                    'headers' => [
                        'Authorization' => 'Bearer ' . $token,
                        'Accept' => 'application/json',
                        'X-EBAY-C-MARKETPLACE-ID' => 'EBAY_US',
                    ],
                    'timeout' => 10
                ]
            );

            $results['browse_api'] = $response->getStatusCode() < 400;
        } catch (\Exception $e) {
            Log::error("Browse API availability check failed: " . $e->getMessage());
        }

        // Trading APIのテスト
        try {
            $xmlRequest = '<?xml version="1.0" encoding="utf-8"?>
            <GeteBayOfficialTimeRequest xmlns="urn:ebay:apis:eBLBaseComponents">
                <RequesterCredentials>
                    <eBayAuthToken>' . htmlspecialchars($token) . '</eBayAuthToken>
                </RequesterCredentials>
            </GeteBayOfficialTimeRequest>';

            $response = $this->client->request('POST', $this->tradingApiUrl, [
                'http_errors' => false,
                'headers' => [
                    'X-EBAY-API-COMPATIBILITY-LEVEL' => $this->tradingApiLevel,
                    'X-EBAY-API-DEV-NAME' => config('ebay.dev_id'),
                    'X-EBAY-API-APP-NAME' => config('ebay.app_id'),
                    'X-EBAY-API-CERT-NAME' => config('ebay.cert_id'),
                    'X-EBAY-API-CALL-NAME' => 'GeteBayOfficialTime',
                    'X-EBAY-API-SITEID' => '0',
                    'Content-Type' => 'text/xml',
                ],
                'body' => $xmlRequest,
                'timeout' => 10
            ]);

            if ($response->getStatusCode() === 200) {
                $xml = simplexml_load_string($response->getBody()->getContents());
                $results['trading_api'] = $xml && (string)$xml->Ack === 'Success';
            }
        } catch (\Exception $e) {
            Log::error("Trading API availability check failed: " . $e->getMessage());
        }

        return $results;
    }
}

<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Stripe\Stripe;
use Stripe\Subscription;

class EbayTrialService
{
    public function __construct()
    {
        if (config('services.stripe.secret')) {
            Stripe::setApiKey(config('services.stripe.secret'));
        }
    }

    /**
     * eBayアカウントが過去に使用されているかチェック
     */
    public function isEbayAccountUsedBefore($ebayId, $currentUserId = null)
    {
        if (empty($ebayId)) {
            return false;
        }

        $query = DB::table('ebay_usage_history')
            ->where('ebay_id', $ebayId);

        // 現在のユーザーを除外して検索
        if ($currentUserId) {
            $query->where('user_id', '!=', $currentUserId);
        }

        $previousUsage = $query->first();

        Log::info('eBay usage check', [
            'ebay_id' => $ebayId,
            'current_user_id' => $currentUserId,
            'has_previous_usage' => !!$previousUsage
        ]);

        return !!$previousUsage;
    }

    /**
     * eBayアカウント使用履歴を記録
     */
    public function recordEbayUsage($ebayId, $ebayUsername, $userId)
    {
        if (empty($ebayId) || empty($userId)) {
            return false;
        }

        try {
            $now = Carbon::now();

            // 既存の記録をチェック
            $existing = DB::table('ebay_usage_history')
                ->where('ebay_id', $ebayId)
                ->where('user_id', $userId)
                ->first();

            if ($existing) {
                // 既存記録を更新
                DB::table('ebay_usage_history')
                    ->where('id', $existing->id)
                    ->update([
                        'ebay_username' => $ebayUsername,
                        'last_connected_at' => $now,
                        'updated_at' => $now,
                    ]);

                Log::info('Updated existing eBay usage record', [
                    'ebay_id' => $ebayId,
                    'user_id' => $userId
                ]);
            } else {
                // 新規記録を作成
                DB::table('ebay_usage_history')->insert([
                    'ebay_id' => $ebayId,
                    'ebay_username' => $ebayUsername,
                    'user_id' => $userId,
                    'first_connected_at' => $now,
                    'last_connected_at' => $now,
                    'created_at' => $now,
                    'updated_at' => $now,
                ]);

                Log::info('Created new eBay usage record', [
                    'ebay_id' => $ebayId,
                    'user_id' => $userId
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to record eBay usage', [
                'ebay_id' => $ebayId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 無料期間をキャンセル（eBay重複使用時）- Stripe連携強化版
     */
    public function cancelTrialDueToEbayDuplication($ebayId, $userId)
    {
        if (empty($ebayId) || empty($userId)) {
            return false;
        }

        try {
            DB::beginTransaction();

            $now = Carbon::now();

            // ユーザー情報を取得
            $user = DB::table('admin_users')->where('id', $userId)->first();
            if (!$user) {
                throw new \Exception('User not found');
            }

            // eBay使用履歴にトライアルキャンセル記録
            DB::table('ebay_usage_history')
                ->where('ebay_id', $ebayId)
                ->where('user_id', $userId)
                ->update([
                    'trial_cancelled' => true,
                    'trial_cancelled_at' => $now,
                    'updated_at' => $now,
                ]);

            // Stripeサブスクリプションのトライアル終了
            if ($user->subscription_id) {
                $this->endStripeTrialImmediately($user->subscription_id);
            }

            // ユーザーの無料期間を即座に終了
            DB::table('admin_users')
                ->where('id', $userId)
                ->update([
                    'trial_ends_at' => $now,
                    'next_payment_date' => $now->toDateTimeString(),
                    'updated_at' => $now,
                ]);

            DB::commit();

            Log::info('Trial cancelled due to eBay duplication', [
                'ebay_id' => $ebayId,
                'user_id' => $userId,
                'stripe_subscription_id' => $user->subscription_id
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel trial', [
                'ebay_id' => $ebayId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Stripeサブスクリプションのトライアルを即座に終了
     */
    private function endStripeTrialImmediately($subscriptionId)
    {
        try {
            if (!config('services.stripe.secret')) {
                Log::warning('Stripe secret not configured, skipping trial end');
                return false;
            }

            $subscription = Subscription::retrieve($subscriptionId);

            if ($subscription->status === 'trialing') {
                // トライアル期間を現在時刻に設定して即座に終了
                $subscription = Subscription::update($subscriptionId, [
                    'trial_end' => 'now'
                ]);

                Log::info('Stripe trial ended immediately', [
                    'subscription_id' => $subscriptionId,
                    'new_status' => $subscription->status
                ]);

                return true;
            } else {
                Log::info('Subscription not in trial, no action needed', [
                    'subscription_id' => $subscriptionId,
                    'status' => $subscription->status
                ]);
                return true;
            }
        } catch (\Exception $e) {
            Log::error('Failed to end Stripe trial', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * ユーザーの無料期間ステータスをチェック（Stripe連携版）
     */
    public function getTrialStatus($userId)
    {
        $user = DB::table('admin_users')->where('id', $userId)->first();

        if (!$user || $user->member_type != 1) {
            return [
                'has_trial' => false,
                'trial_days_remaining' => 0,
                'trial_active' => false
            ];
        }

        // Stripeサブスクリプションの状態もチェック
        $stripeTrialActive = false;
        $stripeTrialEnd = null;

        if ($user->subscription_id && config('services.stripe.secret')) {
            try {
                $subscription = Subscription::retrieve($user->subscription_id);
                if ($subscription->status === 'trialing' && $subscription->trial_end) {
                    $stripeTrialActive = true;
                    $stripeTrialEnd = Carbon::createFromTimestamp($subscription->trial_end);
                }
            } catch (\Exception $e) {
                Log::error('Failed to retrieve Stripe subscription', [
                    'subscription_id' => $user->subscription_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $now = Carbon::now();
        $trialEndsAt = $user->trial_ends_at ? Carbon::parse($user->trial_ends_at) : null;

        // Stripeの情報がある場合はそちらを優先
        if ($stripeTrialEnd && $stripeTrialActive) {
            $trialEndsAt = $stripeTrialEnd;
        }

        if (!$trialEndsAt) {
            return [
                'has_trial' => false,
                'trial_days_remaining' => 0,
                'trial_active' => false,
                'stripe_trial_active' => $stripeTrialActive
            ];
        }

        $daysRemaining = max(0, $now->diffInDays($trialEndsAt, false));
        $trialActive = $trialEndsAt->isFuture() && ($stripeTrialActive || !$user->subscription_id);

        return [
            'has_trial' => true,
            'trial_days_remaining' => $daysRemaining,
            'trial_active' => $trialActive,
            'trial_ends_at' => $trialEndsAt,
            'stripe_trial_active' => $stripeTrialActive
        ];
    }

    /**
     * 2ヶ月無料期間を開始
     */
    public function startTwoMonthTrial($userId)
    {
        try {
            $now = Carbon::now();
            $trialEndsAt = $now->copy()->addMonths(2);

            DB::table('admin_users')
                ->where('id', $userId)
                ->update([
                    'trial_started_at' => $now,
                    'trial_ends_at' => $trialEndsAt,
                    'next_payment_date' => $trialEndsAt->toDateTimeString(),
                    'has_used_trial' => true,
                    'updated_at' => $now,
                ]);

            Log::info('Started 2-month trial', [
                'user_id' => $userId,
                'trial_ends_at' => $trialEndsAt
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to start trial', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 無料期間の残り日数を取得（表示用）
     */
    public function getTrialDaysRemaining($userId)
    {
        $trialStatus = $this->getTrialStatus($userId);
        return $trialStatus['trial_days_remaining'];
    }

    /**
     * 無料期間が有効かチェック
     */
    public function isTrialActive($userId)
    {
        $trialStatus = $this->getTrialStatus($userId);
        return $trialStatus['trial_active'];
    }

    /**
     * eBayアカウントが無料期間に影響するかチェック（事前確認用）
     */
    public function checkEbayTrialImpact($ebayId, $userId)
    {
        if (empty($ebayId)) {
            return [
                'has_impact' => false,
                'is_returning_ebay' => false,
                'will_cancel_trial' => false,
                'message' => 'No eBay ID provided'
            ];
        }

        $user = DB::table('admin_users')->where('id', $userId)->first();
        if (!$user || $user->member_type != 1) {
            return [
                'has_impact' => false,
                'is_returning_ebay' => false,
                'will_cancel_trial' => false,
                'message' => 'User is not a paid member'
            ];
        }

        $isReturningEbay = $this->isEbayAccountUsedBefore($ebayId, $userId);
        $trialStatus = $this->getTrialStatus($userId);
        $willCancelTrial = $isReturningEbay && $trialStatus['trial_active'];

        return [
            'has_impact' => $willCancelTrial,
            'is_returning_ebay' => $isReturningEbay,
            'will_cancel_trial' => $willCancelTrial,
            'current_trial_days' => $trialStatus['trial_days_remaining'],
            'message' => $willCancelTrial ? 'Trial will be cancelled due to returning eBay account' : 'No impact on trial'
        ];
    }
}

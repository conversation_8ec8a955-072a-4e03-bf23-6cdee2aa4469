<?php

namespace App\Observers;

use App\Models\Administrator;

class AdministratorObserver
{
	public function deleted(Administrator $administrator) : void
	{
		//$user_id = $administrator->id;
		//DB::beginTransaction();
		//try{
		//	DB::table('admin_role_users')->where('user_id',$user_id)->delete();
		//	DB::table('admin_users')->where('id',$user_id)->delete();
		//	DB::commit();
		//}catch(\Exception $e){
		//	DB::rollback();
		//}
		
	}
}

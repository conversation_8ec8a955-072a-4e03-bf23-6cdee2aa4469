<?php

namespace App\Observers;

use App\Models\Member;

class MemberObserver
{
    public function deleted(Member $member) : void
	{
		//$user_id = $member->id;
		//DB::beginTransaction();
		//try{
		//	DB::table('admin_role_users')->where('user_id',$user_id)->delete();
		//	DB::table('admin_users')->where('id',$user_id)->delete();
		//	
		//	DB::commit();
		//}catch(\Exception $e){
		//	DB::rollback();
		//}
		
	}
}

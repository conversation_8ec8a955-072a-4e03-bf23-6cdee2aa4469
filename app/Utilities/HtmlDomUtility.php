<?php

namespace App\Utilities;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use KubAT\PhpSimple\HtmlDomParser;
use Illuminate\Support\Facades\Log;

class HtmlDomUtility
{

    /**
     * URLのHTML読み込み
     *
     * @param string $urlString 取得したいページのURL
     * @return string
     */
    public static function getHtml($urlString)
    {
        $html_source = "";
        $ch = curl_init();

        // $agent = "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:47.0) Gecko/20100101 Firefox/47.0)";
        $agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36";
        $header = array(
            'Accept-Encoding: deflate',
            'Accept-Language: ja,en-US;q=0.9,en;q=0.8',
        );

        //ここからオプション
        curl_setopt($ch, CURLOPT_URL, $urlString);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  //証明書検証しない
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  //レスポンスを表示するか
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);  //"Location: " ヘッダの内容をたどる
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);  //"Location: " ヘッダの内容をたどる深さ
        //curl_setopt($ch, CURLOPT_HTTPHEADER, ['User-Agent: AppleWebKit/10000']);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);        //ここまでオプション

        $output = curl_exec($ch);   //cURL セッションを実行する
        $info = curl_getinfo($ch);
        Log::error($output);
        Log::error("-----------------");
        Log::error($info);
        Log::error("----------------------------------------");
        $errno = curl_errno($ch);
        $error = curl_error($ch);
        curl_close($ch);
        if (CURLE_OK !== $errno) {
            Log::error("cURL error ({$errno}): {$error}");
            return '';
        } else {
            mb_language("Japanese");
            $html_source = mb_convert_encoding($output, "UTF-8", "auto");
        }
        return $html_source;
    }

    /**
     * HTMLを配列化
     *
     * @param string $htmlString HTMLの文字列
     * @return object
     */
    public static function getHtmlToDom($htmlString)
    {
	/*
	$no_script_html = preg_replace( '@<(script|style)[^>]*?>.*?</\\1>@si', '', $htmlString );
        $dom = HtmlDomParser::str_get_html($no_script_html);
	*/

        $dom = HtmlDomParser::str_get_html($htmlString);
        return $dom;
    }

}

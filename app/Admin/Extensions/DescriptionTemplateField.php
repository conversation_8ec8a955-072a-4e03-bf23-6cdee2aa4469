<?php
namespace App\Admin\Extensions;
use Encore\Admin\Form\Field;
use App\Models\DescriptionTemplate;

class DescriptionTemplateField extends Field {
	protected $view = 'admin.extensions.description_template';
	
	protected static $css = [];
	
	protected static $js = [];
	
	public function render(){
		$templates = DescriptionTemplate::all()->get();
		$options = [];
		foreach($templates as $template){
			$options[] = [
				'id' => $template->id,
				'name' => $template->name,
				'description' => $template->description,
			];
		}
		$json = json_encode($options,JSON_UNESCAPED_UNICODE);
		
		$this->script = <<<EOT
var obj = JSON.parse('{$json}');
EOT;
		
		return parent::render();
	}
}
<?php

/**
 * <PERSON><PERSON>-admin - admin builder based on <PERSON><PERSON>.
 * <AUTHOR> <https://github.com/z-song>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 * Encore\Admin\Form::forget(['map', 'editor']);
 *
 * Or extend custom form field:
 * Encore\Admin\Form::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */

use App\Admin\Extensions\Translation;
use App\Admin\Extensions\GrossProfit;
use App\Admin\Extensions\AboutItem;
use App\Admin\Extensions\SearchCategories;
use App\Admin\Extensions\EbayDescription;
use App\Admin\Extensions\CustomSelect;
use App\Admin\Extensions\SelectMerchantLocationKey;
use Encore\Admin\Form;

Encore\Admin\Form::forget(['map', 'editor']);

Admin::headerJs('/js/get_product_ui.js');
Admin::css('/js/get_product_ui.css');

Form::extend('translation',Translation::class);
Form::extend('grossProfit',GrossProfit::class);
Form::extend('aboutItem', AboutItem::class);
Form::extend('searchCategories', SearchCategories::class);
Form::extend('ebayDescription', EbayDescription::class);
Form::extend('customSelect', SelectMerchantLocationKey::class);
Form::extend('selectMerchantLocationKey', SelectMerchantLocationKey::class);

Form::init(function (Form $form){
	$form->footer(function($footer){
        $footer->disableViewCheck();
        $footer->disableEditingCheck();
        $footer->disableCreatingCheck();
	});
});
<?php

use Illuminate\Routing\Router;
use App\Admin\Controllers\AuthController;

Admin::routes();

Route::group([
    'prefix'        => config('admin.route.prefix'),
    'namespace'     => config('admin.route.namespace'),
    'middleware'    => config('admin.route.middleware'),
    'as'            => config('admin.route.prefix') . '.',
], function (Router $router) {

    $router->get('/', 'HomeController@index')->name('home');

    // ユーザ管理
    $router->resource('users',AdminUserController::class);

    // 一般ユーザ管理
    $router->resource('member', MemberUserController::class);

    // eBay出品管理
	$router->get('exhibits/merchant_location_key', 'ExhibitController@viewMerchantLocationKey')->name('view_merchant_location_key');
	$router->get('exhibits/merchant_location_keys', 'ExhibitController@getMerchantLocationKeys')->name('list_merchant_location_key');
	$router->post('exhibits/merchant_location_key/{id}', 'ExhibitController@setMerchantLocationKey')->name('save_merchant_location_key');
    $router->resource('exhibits', ExhibitController::class);

    // 出品テンプレート一覧
    $router->resource('templates', TemplatesController::class);
    $router->resource('description_templates', DescriptionTemplateController::class);

    //UIテスト用
    $router->resource('tests', TestController::class);
    $router->resource('adminapi', AdminapiController::class);


    // // 決済履歴
    // $router->resource('payment_history', PaymentHistoryController::class);


    $router->post('payment', [App\Admin\Controllers\PaymentController::class, 'payment'])->name('payment.payment');

    $router->get('payment/success', [App\Admin\Controllers\PaymentController::class, 'success'])->name('payment.success');
    $router->get('payment/error', [App\Admin\Controllers\PaymentController::class, 'error'])->name('payment.error');

    $router->post('auth/refresh_token',[App\Admin\Controllers\AuthController::class, 'get_user_token'])->name('get_user_token');


});

Route::group([], function () {
    // ログイン関連のルートをCustomAuthControllerに変更
    Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('login', [AuthController::class, 'login']);
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');
});

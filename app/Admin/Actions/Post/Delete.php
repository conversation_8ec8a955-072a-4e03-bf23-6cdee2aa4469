<?php

namespace App\Admin\Actions\Post;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use GuzzleHttp\Client as GuzzleClient;

class Delete extends RowAction
{
    public $name = '削除';

    public function handle(Model $model)
    {
		$message = '削除しました。';
		Log::setDefaultDriver('exhibits');
		$offer_id = $this->row->offer_id;
		
		Log::info('--- DELETE ITEM ---');
		Log::info('offerId : '.$offer_id);
		if($offer_id){
			$user_id = $this->row->admin_user_id;
			$user = DB::table('admin_users')->where('id', $user_id)->first();
			$token = $user->access_token;
			
			$client = null;
			$client = new GuzzleClient();
			
			$response = $client->request('DELETE',config('ebay.api_endpoint').'/sell/inventory/v1/offer/'.$offer_id,[
				'http_errors' => false,
				'headers' => [
					'Authorization' => [
						'Bearer '.$user->access_token,
					],
					'Accept' => ['application/json'],
					'Content-Type' => ['application/json'],
					'Content-Language' => ['en-US'],
				],
			]);
			$code = $response->getStatusCode();
			Log::info('DELETE '.config('ebay.api_endpoint').'/sell/inventory/v1/offer/'.$offer_id);
            Log::info(" Response \n HTTP Status: ".$response->getStatusCode()." \n Body :".$response->getBody()->getContents());
			
			if($code == 200 || $code == 204 || $code == 404) {
				// 成功 ,見つからない場合はDBからも削除
				$message = '削除しました※eBay上からも出品を削除しています。';
			}
		}
		$this->row->delete();
		return $this->response()->success($message)->refresh();
    }
	
	public function dialog(){
		$offer_id = $this->row->offer_id;
		if($offer_id){
			$message = '出品データを削除しますか？';
		} else {
			$message = '出品データを削除しますか？';
		}
		$this->confirm($message);
	}

}
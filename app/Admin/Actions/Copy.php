<?php

namespace App\Admin\Actions;

use Encore\Admin\Actions\RowAction;
use Encore\Admin\Facades\Admin;
use Illuminate\Database\Eloquent\Model;

class Copy extends RowAction
{
    public function name()
    {
        return 'コピー';
    }

    public function handle(Model $model)
    {
        try {
            $new = $model->replicate();
            $new->admin_user_id = Admin::user()->id;
            $new->title = $model->title . '（コピー）';
            $new->save();
        } catch (\Exception $e) {
            return $this->response()->error('コピーに失敗しました: ' . $e->getMessage());
        }

        return $this->response()->success('コピーしました。')->redirect(route('admin.templates.edit', $new->id));
    }
}


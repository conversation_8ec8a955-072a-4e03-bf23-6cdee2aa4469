<?php

namespace App\Admin\Controllers;

use App\Models\ExhibitTemplate;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use App\Admin\Actions\Copy;
use Encore\Admin\Facades\Admin;

use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class TemplatesController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '出品テンプレート';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $templates = new ExhibitTemplate();
        $user_id = Admin::user()->id;

        $grid = new Grid($templates);

        $grid->header(function () {
            return '<span class="help-block"><i class="fa fa-info-circle"></i>' . trans('admin.help.manual.templates') . '</span>';
        });

        //$grid->header(function(){
        //	return '<span class="help-block"><i class="fa fa-info-circle"></i>'.trans('admin.help.manual.templates').'</span>';
        //});
        $grid->disableExport();

        if (Admin::user()->isRole('member') || Admin::user()->isRole('NPmember')) {
            $grid->model()->where('admin_user_id', Admin::user()->id)->orWhere('admin_user_id', 1); //サンプルも表示
            // $grid->model()->where('admin_user_id', Admin::user()->id);
        }

        if (!Admin::user()->isRole('member') && !Admin::user()->isRole('NPmember')) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('admin_user_id', trans('admin.username'))->link(function ($row) {
                return route('admin.member.edit', $row->admin_user_id);
            });
        }

        $grid->column('title', trans('admin.template_title'))->link(function ($row) {
            return route('admin.templates.edit', $row->id);
        }, '');

        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->like('title', trans('admin.title'));
        });

        $grid->tools(function (Grid\Tools $tools) {
            $tools->append('<div class="btn-group pull-right" style="margin-right: 10px"><a href="/admin/templates/create" class="btn btn-sm btn-success"><i class="fa fa-plus"></i>&nbsp;&nbsp;新規テンプレート作成</a></div>');
        });
        $grid->disableCreateButton();

        $grid->actions(function ($actions) {
            $template = \App\Models\ExhibitTemplate::find($actions->getKey());

            if ($template && $template->admin_user_id == 1) {
                $actions->disableEdit();
                $actions->disableDelete();
            }

            $actions->add(new \App\Admin\Actions\Copy());
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $row = ExhibitTemplate::findOrFail($id);

        if ((Admin::user()->isRole('member') || Admin::user()->isRole('NPmember')) &&
            ($row->admin_user_id != Admin::user()->id && $row->admin_user_id != 1)
        ) {
            return redirect('/admin');
        }

        $show = new Show($row);

        $show->field('id', 'ID');
        $show->field('title', trans('admin.title'));
        $show->field('category_id', trans('admin.category_id'));
        $show->field('sku', trans('admin.sku'));
        $show->field('stock_quantity', trans('admin.stock_quantity'));
        $show->field('price', trans('admin.price'));
        $show->field('condition', trans('admin.condition'));
        $show->image('product_image', trans('admin.product_image'));
        $show->field('is_scale', trans('admin.is_scale'));
        $show->field('is_advertising', trans('admin.is_advertising'));
        $show->field('color', trans('admin.color'));
        $show->field('ca_prop_65', trans('admin.ca_prop_65'));
        $show->field('is_lot_sales', trans('admin.is_lot_sales'));
        $show->field('product_type', trans('admin.product_type'));
        $show->field('production_area', trans('admin.production_area'));
        $show->field('origin_date', trans('admin.origin_date'));
        $show->field('product_description', trans('admin.product_description'));
        $show->field('format', trans('admin.format'))->using([
            0 => '未選択',
            1 => '固定',
            2 => 'オークション',
        ]);

        if ($row->format == 1) {
            $show->field('fixed_listing_end_at', trans('admin.fixed_listing_end_at'))->format('YYYY-MM-DD');
            $show->field('fixed_listing_start_at', trans('admin.fixed_listing_start_at'))->format('YYYY-MM-DD');
            $show->field('fixed_buyout_price', trans('admin.fixed_buyout_price'))->min(0);
            $show->field('is_fixed_negotiable_price', trans('admin.is_fixed_negotiable_price'))->min(0);
            $show->field('fixed_negotiable_price_message', trans('admin.fixed_negotiable_price_message'));
            $show->field('fixed_quantity', trans('admin.fixed_quantity'))->min(0);
        } else if ($row->format == 2) {
            $show->field('auction_listing_end_at', trans('admin.auction_listing_end_at'))->format('YYYY-MM-DD');
            $form->field('auction_listing_start_at', trans('admin.auction_listing_start_at'))->format('YYYY-MM-DD');
            $form->field('auction_start_price', trans('admin.auction_start_price'))->min(0);
            $form->field('auction_buyout_price', trans('admin.auction_buyout_price'))->min(0);
            //$form->field('auction_lowest_price',trans('admin.auction_lowest_price'))->min(0);
            $form->field('is_auction_negotiable_price', trans('admin.is_auction_negotiable_price'))->min(0);
            $form->field('auction_negotiable_price_message', trans('admin.auction_negotiable_price_message'));
            $form->field('auction_quantity', trans('admin.auction_quantity'))->min(0);
        }

        $show->field('is_private_listing', trans('admin.is_private_listing'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('is_charity', trans('admin.is_charity'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('tax', trans('admin.tax'));
        $show->field('payment_rule', trans('admin.payment_rule'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('delivery_rule', trans('admin.delivery_rule'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('return_rule', trans('admin.return_rule'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('product_size', trans('admin.product_size'));
        $show->field('product_weight', trans('admin.product_weight'));
        $show->field('delivery_source_area', trans('admin.delivery_source_area'));


        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $ExhibitTemplate = new ExhibitTemplate();
        $form = new Form($ExhibitTemplate);


        if ($form->isEditing() && (Admin::user()->isRole('member') || Admin::user()->isRole('NPmember'))) {
            $template_id = request()->route()->parameter('template');
            $template = \App\Models\ExhibitTemplate::find($template_id);

            if ($template && $template->admin_user_id != Admin::user()->id) {
                return redirect('/admin');
            }
        }



        $form->hidden('admin_user_id')->default(Admin::user()->id);

        $form->text('title', trans('admin.template_title'))->help(trans('admin.help.manual.templates'));

        $form->ckeditor('display_product_description', trans('admin.display_product_description'))->help(trans('admin.help.display_product_description'));

        $form->radio('format', trans('admin.format'))->options([
            1 => '固定',
            2 => 'オークション',
        ])->when(1, function (Form $form2) use ($form) {
            $form2->select('fixed_listing_end_at', trans('admin.fixed_listing_end_at'))->options([
                //'DAYS_1' => '1日',
                //'DAYS_3' => '3日',
                //'DAYS_5' => '5日',
                //'DAYS_7' => '7日',
                //'DAYS_10' => '10日',
                //'DAYS_21' => '21日',
                //'DAYS_30' => '30日',
                'GTC' => '無期限',
            ]);
            //$form2->date('fixed_listing_start_at',trans('admin.fixed_listing_start_at'))->format('YYYY-MM-DD');
            $form2->currency('fixed_buyout_price', trans('admin.fixed_buyout_price'))->digits(2)->symbol('$');

            // APIに交渉許可ない
            // $form->select('is_fixed_negotiable_price',trans('admin.is_fixed_negotiable_price'))->options([0 => '許可しない',1 => '許可する']);
            // $form->text('fixed_negotiable_price_message',trans('admin.fixed_negotiable_price_message'));
            $form2->number('fixed_quantity', trans('admin.fixed_quantity'))->min(1)->default(1);

            $form2->radio('best_offer_enabled', trans('admin.best_offer_enabled'))->options([
                false => 'ベストオファーを許可しない',
                true => 'ベストオファーを許可する',
            ])->when(true, function (Form $form3) use ($form) {
                $form3->radio('is_auto_accept_price', trans('admin.auto_accept_price'))->options([
                    false => '自動承認しない',
                    true => '自動承認する',
                ])->when(true, function (Form $form4) use ($form) {
                    $form4->currency('auto_accept_price', trans('admin.auto_accept_price'))->digits(2)->symbol('$');
                })->help(trans('admin.help.auto_accept_price'));
                $form3->radio('is_auto_decline_price', trans('admin.auto_decline_price'))->options([
                    false => '自動拒否しない',
                    true => '自動拒否する',
                ])->when(true, function (Form $form4) use ($form) {
                    $form4->currency('auto_decline_price', trans('admin.auto_decline_price'))->digits(2)->symbol('$');
                })->help(trans('admin.help.auto_decline_price'));
            })->default(false);
            //$form->grossProfit('gross_profit1','粗利');
        })->when(2, function (Form $form) {
            $form->select('auction_listing_end_at', trans('admin.auction_listing_end_at'))->options([
                'DAYS_1' => '1日',
                'DAYS_3' => '3日',
                'DAYS_5' => '5日',
                'DAYS_7' => '7日',
                'DAYS_10' => '10日',
                //'DAYS_21' => '21日',
                //'DAYS_30' => '30日',
            ]);
            //$form->date('auction_listing_start_at',trans('admin.auction_listing_start_at'))->format('YYYY-MM-DD');
            $form->currency('auction_start_price', trans('admin.auction_start_price'))->digits(2)->symbol('$');
            $form->currency('auction_buyout_price', trans('admin.auction_buyout_price'))->digits(2)->symbol('$');
            $form->hidden('auction_lowest_price')->default(0);
            //$form->currency('auction_lowest_price',trans('admin.auction_lowest_price'))->digits(2)->symbol('$');

            // APIに交渉許可ない
            //$form->select('is_auction_negotiable_price',trans('admin.is_auction_negotiable_price'))->options([0 => '許可しない',1 => '許可する']);
            //$form->text('auction_negotiable_price_message',trans('admin.auction_negotiable_price_message'));
            $form->number('auction_quantity', trans('admin.auction_quantity'))->min(1)->default(1);

            //$form->grossProfit('gross_profit2','粗利');
        })->default(1);

        // 自分のテンプレでない場合＝メンバーじゃないばあい
        $is_policy_enable = true;
        if (!Admin::user()->isRole('member')) {
            $form->hidden('fullfilment_policy_id')->default(0);
            $form->hidden('payment_policy_id')->default(0);
            $form->hidden('return_policy_id')->default(0);
            $form->hidden('merchant_location_key')->default(null);
        } else {
            $form->select('fullfilment_policy_id', trans('admin.fullfilment_policy_id'))->options($this->getFulfillmentPolicy('EBAY_US'));
            $form->select('payment_policy_id', trans('admin.payment_policy_id'))->options($this->getPaymentPolicy('EBAY_US'));
            $form->select('return_policy_id', trans('admin.return_policy_id'))->options($this->getReturnPolicy('EBAY_US'));
            $form->select('merchant_location_key', trans('admin.merchant_location_key'))->options($this->getMearchantLocation('EBAY_US'));

            $form->button(trans('admin.reload_merchant_location_key'))->on('click', <<<JS
$('select[name=merchant_location_key]').empty();
$.ajax({
	type: 'GET',
	url: '/admin/exhibits/merchant_location_keys',
}).done(function(result){
	$.each(result.data,function(idx,row){
		$('select[name=merchant_location_key]').append($('<option>').attr({value: idx}).text(row));
	});
	$('select[name=merchant_location_key]').select2({"allowClear":false});
});
JS);
            $form->button(trans('admin.view_merchant_location_key'))->on('click', <<<JS
window.open('/admin/exhibits/merchant_location_key',null,'width=600,height=500');
JS);
        }

        $form->footer(function ($footer) {
            $footer->disableReset();
        });

        return $form;
    }

    private function getFulfillmentPolicy($marketplace_id)
    {
        //return ['0' => 'ポリシー未選択'];

        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/account/v1/fulfillment_policy?marketplace_id=' . $marketplace_id, [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        if (!isset($obj["fulfillmentPolicies"])) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        $temp = ['0' => 'ポリシー未選択'];;
        foreach ($obj["fulfillmentPolicies"] as $row) {
            if (isset($row["name"]) && $row["marketplaceId"] == $marketplace_id) {
                $temp[$row['fulfillmentPolicyId']] = $row['name'];
            }
        }
        return $temp;
    }

    private function getPaymentPolicy($marketplace_id)
    {
        //return ['0' => 'ポリシー未選択'];

        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/account/v1/payment_policy?marketplace_id=' . $marketplace_id, [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        if (!isset($obj["paymentPolicies"])) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        $temp = ['0' => 'ポリシー未選択'];;
        foreach ($obj["paymentPolicies"] as $row) {
            if (isset($row["name"]) && $row["marketplaceId"] == $marketplace_id) {
                $temp[$row['paymentPolicyId']] = $row['name'];
            }
        }
        return $temp;
    }

    private function getReturnPolicy($marketplace_id)
    {

        //return ['0' => 'ポリシー未選択'];

        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/account/v1/return_policy?marketplace_id=' . $marketplace_id, [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        if (!isset($obj["returnPolicies"])) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        $temp = ['0' => 'ポリシー未選択'];;
        foreach ($obj["returnPolicies"] as $row) {
            if (isset($row["name"]) && $row["marketplaceId"] == $marketplace_id) {
                $temp[$row['returnPolicyId']] = $row['name'];
            }
        }
        return $temp;
    }

    private function getMearchantLocation($marketplace_id)
    {
        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/inventory/v1/location', [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return ['' => 'ロケーション未選択'];
        }
        if (!isset($obj["locations"])) {
            // 失敗？
            return ['' => 'ロケーション未選択'];
        }
        $temp = ['' => 'ロケーション未選択'];
        foreach ($obj["locations"] as $row) {
            if (isset($row["merchantLocationKey"])) {
                $temp[$row['merchantLocationKey']] = $row['merchantLocationKey'];
            }
        }
        return $temp;
    }
}

<?php

namespace App\Admin\Controllers;

use Encore\Admin\Auth\Database\Role;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

use Encore\Admin\Auth\Database\Administrator;

class AdminUserController extends AdminController
{
    /**
     * {@inheritdoc}
     */
    protected function title()
    {
        return trans('admin.manager_user');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $userModel = config('admin.database.admin_model');

        $grid = new Grid(new $userModel());
        $grid->column('id', 'ID')->sortable();
        $grid->column('username', trans('admin.username'));
        $grid->column('name', trans('admin.name'));
        $grid->column('permission_type', trans('admin.permission_type'))->display(function($id){
			return Role::find($id)->name;
		});
        //$grid->column('permissions', trans('admin.permission'))->pluck('name')->label();
        $grid->column('created_at', trans('admin.created_at'));
        $grid->column('updated_at', trans('admin.updated_at'));
		
		$grid->filter(function($filter){
			$roleModel = config('admin.database.roles_model');
			
			$filter->like('username',trans('admin.username'));
			$filter->like('name',trans('admin.name'));
			$filter->between('created_at',trans('admin.created_at'))->datetime();
			$filter->in('permission_type',trans('admin.permission_type'))->checkbox($roleModel::whereIn('id',[1,2])->pluck('name', 'id'));
		});

        $grid->actions(function (Grid\Displayers\Actions $actions) {
            if ($actions->getKey() == 1) {
                $actions->disableDelete();
            }
        });

        $grid->tools(function (Grid\Tools $tools) {
            $tools->batch(function (Grid\Tools\BatchActions $actions) {
                //$actions->disableDelete();
            });
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        $userModel = config('admin.database.admin_model');

        $show = new Show($userModel::findOrFail($id));

        $show->field('id', 'ID');
        $show->field('username', trans('admin.username'));
        $show->field('name', trans('admin.name'));
		$show->field('permission_type', trans('admin.permission_type'))->as(function($id){
			return Role::find($id)->name;
		});
        //$show->field('roles', trans('admin.roles'))->as(function ($roles) {
        //    return $roles->pluck('name');
        //})->label();
        //$show->field('permissions', trans('admin.permissions'))->as(function ($permission) {
        //    return $permission->pluck('name');
        //})->label();
        $show->field('created_at', trans('admin.created_at'));
        $show->field('updated_at', trans('admin.updated_at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    public function form()
    {
        $userModel = config('admin.database.admin_model');
        $permissionModel = config('admin.database.permissions_model');
        $roleModel = config('admin.database.roles_model');

        $form = new Form(new $userModel());

        $userTable = config('admin.database.users_table');
        $connection = config('admin.database.connection');

        $form->display('id', 'ID');
        $form->email('username', trans('admin.username'))
            ->creationRules(['required', "unique:{$connection}.{$userTable}"])
            ->updateRules(['required', "unique:{$connection}.{$userTable},username,{{id}}"]);

        $form->text('name', trans('admin.name'))->rules('required');
        $form->image('avatar', trans('admin.avatar'));
        $form->password('password', trans('admin.password'))->rules('required|confirmed');
        $form->password('password_confirmation', trans('admin.password_confirmation'))->rules('required')
            ->default(function ($form) {
                return $form->model()->password;
            });

        $form->ignore(['password_confirmation']);

        $form->select('roles', trans('admin.roles'))
			->options($roleModel::whereIn('id',[1,2])->pluck('name', 'id'))
			->rules('required')
			->default(function($form){
				if($form->model()->permission_type){
					return $form->model()->permission_type;
				} else {
					return null;
				}
			});

        $form->display('created_at', trans('admin.created_at'));
        $form->display('updated_at', trans('admin.updated_at'));

        $form->saving(function (Form $form) {
			$form->model()->permission_type = $form->roles;
			if ($form->password && $form->model()->password != $form->password) {
                $form->password = Hash::make($form->password);
            }
        });
		
		$form->saved(function(Form $form){
			DB::table('admin_role_users')->where('user_id',$form->model()->id)->delete();
			Administrator::where('id',$form->model()->id)->first()->roles()->save(Role::where('id',$form->model()->permission_type)->first());
		});

        return $form;
    }
}

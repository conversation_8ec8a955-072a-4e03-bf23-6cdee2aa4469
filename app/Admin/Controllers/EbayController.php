<?php

namespace App\Admin\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Services\EbayTrialService;
use GuzzleHttp\Client as GuzzleClient;

class EbayController
{
    protected $ebayTrialService;

    public function __construct(EbayTrialService $ebayTrialService)
    {
        $this->ebayTrialService = $ebayTrialService;
    }

    /**
     * eBay連携前の事前チェック（ユーザー情報取得）
     */
    public function preCheckEbayAccount(Request $request)
    {
        $user = Auth::guard('admin')->user();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 401);
        }

        $authCode = $request->input('auth_code');
        if (!$authCode) {
            return response()->json(['status' => 'error', 'message' => 'Authorization code is required'], 400);
        }

        try {
            // 一時的にトークンを取得してユーザー情報を確認
            $ebayUserInfo = $this->getEbayUserInfoFromAuthCode($authCode);

            if (!$ebayUserInfo) {
                return response()->json(['status' => 'error', 'message' => 'Failed to get eBay user info'], 400);
            }

            $ebayId = $ebayUserInfo['userId'];
            $ebayUsername = $ebayUserInfo['username'];

            // 重複チェック
            $isUsedBefore = $this->ebayTrialService->isEbayAccountUsedBefore($ebayId, $user->id);

            return response()->json([
                'status' => 'success',
                'ebay_id' => $ebayId,
                'ebay_username' => $ebayUsername,
                'is_returning_ebay' => $isUsedBefore,
                'will_cancel_trial' => $isUsedBefore && $user->member_type == 1
            ]);

        } catch (\Exception $e) {
            Log::error('Error in pre-check eBay account', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['status' => 'error', 'message' => 'Internal error'], 500);
        }
    }

    /**
     * eBay認証コードからユーザー情報を取得（一時的）
     */
    private function getEbayUserInfoFromAuthCode($authCode)
    {
        try {
            // トークン取得
            $base64encoded = base64_encode(config('ebay.client_id') . ":" . config('ebay.client_secret'));
            $client = new GuzzleClient();

            $response = $client->request('POST', config('ebay.api_endpoint') . '/identity/v1/oauth2/token', [
                'headers' => [
                    'Authorization' => ['Basic ' . $base64encoded],
                    'Content-Type' => ['application/x-www-form-urlencoded'],
                ],
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'code' => $authCode,
                    'redirect_uri' => config('ebay.ru_name'),
                ]
            ]);

            $response_body = $response->getBody()->getContents();
            $obj_response = json_decode($response_body);

            if (!isset($obj_response->access_token)) {
                return null;
            }

            $ebayAccessToken = $obj_response->access_token;

            // ユーザー情報取得
            $userResponse = $client->request('GET', config('ebay.apiz_endpoint') . '/commerce/identity/v1/user/', [
                'http_errors' => false,
                'headers' => [
                    'Authorization' => ['Bearer ' . $ebayAccessToken],
                    'Accept' => ['application/json'],
                    'Content-Type' => ['application/json'],
                    'Content-Language' => ['en-US'],
                ],
            ]);

            $userResponseBody = $userResponse->getBody()->getContents();
            $userObj = json_decode($userResponseBody, true);

            if ($userResponse->getStatusCode() != 200 || !isset($userObj["userId"])) {
                return null;
            }

            return [
                'userId' => $userObj["userId"],
                'username' => $userObj["username"],
                'access_token' => $ebayAccessToken,
                'token_data' => $obj_response
            ];

        } catch (\Exception $e) {
            Log::error('Error getting eBay user info from auth code', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * eBay連携確定処理（警告確認後）
     */
    public function confirmEbayConnection(Request $request)
    {
        $user = Auth::guard('admin')->user();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 401);
        }

        $authCode = $request->input('auth_code');
        $confirmedTrialCancellation = $request->input('confirmed_trial_cancellation', false);

        if (!$authCode) {
            return response()->json(['status' => 'error', 'message' => 'Authorization code is required'], 400);
        }

        try {
            // eBayユーザー情報とトークン取得
            $ebayUserInfo = $this->getEbayUserInfoFromAuthCode($authCode);

            if (!$ebayUserInfo) {
                return response()->json(['status' => 'error', 'message' => 'Failed to get eBay user info'], 400);
            }

            $ebayId = $ebayUserInfo['userId'];
            $ebayUsername = $ebayUserInfo['username'];
            $tokenData = $ebayUserInfo['token_data'];

            // 重複チェック
            $isUsedBefore = $this->ebayTrialService->isEbayAccountUsedBefore($ebayId, $user->id);

            // 重複があり、無料期間をキャンセルする場合の確認
            if ($isUsedBefore && $user->member_type == 1 && !$confirmedTrialCancellation) {
                return response()->json([
                    'status' => 'confirmation_required',
                    'message' => 'This eBay account has been used before. Trial will be cancelled.',
                    'ebay_username' => $ebayUsername
                ]);
            }

            // 既存のeBayアカウント重複チェック（他のユーザーでの使用）
            $existingUser = DB::table('admin_users')
                ->where('id', '<>', $user->id)
                ->where('ebay_id', $ebayId)
                ->where('permission_type', 3)
                ->first();

            if ($existingUser && $user->id != 1) {
                return response()->json([
                    'status' => 'error',
                    'message' => '既に登録されているeBayアカウントです。'
                ]);
            }

            // トランザクション開始
            DB::beginTransaction();

            try {
                // eBay使用履歴を記録
                $this->ebayTrialService->recordEbayUsage($ebayId, $ebayUsername, $user->id);

                // 過去使用による無料期間キャンセル処理
                if ($isUsedBefore && $user->member_type == 1) {
                    $this->ebayTrialService->cancelTrialDueToEbayDuplication($ebayId, $user->id);
                }

                // ユーザー情報更新
                DB::table('admin_users')->where('id', $user->id)->update([
                    'oauth_code' => $authCode,
                    'access_token' => $tokenData->access_token,
                    'access_token_expires_in' => date('Y-m-d H:i:s', strtotime("now +" . $tokenData->expires_in . "seconds")),
                    'refresh_token' => $tokenData->refresh_token,
                    'refresh_token_expires_in' => date('Y-m-d H:i:s', strtotime("now +" . $tokenData->refresh_token_expires_in . "seconds")),
                    'ebay_id' => $ebayId,
                    'ebay_username' => $ebayUsername,
                    'updated_at' => now(),
                ]);

                // 権限更新（有料会員の場合）
                if ($user->member_type == 1) {
                    $roleUser = DB::table('admin_role_users')->where('user_id', $user->id)->first();
                    if ($roleUser && !in_array($roleUser->role_id, [1, 2])) {
                        DB::table('admin_role_users')->where('user_id', $user->id)->update([
                            'role_id' => 3,
                            'updated_at' => now(),
                        ]);
                    }
                }

                DB::commit();

                Log::info('eBay connection confirmed successfully', [
                    'user_id' => $user->id,
                    'ebay_id' => $ebayId,
                    'trial_cancelled' => $isUsedBefore && $user->member_type == 1
                ]);

                return response()->json([
                    'status' => 'success',
                    'message' => 'eBay連携が完了しました。',
                    'trial_cancelled' => $isUsedBefore && $user->member_type == 1
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Error confirming eBay connection', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['status' => 'error', 'message' => 'Internal error'], 500);
        }
    }

    /**
     * eBay連携時の無料期間への影響をチェック
     */
    public function checkTrialImpact(Request $request)
    {
        $user = Auth::guard('admin')->user();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 401);
        }

        $ebayId = $request->input('ebay_id');
        if (!$ebayId) {
            return response()->json(['status' => 'error', 'message' => 'eBay ID is required'], 400);
        }

        try {
            // 過去に使用されているかチェック
            $isUsedBefore = $this->ebayTrialService->isEbayAccountUsedBefore($ebayId, $user->id);

            if ($isUsedBefore && $user->member_type == 1) {
                // 有料会員で過去に使用済みeBayアカウントの場合、無料期間をキャンセル
                $this->ebayTrialService->cancelTrialDueToEbayDuplication($ebayId, $user->id);

                Log::info('Trial cancelled due to eBay duplication', [
                    'user_id' => $user->id,
                    'ebay_id' => $ebayId
                ]);

                return response()->json([
                    'status' => 'trial_cancelled',
                    'message' => 'eBayアカウントが過去に使用されているため、無料期間が終了されました。'
                ]);
            }

            return response()->json([
                'status' => 'success',
                'is_returning_ebay' => $isUsedBefore
            ]);
        } catch (\Exception $e) {
            Log::error('Error checking eBay trial impact', [
                'user_id' => $user->id,
                'ebay_id' => $ebayId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['status' => 'error', 'message' => 'Internal error'], 500);
        }
    }

    /**
     * 現在のeBay連携の無料期間への影響ステータスを取得
     */
    public function getTrialImpactStatus(Request $request)
    {
        $user = Auth::guard('admin')->user();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 401);
        }

        $ebayId = $request->input('ebay_id');
        if (!$ebayId) {
            return response()->json(['status' => 'error', 'message' => 'eBay ID is required'], 400);
        }

        try {
            $isUsedBefore = $this->ebayTrialService->isEbayAccountUsedBefore($ebayId, $user->id);

            return response()->json([
                'status' => 'success',
                'is_returning_ebay' => $isUsedBefore
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting eBay trial impact status', [
                'user_id' => $user->id,
                'ebay_id' => $ebayId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['status' => 'error', 'message' => 'Internal error'], 500);
        }
    }

    /**
     * eBay連携を解除
     */
    public function disconnect(Request $request)
    {
        $user = Auth::guard('admin')->user();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 401);
        }

        try {
            // eBay関連情報をクリア
            DB::table('admin_users')->where('id', $user->id)->update([
                'oauth_code' => '',
                'access_token' => null,
                'access_token_expires_in' => null,
                'refresh_token' => null,
                'ebay_id' => null,
                'ebay_username' => null,
                'refresh_token_expires_in' => null,
                'updated_at' => now(),
            ]);

            Log::info('eBay connection disconnected', ['user_id' => $user->id]);

            return response()->json([
                'status' => 'success',
                'message' => 'eBay連携を解除しました。'
            ]);
        } catch (\Exception $e) {
            Log::error('Error disconnecting eBay', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['status' => 'error', 'message' => 'Internal error'], 500);
        }
    }

    /**
     * ユーザー情報を取得（eBay連携後の情報更新用）
     */
    public function getUserInfo(Request $request)
    {
        $user = Auth::guard('admin')->user();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 401);
        }

        try {
            // 最新のユーザー情報を取得
            $freshUser = DB::table('admin_users')->where('id', $user->id)->first();

            return response()->json([
                'status' => 'success',
                'ebay_id' => $freshUser->ebay_id,
                'ebay_username' => $freshUser->ebay_username,
                'member_type' => $freshUser->member_type
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting user info', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['status' => 'error', 'message' => 'Internal error'], 500);
        }
    }
}

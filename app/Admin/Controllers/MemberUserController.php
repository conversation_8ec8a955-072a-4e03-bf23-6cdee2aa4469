<?php

namespace App\Admin\Controllers;

use Encore\Admin\Auth\Database\Role;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

use Encore\Admin\Auth\Database\Administrator;

class MemberUserController extends AdminController
{
    /**
     * {@inheritdoc}
     */
    protected function title()
    {
        return trans('admin.member_user');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $userModel = config('admin.database.member_model');

        $grid = new Grid(new $userModel());
		
		//$grid->model()->where('roles',2);

        // エクスポート時にaタグなどが出力されてしまうための対応
        $grid->export(function ($export) {
            $export->column('id', function ($value) {
                return strip_tags($value);
            });
            $export->column('username', function ($value) {
                return strip_tags($value);
            });
        });

        $grid->column('id', 'ID')->sortable()->link(function($row){
			return route('admin.member.edit', $row->id);
		},'');

        $grid->column('username', trans('admin.username'))->link(function($row){
			return route('admin.member.edit', $row->id);
		},'');

        $grid->column('name', trans('admin.name'));
		$grid->column('member_type', trans('admin.member_type'))->display(function($id){
			if($id==1){
				return trans('admin.paid');
			} else {
				return trans('admin.free');
			}
		});
        $grid->column('created_at', trans('admin.created_at'));
        $grid->column('updated_at', trans('admin.updated_at'));
		
		$grid->filter(function($filter){
			$filter->like('username',trans('admin.username'));
			$filter->like('name',trans('admin.name'));
			$filter->between('created_at',trans('admin.created_at'))->datetime();
			$filter->in('member_type',trans('admin.member_type'))->checkbox([0 => trans('admin.free'),1 => trans('admin.paid')]);
		});

        $grid->actions(function (Grid\Displayers\Actions $actions) {
            if ($actions->getKey() == 1) {
                $actions->disableDelete();
            }
        });

        $grid->tools(function (Grid\Tools $tools) {
            $tools->batch(function (Grid\Tools\BatchActions $actions) {
                //$actions->disableDelete();
            });
        });
		
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        $userModel = config('admin.database.member_model');

        $show = new Show($userModel::findOrFail($id));

        $show->field('id', 'ID');
        $show->field('username', trans('admin.username'));
        $show->field('name', trans('admin.name'));
		
		$show->field('member_type', trans('admin.member_type'))->as(function($id){
			if($id==1){
				return trans('admin.paid');
			} else {
				return trans('admin.free');
			}
		});
		$show->field('verified', trans('admin.is_verified'))->as(function($id){
			if($id==1){
				return trans('admin.verified');
			} else {
				return trans('admin.unverified');
			}
		});
		
        $show->field('created_at', trans('admin.created_at'));
        $show->field('updated_at', trans('admin.updated_at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    public function form()
    {
        $userModel = config('admin.database.member_model');

        $form = new Form(new $userModel());

        $userTable = config('admin.database.users_table');
        $connection = config('admin.database.connection');

        $form->display('id', 'ID');
        $form->email('username', trans('admin.username'))
            ->creationRules(['required', "unique:{$connection}.{$userTable}"])
            ->updateRules(['required', "unique:{$connection}.{$userTable},username,{{id}}"]);

        $form->text('name', trans('admin.name'))->rules('required');
        $form->image('avatar', trans('admin.avatar'));
		
		$form->switch('member_type', trans('admin.member_type'))->states([
			'on'  => ['value' => 1, 'text' => trans('admin.paid'), 'color' => 'success'],
			'off' => ['value' => 0, 'text' => trans('admin.free'), 'color' => 'danger'],
		]);
		$form->switch('verified', trans('admin.is_verified'))->states([
			'on'  => ['value' => 1, 'text' => trans('admin.verified'), 'color' => 'success'],
			'off' => ['value' => 0, 'text' => trans('admin.unverified'), 'color' => 'danger'],
		]);
		//$form->select('member_type', trans('admin.member_type'))
		//	->options([0 => trans('admin.free'),1 => trans('admin.paid')])
		//	->rules('required');
		
        $form->password('password', trans('admin.password'))->rules('required|confirmed');
        $form->password('password_confirmation', trans('admin.password_confirmation'))->rules('required')
            ->default(function ($form) {
                return $form->model()->password;
            });

        $form->ignore(['password_confirmation']);

		// ロールはMember固定
		$form->hidden('roles')->default(3);

        $form->display('created_at', trans('admin.created_at'));
        $form->display('updated_at', trans('admin.updated_at'));

        $form->saving(function (Form $form) {
			$form->model()->permission_type = 3;
            if ($form->password && $form->model()->password != $form->password) {
                $form->password = Hash::make($form->password);
            }
        });
		
		$form->saved(function(Form $form){
			DB::table('admin_role_users')->where('user_id',$form->model()->id)->delete();
			Administrator::where('id',$form->model()->id)->first()->roles()->save(Role::where('id',$form->model()->permission_type)->first());
		});

        return $form;
    }
}

<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AuthController as BaseAuthController;

use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Psr7;
use App\Services\EbayTrialService;

class AuthController extends BaseAuthController
{
    protected $ebayTrialService; // この行を追加

    public function __construct(EbayTrialService $ebayTrialService) // このコンストラクタを追加
    {
        $this->ebayTrialService = $ebayTrialService;
    }

    public function getLogin()
    {
        if ($this->guard()->check()) {
            return redirect($this->redirectPath());
        }

        return view('admin.auth.login');
    }

    public function postLogin(Request $request)
    {
        $this->loginValidator($request->all())->validate();

        $credentials = $request->only([$this->username(), 'password']);
        $remember = $request->get('remember', false);

        if ($this->guard()->attempt($credentials, $remember)) {
            if (Admin::user()->verified != 1) {
                // 本登録していない人はエラー扱い
                $this->guard()->logout();
                $request->session()->invalidate();
                return back()->withInput()->withErrors([
                    $this->username() => trans('auth.validregist'),
                ]);
            } else {
                return $this->sendLoginResponse($request);
            }
        }

        return back()->withInput()->withErrors([
            $this->username() => $this->getFailedLoginMessage(),
        ]);
    }

    // ログイン後のリダイレクト先を設定
    protected function redirectTo()
    {
        if (Admin::user()->member_type == 1 && Admin::user()->ebay_id) {
            return '/admin/exhibits/create';
        } else {
            return '/admin';
        }
    }

    public function settingForm()
    {
        $class = config('admin.database.users_model');

        $form = new Form(new $class());

        $form->display('username', trans('admin.username'));
        $form->text('name', trans('admin.name'))->rules('required');
        //$form->image('avatar', trans('admin.avatar'));

        if (Admin::user()->isRole('member') && Admin::user()->member_type == 1 && Admin::user()->ebay_id) {

            //$form->text('ebay_client_id','ebay Client ID')->rules('required');
            //$form->text('ebay_ru_name','RuName(eBay Redirect URL name)')->rules('required');
            //$form->text('ebay_client_secret','ebay Client Secret')->rules('required');

            // 各Ebay側の情報入っているかチェック
            $url_query = [
                'client_id' => config('ebay.client_id'),
                'response_type' => 'code',
                'redirect_uri' => config('ebay.ru_name'),
                'scope' => 'https://api.ebay.com/oauth/api_scope https://api.ebay.com/oauth/api_scope/sell.inventory https://api.ebay.com/oauth/api_scope/sell.account https://api.ebay.com/oauth/api_scope/sell.fulfillment https://api.ebay.com/oauth/api_scope/commerce.identity.readonly',
            ];

            $url = config('ebay.auth_endpoint') . '/oauth2/authorize?' . http_build_query($url_query);
            $js = "window.open('{$url}','','width=500,height=400');return false;";
            // OAuthを通す
            $form->button('oauth_button', 'eBay連携')->on('click', $js);

            //if(Admin::user()->access_token){
            //	$js = "";
            //	$js.= "let res = window.confirm('eBay販売者プログラムの状態を確認しますか？');";
            //	$js.= " if(res == true){ ";
            //	$js.= " $.ajax({ ";
            //	$js.= " url:'/admin/auth/checkprogram', ";
            //	$js.= " type: 'GET', ";
            //	$js.= " }).done(function(){ alert('確認しました'); window.reload();}).fail(function(){alert('確認できませんでした。販売者プログラムの設定を確認してください。');});";
            //	$js.= " } ";
            //
            //	$form->button('eBay販売プログラム確認')->on('click',$js);
            //}
        }

        $form->password('password', trans('admin.password'))->rules('confirmed|required');
        $form->password('password_confirmation', trans('admin.password_confirmation'))->rules('required')
            ->default(function ($form) {
                return $form->model()->password;
            });
        $email = urlencode(Admin::user()->username);
        $form->button('お問い合わせ')->on('click', "window.open('/contact?email={$email}','_blank');");

        $form->setAction(admin_url('auth/setting'));

        $form->ignore(['password_confirmation']);

        $form->saving(function (Form $form) {
            if ($form->password && $form->model()->password != $form->password) {
                $form->password = Hash::make($form->password);
            }
        });

        $form->saved(function () {
            // 販売プログラムの確認をする
            $client = null;
            $client = new GuzzleClient();
            $response = $client->request('POST', config('ebay.api_endpoint') . '/sell/account/v1/program/opt_in', [
                'http_errors' => false,
                'headers' => [
                    'Authorization' => [
                        'Bearer ' . Admin::user()->access_token,
                    ],
                    'Accept' => ['application/json'],
                    'Content-Type' => ['application/json'],
                ],
                'json' => [
                    'programType' => 'SELLING_POLICY_MANAGEMENT',
                ],
            ]);
            $class = config('admin.database.users_model');
            $userModel = new $class;
            if ($response->getStatusCode() == 200) {

                $user = $userModel->where('id', Admin::user()->id)->first();
                $user->is_policy = 1;
                $user->save();
            } else {
                $response_body = $response->getBody()->getContents();
                $obj = json_decode($response_body, true);
                if (isset($obj["errors"]) && isset($obj["errors"][0]) && isset($obj["errors"][0]["errorId"]) && $obj["errors"][0]["errorId"] == 20500) {
                    $user = $userModel->where('id', Admin::user()->id)->first();
                    $user->is_policy = 1;
                    $user->save();
                }
            }

            admin_toastr(trans('admin.update_succeeded'));

            return redirect(admin_url('auth/setting'));
        });

        return $form;
    }

    function checkProgram()
    {
        $ebay_token = Admin::user()->access_token;
        if (!$ebay_token) {
            // 失敗？
            return response()->json(['get' => $_POST], 401);
        }

        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/account/v1/program/get_opted_in_programs', [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . $ebay_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);
        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return response()->json([
                $obj
            ], $response->getStatusCode());
        }


        if (!isset($obj["programs"])) {
            // 失敗？
            return response()->json([
                $obj
            ], 403);
        }
        $isOK = false;
        foreach ($obj["programs"] as $row) {
            if (isset($row["programType"]) && $row["programType"] == "SELLING_POLICY_MANAGEMENT") {
                $isOK = true;
                break;
            }
        }
        if (!$isOK) {
            // 失敗？
            return response()->json([
                $obj
            ], 403);
        }

        return Response()->json();
    }

    public function oauthRedirectSuccess()
    {
        $class = config('admin.database.users_model');
        $userModel = new $class;

        try {
            $user = $userModel->where('id', Admin::user()->id)->first();
            $user->oauth_code = (isset($_GET["code"]) ? $_GET["code"] : null);
            $user->oauth_expires_in = (isset($_GET["expires_in"]) ? date('Y-m-d H:i:s', strtotime("now +" . $_GET["expires_in"] . "seconds")) : null);
            $user->save();

            // トークン取得
            $base64encoded = base64_encode(config('ebay.client_id') . ":" . config('ebay.client_secret'));
            $client = new GuzzleClient();
            $response = $client->request('POST', config('ebay.api_endpoint') . '/identity/v1/oauth2/token', [
                'headers' => [
                    'Authorization' => ['Basic ' . $base64encoded],
                    'Content-Type' => ['application/x-www-form-urlencoded'],
                ],
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'code' => $_GET["code"],
                    'redirect_uri' => config('ebay.ru_name'),
                ]
            ]);

            $response_body = $response->getBody()->getContents();
            $obj_response = json_decode($response_body);

            $ebay_access_token = $obj_response->access_token;

            // ユーザ情報取得
            $client = null;
            $client = new GuzzleClient();
            $response2 = $client->request('GET', config('ebay.apiz_endpoint') . '/commerce/identity/v1/user/', [
                'http_errors' => false,
                'headers' => [
                    'Authorization' => [
                        'Bearer ' . $ebay_access_token,
                    ],
                    'Accept' => ['application/json'],
                    'Content-Type' => ['application/json'],
                    'Content-Language' => ['en-US'],
                ],
            ]);
            $response_body2 = $response2->getBody()->getContents();
            $user_obj = json_decode($response_body2, true);
            if ($response2->getStatusCode() != 200 && !isset($user_obj["userId"])) {
                return view('admin.payment.failed')->with([
                    'message' => 'ユーザ情報の確認に失敗しました。',
                ]);
            }

            $check_ebay = \DB::table('admin_users')->where('id', '<>', Admin::user()->id)->where('ebay_id', $user_obj["userId"])->where('permission_type', 3)->count();
            if ($check_ebay > 0 && Admin::user()->id != 1) {
                return view('admin.payment.failed')->with([
                    'message' => '既に登録されているeBayアカウントです。',
                ]);
            }

            // eBay使用履歴を記録
            $this->ebayTrialService->recordEbayUsage($user_obj["userId"], $user_obj["username"], Admin::user()->id);

            // 過去使用チェックと無料期間への影響確認
            if ($user->member_type == 1) { // 有料会員の場合のみ
                $isUsedBefore = $this->ebayTrialService->isEbayAccountUsedBefore($user_obj["userId"], Admin::user()->id);
                if ($isUsedBefore) {
                    // 過去に使用されている場合、無料期間を終了
                    $this->ebayTrialService->cancelTrialDueToEbayDuplication($user_obj["userId"], Admin::user()->id);
                }
            }

            // 各種情報を更新
            $user->access_token = $obj_response->access_token;
            $user->access_token_expires_in = date('Y-m-d H:i:s', strtotime("now +" . $obj_response->expires_in . "seconds"));
            $user->refresh_token = $obj_response->refresh_token;
            $user->refresh_token_expires_in = date('Y-m-d H:i:s', strtotime("now +" . $obj_response->refresh_token_expires_in . "seconds"));
            $user->ebay_id = $user_obj["userId"];
            $user->ebay_username = $user_obj["username"];
            $user->save();

            if ($user->member_type == 1) {
                $role_user = FacadesDB::table('admin_role_users')->where('user_id', Admin::user()->id)->first();
                if ($role_user && ($role_user->role_id == 1 || $role_user->role_id == 2)) {
                    // 何もしない
                } else {
                    FacadesDB::table('admin_role_users')->where('user_id', Admin::user()->id)->update([
                        'role_id' => 3,
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);
                }

                return view('admin.payment.success');
            }
            // 有料会員フラグが立ってなかったら
            return view('admin.payment.regist');
        } catch (ClientException $e) {
            return view('admin.payment.failed')->with([
                'message' => 'eBayへのログインに失敗しました。'
            ]);
        }
    }

    public function oauthRedirectFailed()
    {
        return view('admin.payment.failed')->with([
            'message' => 'eBayへのログインに失敗しました。'
        ]);
    }

    public function get_user_token(Request $request)
    {
        $class = config('admin.database.users_model');
        $userModel = new $class;
        $user = $userModel->where('id', Admin::user()->id)->first();

        return response()->json([
            'token' => $user->access_token,
        ]);
    }
}

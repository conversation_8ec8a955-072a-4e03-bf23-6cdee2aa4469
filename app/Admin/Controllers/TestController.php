<?php

namespace App\Admin\Controllers;

use Carbon\Carbon;
use App\Models\Admin\Tests;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Layout\Content;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class TestController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Tests';

    protected $get_js_html = <<<'HTMLSTRING'
<link rel="stylesheet" href="/js/get_product_ui.css">
<script src="/js/get_product_ui.js"></script>
HTMLSTRING;

    protected $image_sample_html = <<<'HTMLSTRING'
<div class="sample_image_display_area"></div>
<script>
	setDisplayImage();
</script>
HTMLSTRING;

    protected $get_product_html = <<<'HTMLSTRING'
<div class="get_product_info_area"></div>
<script>
	get_product_info_config['title_set_selecter'] = 'input[name="name"]';
	get_product_info_config['comment_set_selecter'] = 'textarea[name="caption"]';
	get_product_info_config['image_set_selecter'] = '#hidden_image_path';
	get_product_info_config['image_sample_set_selecter'] = '.sample_image_display_area';
	setGetProductUI();
</script>
HTMLSTRING;

    protected $image_uploder_html = <<<'HTMLSTRING'
<div class="get_uploder_area"></div>
<script>
	setUploaderUI();
</script>
HTMLSTRING;


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Tests());

        // フィルタの設定
        $grid->filter(function ($filter) {
            $filter->disableIdFilter();// デフォルトのID検索を非表示
            $filter->between('send_date', trans('admin.tests.id'));
            $filter->like('name', trans('admin.tests.name'));
            $filter->like('caption', trans('admin.tests.caption'));
        });

        // 出力ボタン非表示
        $grid->disableExport();

        $grid->id(trans('admin.tests.id'))->sortable();
        $grid->name(trans('admin.tests.name'))->sortable();
        //$grid->caption(trans('admin.tests.caption'))->sortable();

        $grid->created_at(trans('admin.created_at'))->display(function () {
            return Carbon::parse($this->created_at)->format('Y-m-d H:i:s');
        })->sortable();

        $grid->updated_at(trans('admin.updated_at'))->display(function () {
            return Carbon::parse($this->updated_at)->format('Y-m-d H:i:s');
        })->sortable();

        // 操作部分の設定
        $grid->actions(function ($actions) {
            $actions->disableView();
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Tests::findOrFail($id));
        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Tests());

        $form->display('id', trans('admin.tests.id'));
        $form->html($this->get_product_html);
        $form->text('name', trans('admin.tests.name'))->rules('required|max:200');
        $form->textarea('caption', trans('admin.tests.caption'))->rules('required');
        $form->hidden('image_path')->attribute(['id' => 'hidden_image_path']);//画像はパスをhiddenで保持

        $form->html($this->image_sample_html, trans('admin.tests.image_path'));
        $form->html($this->image_uploder_html);
        return $form;
    }
}

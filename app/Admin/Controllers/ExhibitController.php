<?php

namespace App\Admin\Controllers;

use Illuminate\Http\Request;
use App\Models\Categories;
use App\Models\Exhibit;
use App\Models\ExhibitTemplate;
use App\Admin\Actions\Post\Delete;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Controllers\AdminController;
//use Encore\Admin\Form;
use App\Admin\Extensions\CustomForm as Form;
use Encore\Admin\Form\Tools;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class ExhibitController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '出品情報';


    /*
以下の必要JSは「/srv/app/app/Admin/bootstrap.php」で指定
/js/get_product_ui.css
/js/get_product_ui.js
*/

    //アップロード画像表示エリア
    protected $image_sample_html = <<<'HTMLSTRING'
<div class="sample_image_display_area"></div>
<script>
    setDisplayImage();
</script>
HTMLSTRING;

    //商品情報取得ボタン
    protected $get_product_html = <<<'HTMLSTRING'
<div class="get_product_info_area"></div>
<script>
    get_product_info_config['title_set_selecter'] = 'input[name="title"]';
    get_product_info_config['comment_set_selecter'] = 'textarea[name="product_description"]';
    get_product_info_config['image_set_selecter'] = '#hidden_product_image';
    get_product_info_config['image_sample_set_selecter'] = '.sample_image_display_area';
	get_product_info_config['sku_set_selecter'] = 'input[name="sku"]';
    setGetProductUI();
</script>
HTMLSTRING;

    //画像アップロードボタン
    protected $image_uploder_html = <<<'HTMLSTRING'
<div class="get_uploder_area"></div>
<span class="help-block">
    <i class="fa fa-info-circle"></i>&nbsp;画像の最低要件は１枚以上かつ最長辺が500pxls以上
</span>
<script>
    setUploaderUI();
</script>
HTMLSTRING;

    protected $exhibit_status = [
        0 => '在庫化前',
        1 => '出品前',
        2 => '出品中',
        3 => '販売済み',
        4 => '出品取り消し済み',
    ];

    protected $is_referer_soldout = [
        -1 => '未連携',
        0 => '販売中',
        1 => '終了',
    ];


    protected $condition_enum = [
        1000 => 'NEW',
        1500 => 'NEW_OTHER',
        1750 => 'NEW_WITH_DEFECTS',
        2000 => 'CERTIFIED_REFURBISHED',
        2010 => 'EXCELLENT_REFURBISHED',
        2020 => 'VERY_GOOD_REFURBISHED',
        2030 => 'GOOD_REFURBISHED',
        2500 => 'SELLER_REFURBISHED',
        2750 => 'LIKE_NEW',
        3000 => 'USED_EXCELLENT',
        4000 => 'USED_VERY_GOOD',
        5000 => 'USED_GOOD',
        6000 => 'USED_ACCEPTABLE',
        7000 => 'FOR_PARTS_OR_NOT_WORKING',
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Exhibit());


        // ログインしたユーザのものだけ見える
        if (Admin::user()->isRole('member') || Admin::user()->isRole('NPmember')) {
            $grid->model()->where('admin_user_id', Admin::user()->id);
        }
        $grid->model()->orderBy('id', 'DESC');

        $grid->header(function () {
            return '<span class="help-block"><i class="fa fa-info-circle"></i>' . trans('admin.help.manual.exhibits1') . '</span><span class="help-block"><i class="fa fa-info-circle"></i>' . trans('admin.help.manual.exhibits2') . '</span>';
        });

        $grid->column('id', __('ID'))->display(function ($value, $column) {
            if ($this->status == 0 || $this->status == 1) {
                $route = route('admin.exhibits.edit', $this->id);
                return "<a href='{$route}'>{$value}</a>";
            } else {
                return $value;
            }
        }, '')->sortable();

        if (!Admin::user()->isRole('member') && !Admin::user()->isRole('NPmember')) {
            // member以外は登録したユーザ名表示
            $grid->column('admin_user_id', trans('admin.username'))->link(function ($row) {
                return route('admin.member.edit', $row->admin_user_id);
            }, '')->sortable();
        }
        $grid->column('sku', trans('admin.sku'))->display(function ($value, $column) {
            if (Admin::user()->isRole('administrator')) {
                $route = route('admin.exhibits.edit', $this->id);
                return "<a href='{$route}'>{$value}</a>";
            }
            if ($this->status == 0 || $this->status == 1) {
                $route = route('admin.exhibits.edit', $this->id);
                return "<a href='{$route}'>{$value}</a>";
            } else {
                return $value;
            }
        }, '')->sortable();

        $grid->column('title', trans('admin.display_grid_title'))->display(function ($value, $column) {
            if ($this->status == 0 || $this->status == 1) {
                $route = route('admin.exhibits.edit', $this->id);
                return "<a href='{$route}'>{$this->title} / {$this->display_title}</a>";
            } else {
                return "{$this->title} / {$this->display_title}";
            }
        }, '')->sortable();

        $grid->column('price', trans('admin.price') . "(USD)")->display(function ($row) {
            if ($this->format == 2) {
                return number_format($this->auction_buyout_price, 2);
            } else {
                return number_format($this->fixed_buyout_price, 2);
            }
        });
        $grid->column('referer_price', trans('admin.referer_price') . "(JPY)")->display(function ($row) {
            return number_format($this->referer_price);
        })->sortable();

        $grid->column('referer_id', trans('admin.referer_id'))->display(function ($val) {
            $arr = [
                'yahoo' => 'https://page.auctions.yahoo.co.jp/jp/auction/',
                'mercari' => 'https://jp.mercari.com/item/',
                'amazon' => 'https://www.amazon.co.jp/dp/',
                'ebay' => 'https://www.ebay.com/itm/',
                'jancode' => '',
            ];
            if (preg_match('/https?:\/{2}[\w\/:%#\$&\?\(\)~\.=\+\-]+/', $val)) {
                return "<a href=\"{$val}\" target=\"_blank\">{$val}</a>";
            }
            if ($this->referer_service != null && $val != null && $this->referer_service != "jancode") {
                $url = $arr[$this->referer_service] . $val;
                return "<a href=\"{$url}\" target=\"_blank\">{$val}</a>";
            } else if ($this->referer_service == "jancode") {
                // JANコードの表示処理でも「JAN: 」プレフィックスを除去
                $clean_jancode = $val;
                if (strpos($clean_jancode, 'JAN:') === 0) {
                    $clean_jancode = trim(substr($clean_jancode, 4));
                } else if (strpos($clean_jancode, 'JAN ') === 0) {
                    $clean_jancode = trim(substr($clean_jancode, 4));
                }
                $clean_jancode = preg_replace('/[^0-9]/', '', $clean_jancode);
                return "JANコード:{$clean_jancode}";
            } else {
                return '参照元不明';
            }
        });

        $arr = $this->exhibit_status;
        $grid->column('status', trans('admin.exhibit_status'))->display(function ($val) use ($arr) {
            return isset($arr[$val]) ? $arr[$val] : '';
        })->sortable();

        $grid->column('publish_result', trans('admin.publish_result'))->display(function ($val) {
            $publish_result = json_decode($val, true);
            if (isset($publish_result["listingId"])) {
                return '<a href="https://www.ebay.com/itm/' . $publish_result["listingId"] . '" target="_blank">' . $publish_result["listingId"] . '</a>';
            } else {
                return '';
            }
        });

        $arr2 = $this->is_referer_soldout;
        $grid->column('is_referer_soldout', trans('admin.is_referer_soldout'))->display(function ($val) use ($arr2) {
            $_val = -2;
            if ($this->referer_service == null && $this->referer_id == null) {
                $_val = -1;
            } else if ($this->referer_service != null && $this->referer_id != null && $val == 0) {
                $_val = 0;
            } else if ($this->referer_service != null && $this->referer_id != null && $val == 1) {
                $_val = 1;
            }
            return isset($arr2[$_val]) ? $arr2[$_val] : '';
        });

        $exhibit_status = $this->exhibit_status;
        $is_referer_soldout = $this->is_referer_soldout;
        $grid->export(function ($export) use ($exhibit_status, $is_referer_soldout) {
            $export->originalValue(['id', 'admin_user_id', 'sku', 'title', 'referer_id']);
            $export->column('publish_result', function ($val, $original) {
                $publish_result = json_decode($original, true);
                if (isset($publish_result["listingId"])) {
                    return 'https://www.ebay.com/itm/' . $publish_result["listingId"];
                } else {
                    return '';
                }
            });
            //$export->column('status',function($val,$original) use ($exhibit_status){
            //	return isset($exhibit_status[$original]) ? $exhibit_status[$original] : '';
            //});
        });

        $grid->filter(function ($filter) use ($exhibit_status, $is_referer_soldout) {
            $filter->disableIdFilter();
            $filter->like('sku', trans('admin.sku'));
            $filter->like('title', trans('admin.title'));
            $filter->between('price', trans('admin.price'))->decimal();
            $filter->between('referer_price', trans('admin.referer_price'))->decimal();
            $filter->in('status', trans('admin.exhibit_status'))->checkbox($exhibit_status);
            $filter->in('is_referer_soldout', trans('admin.is_referer_soldout'))->checkbox($is_referer_soldout);
        });

        $grid->actions(function ($actions) {
            $actions->disableDelete();
            $actions->disableView();
            $actions->add(new Delete);
        });

        $grid->tools(function (Grid\Tools $tools) {
            $tools->append('<div class="btn-group pull-right" style="margin-right: 10px"><a href="/admin/exhibits/create" class="btn btn-sm btn-success"><i class="fa fa-plus"></i>&nbsp;&nbsp;新規出品</a></div>');
        });
        $grid->disableCreateButton();

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $row = Exhibit::findOrFail($id);
        $show = new Show($row);

        $show->field('id', 'ID');
        $show->field('title', trans('admin.title'));
        $show->field('category_id', trans('admin.category_id'));
        $show->field('sku', trans('admin.sku'));
        $show->field('stock_quantity', trans('admin.stock_quantity'));
        $show->field('price', trans('admin.price'));
        $show->field('condition', trans('admin.condition'));
        $show->image('product_image', trans('admin.product_image'));
        $show->field('is_scale', trans('admin.is_scale'));
        $show->field('is_advertising', trans('admin.is_advertising'));
        $show->field('color', trans('admin.color'));
        $show->field('ca_prop_65', trans('admin.ca_prop_65'));
        //$show->field('is_lot_sales',trans('admin.is_lot_sales'));
        //$show->field('product_type',trans('admin.product_type'));
        $show->field('production_area', trans('admin.production_area'));
        $show->field('origin_date', trans('admin.origin_date'));
        $show->field('product_description', trans('admin.product_description'));
        $show->field('format', trans('admin.format'))->using([
            0 => '未選択',
            1 => '固定',
            2 => 'オークション',
        ]);

        if ($row->format == 1) {
            $show->field('fixed_listing_end_at', trans('admin.fixed_listing_end_at'))->format('YYYY-MM-DD');
            //$show->field('fixed_listing_start_at',trans('admin.fixed_listing_start_at'))->format('YYYY-MM-DD');
            $show->field('fixed_buyout_price', trans('admin.fixed_buyout_price'))->min(0);
            $show->field('is_fixed_negotiable_price', trans('admin.is_fixed_negotiable_price'))->options([0 => '許可しない', 1 => '許可する']);
            $show->field('fixed_negotiable_price_message', trans('admin.fixed_negotiable_price_message'));
            $show->field('fixed_quantity', trans('admin.fixed_quantity'))->min(0);
        } else if ($row->format == 2) {
            $show->field('auction_listing_end_at', trans('admin.auction_listing_end_at'))->format('YYYY-MM-DD');
            //$form->field('auction_listing_start_at',trans('admin.auction_listing_start_at'))->format('YYYY-MM-DD');
            $form->field('auction_start_price', trans('admin.auction_start_price'))->min(0);
            $form->field('auction_buyout_price', trans('admin.auction_buyout_price'))->min(0);
            //$form->field('auction_lowest_price',trans('admin.auction_lowest_price'))->min(0);
            $form->field('is_auction_negotiable_price', trans('admin.is_auction_negotiable_price'))->options([0 => '許可しない', 1 => '許可する']);
            $form->field('auction_negotiable_price_message', trans('admin.auction_negotiable_price_message'));
            $form->field('auction_quantity', trans('admin.auction_quantity'))->min(0);
        }

        $show->field('is_private_listing', trans('admin.is_private_listing'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('is_charity', trans('admin.is_charity'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('tax', trans('admin.tax'));
        $show->field('payment_rule', trans('admin.payment_rule'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('delivery_rule', trans('admin.delivery_rule'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('return_rule', trans('admin.return_rule'))->using([
            0 => 'FALSE',
            1 => 'TRUE',
        ]);
        $show->field('product_size', trans('admin.product_size'));
        $show->field('product_weight', trans('admin.product_weight'));
        $show->field('delivery_source_area', trans('admin.delivery_source_area'));


        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $exhibit = new Exhibit();

        $form = new Form($exhibit);

        $is_ebay_auth = (Admin::user()->access_token_expires_in && (new \DateTime(Admin::user()->access_token_expires_in))->format('U') > time()) ? true : false;

        if (!$form->isEditing()) {
            if (Admin::user()->member_type != 1) {
                return redirect('/admin');
            }
        }

        Admin::headerJs('https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js');

        // クエリ文字取得 & js生成
        $template_id = isset($_GET["template"]) ? $_GET["template"] : null;
        $access_token = Admin::user()->access_token;

        $script = <<<SCRIPT
$('select[name=exhibit_template_id]').change(function(){
    let val = $(this).val();
    if(val > 0){
        let ret = window.confirm("テンプレートを呼び出しますか？");
        if(ret == true){
            window.location = $(location).attr('origin') + $(location).attr('pathname') + '?template=' + val;
        }
    }
});
// description_template_selectorの値が変更されたときのイベントリスナー
$('select[name=description_template_selector]').on('change', function() {
    var templateId = $(this).val();
    if (templateId !== '') {
        // 選択したテンプレートの内容を取得してdescriptionフィールドにセット
        $.get('/api/description_template_content/' + templateId, function(data) {
            if (data.result == true) {
				var editor = CKEDITOR.instances['description_template_content'];
                editor.setData(data.content);
            }
        });
    }
});
// SKUフィールドから「JAN:」を自動除去
function removeLANPrefix(inputElement) {
    var currentValue = inputElement.val();
    console.log('SKU値チェック開始:', currentValue);
    
    if (currentValue && currentValue.indexOf('JAN:') === 0) {
        var cleanValue = currentValue.replace(/^JAN:\s*/, '');
        console.log('JAN:を除去:', currentValue, '->', cleanValue);
        inputElement.val(cleanValue);
        return true;
    } else if (currentValue && currentValue.indexOf('JAN ') === 0) {
        var cleanValue = currentValue.replace(/^JAN\s+/, '');
        console.log('JAN を除去:', currentValue, '->', cleanValue);
        inputElement.val(cleanValue);
        return true;
    }
    return false;
}

// リアルタイム処理
$(document).on('input paste keyup', 'input[name="sku"]', function() {
    var self = $(this);
    setTimeout(function() {
        removeLANPrefix(self);
    }, 10);
});

// ページ読み込み時とフォーカス時に適用
$(document).ready(function() {
    console.log('SKU自動除去処理を初期化');
    
    function applySKUCleaning() {
        var skuInput = $('input[name="sku"]');
        console.log('SKU入力フィールド検索:', skuInput.length);
        
        if (skuInput.length > 0) {
            removeLANPrefix(skuInput);
            
            // フォーカス時にも適用
            skuInput.on('focus', function() {
                setTimeout(function() {
                    removeLANPrefix(skuInput);
                }, 10);
            });
        }
    }
    
    // 即座に適用
    applySKUCleaning();
    
    // 少し遅れて再度適用（他のスクリプトが値を設定する場合に備えて）
    setTimeout(applySKUCleaning, 500);
    setTimeout(applySKUCleaning, 1000);
    setTimeout(applySKUCleaning, 2000);
});
$(document).on('click','.sample_image_display_thumbnail_delete',function(){
	let target = $(this).data('url');
	$('div.sample_image_display_thumbnail[data-url="'+target+'"]').remove();
	let img_string = $('input[name=product_image]').val();
	let arr = new Array();
	$.each(img_string.split(',') ,function(idx,val){
		if(target != val.trim()){
			arr.push(val.trim());
		}
	});
	$('input[name=product_image]').val(arr.join(','));
});

setTimeout(function(){
	$('.sample_image_display_area').sortable({
		update: function(event, ui){
			console.log(event);
			console.log(ui);
			let arr = new Array();
			$.each($('.sample_image_display_thumbnail'), function(idx,obj){
				arr.push($(obj).data('url'));
			});
			$('input[name=product_image]').val(arr.join(','));
		}
	});
},2000);


SCRIPT;
        Admin::script($script);

        //$this->getCategories(Admin::user()->ebay_token);
        //$this->getCategorySpecifics(Admin::user()->ebay_token,18345);

        //$this->getCategoryAspect('EBAY_US');

        // テンプレのセレクトボックスを生成
        $templates = new ExhibitTemplate();
        $template = ExhibitTemplate::where('id', $template_id)->whereIn('admin_user_id', [1, Admin::user()->id])->first();
        $arr = array();
        $adminUsers = DB::table('admin_users')->where('permission_type', 1)->pluck('id');
        $adminUsers[] = Admin::user()->id;
        foreach ($templates::whereIn('admin_user_id', [1, Admin::user()->id])->get() as $row) {
            $arr[$row->id] = $row->title;
        }

        $form->html('<div class="box-header with-border clearfix"><span class="help-block"><i class="fa fa-info-circle"></i>&nbsp;出品ページ作成方法は▶<a href="https://youtu.be/xlpFM5WrxlY" target="_blank">こちら</a></span><span class="help-block"><b>※本ページ内の価格やサイズ（cm）等の数値は全て半角でご記入ください</b></span></div>');

        $form->column(8 / 12, function ($form) use ($arr, $template, $access_token, $exhibit) {
            //

            Admin::style('.custom-class { overflow-y: auto; overflow-x:hidden; height: 75vh; }');
            //$form->html($styleHtml)->style('overflow', 'scroll')->style('height', '75vh');
            if ($form->isEditing()) {
                $exhibit = Exhibit::where('id', request()->route()->parameters["exhibit"])->first();
                // ユーザIDが異なるかつロールが一般だったらリダイレクトさせる
                if (($exhibit->admin_user_id != Admin::user()->id && (Admin::user()->isRole('member') || Admin::user()->isRole('NPmember'))) || !($exhibit->status == 0 || $exhibit->status == 1)) {
                    if (!Admin::user()->isRole('administrator')) {
                        $form->html('<script>window.location.href="/admin/exhibits"</script>');
                    }
                    //redirect('/admin/exhibits');
                }
                if ($exhibit->publish_result) {
                    $publish_result = json_decode($exhibit->publish_result, true);
                    if (isset($publish_result["listingId"])) {
                        $form->html('<a href="https://www.ebay.com/itm/' . $publish_result["listingId"] . '" target="_blank">https://www.ebay.com/itm/' . $publish_result["listingId"] . '</a>');
                    } else if (isset($publish_result["errors"])) {
                        foreach ($publish_result["errors"] as $error) {
                            $form->html('<b style="color:red;">' . $error["message"] . '</b>');
                        }
                    }
                }

                if ($exhibit->api_res) {
                    $api_res = json_decode($exhibit->api_res, true);
                    if (isset($api_res['errors'])) {
                        foreach ($api_res["errors"] as $error) {
                            $form->html('<b style="color:red;">' . $error["message"] . '</b>');
                        }
                    }
                }

                if ($exhibit->aspects) {
                    $form->hidden('default_form_aspects')->default($exhibit->aspects);
                }
            }


            $form->select('exhibit_template_id', trans('admin.exhibit_template_id'))->options($arr)->default(isset($template->id) ? $template->id : 0)
                ->help('&nbsp;テンプレート編集＆新規作成は▶<a href="/admin/templates">こちら</a>');

            $form->hidden('admin_user_id')->default(Admin::user()->id);

            $form->html($this->get_product_html);

            $form->select('referer_service', trans('admin.referer_service'))->options([
                'yahoo' => 'ヤフオク',
                'mercari' => 'メルカリ',
                'amazon' => 'Amazon',
                'ebay' => 'eBay',
                'jancode' => 'JANコード',
            ])->rules(['required']);
            // $form->hidden('referer_service');
            $form->text('referer_id', trans('admin.referer_id'))->rules(['required']);
            //$form->decimal('referer_price',trans('admin.referer_price'))->rules(['required']);
            $form->currency('referer_price', trans('admin.referer_price'))->digits(0)->symbol('￥')->required();
            //$form->hidden('referer_id');
            //
            if ($form->isEditing()) {
                $exhibit = Exhibit::where('id', request()->route()->parameters["exhibit"])->first();
                if ($exhibit->referer_service && $exhibit->referer_id) {
                    $form->display('is_referer_soldout', trans('admin.is_referer_soldout'))->with(function () {
                        if ($this->is_referer_soldout == 0) {
                            return "販売中";
                        } else {
                            return "終了";
                        }
                    });
                } else {
                    $form->display('is_referer_soldout', trans('admin.is_referer_soldout'))->with(function () {
                        return '未連携';
                    });
                }
            }
            $form->text('title', trans('admin.title'))->rules(['required']);
            $form->text('display_title', trans('admin.display_title'))->rules(['required', 'max:80'])->help(__('admin.help.display_title'));

            $form->text('sku', trans('admin.sku'))->default(isset($template->sku) ? preg_replace('/^JAN:\s*/', '', $template->sku) : '')->required();

            // 商品カテゴリー
            //$form->decimal('category_id',trans('admin.category_id'))->required();

            //// こっから動的に変えてく！！
            // 商品詳細

            $form->aboutItem()->addVariables(['token' => $access_token]);
            $form->hidden('category_id');
            $form->hidden('condition');
            $form->hidden('condition_descriptors');
            $form->hidden('aspects');

            //$form->number('stock_quantity',trans('admin.stock_quantity'))->min(0)->required();
            $form->hidden('stock_quantity')->default(0);
            //$form->number('quantity_limit_per_buyer',trans('admin.quantity_limit_per_buyer'))->min(0)->required();
            $form->hidden('quantity_limit_per_buyer')->default(0);
            //$form->currency('price',trans('admin.price'))->digits(0)->symbol('￥')->required();
            $form->hidden('price')->default(0);
            //$form->select('condition',trans('admin.condition'))->options([
            //	'NEW' => '新品(未開封)',
            //	'LIKE_NEW' => '新古品',
            //	'NEW_OTHER' => '新品(開封済)',
            //	'NEW_WITH_DEFECTS' => '新品(欠損)',
            //	// 'MANUFACTURER_REFURBISHED' => 'メーカー再生品', // もう使えない
            //	// 'CERTIFIED_REFURBISHED' => 'メーカー再生品', // これももう使えないらしい
            //	// 'EXCELLENT_REFURBISHED' => 'メーカー再生品', // カテゴリーがスマホのみ利用可能らしい
            //	// 'VERY_GOOD_REFURBISHED' => 'メーカー修理品', // カテゴリーがスマホのみ利用可能らしい
            //	// 'GOOD_REFURBISHED',
            //	// 'SELLER_REFURBISHED' => '修理品',
            //	'USED_EXCELLENT' => '中古(最優良)',
            //	'USED_VERY_GOOD' => '中古(優良)',
            //	'USED_GOOD' => '中古(良)',
            //	'USED_ACCEPTABLE' => '中古',
            //	'FOR_PARTS_OR_NOT_WORKING' => 'ジャンク品',
            //]);
            $form->text('condition_description', trans('admin.condition_description'))->help(trans('admin.help.condition_description'));
            //$form->ckeditor('item_description',trans('admin.item_description'))->help(__('admin.help.item_description'))->required();
            $form->hidden('item_description')->default('');
            //$form->image('product_image',trans('admin.product_image'))->default(isset($template->product_image) ? $template->product_image : '');
            $form->hidden('product_image')->attribute(['id' => 'hidden_product_image']); //画像はパスをhiddenで保持
            $form->html($this->image_sample_html, trans('admin.product_image'))->required()->help(trans('admin.help.product_image'));
            $form->html($this->image_uploder_html);

            // APIで指定できない
            //$form->select('is_scale',trans('admin.is_scale'))->options([
            //  0 => '拡大許可しない',
            //  1 => '拡大許可する',
            //])->default(isset($template->is_scale) ? $template->is_scale : 0);
            //$form->select('is_advertising',trans('admin.is_advertising'))->options([
            //  0 => '広告許可しない',
            //  1 => '広告許可する',
            //])->default(isset($template->is_advertising) ? $template->is_advertising : 0);
            //$form->text('color',trans('admin.color'));

            // APIで指定できない？
            // $form->text('ca_prop_65',trans('admin.ca_prop_65'));
            //$form->radio('is_lot_sales',trans('admin.is_lot_sales'))->options([
            //	0 => '許可しない',
            //	1 => '許可する'
            //])->when(1,function(Form $form){
            //	$form->number('lot_size',trans('admin.lot_size'))->min(0);
            //});
            //$form->select('product_type',trans('admin.product_type'))->options([
            //	0 => '未選択',
            //	1 => 'オリジナル',
            //	2 => '複製品',
            //]);
            $form->hidden('is_lot_sales')->default(0);
            $form->hidden('product_type')->default(0);

            // APIで指定できない
            //$form->number('production_area',trans('admin.production_area'));
            //$form->date('origin_date',trans('admin.origin_date'))->format('YYYY-MM-DD');
            $form->ckeditor('product_description', trans('admin.product_description'));
            $form->ckeditor('display_product_description', trans('admin.display_product_description'))->default(isset($template->display_product_description) ? $template->display_product_description : '')->help(__('admin.help.display_product_description'))->required();

            $form->radio('format', trans('admin.format'))->options([
                1 => '固定',
                2 => 'オークション',
            ])->when(1, function (Form $form2) use ($form, $template) {
                $form2->select('fixed_listing_end_at', trans('admin.fixed_listing_end_at'))->options([
                    //'DAYS_1' => '1日',
                    //'DAYS_3' => '3日',
                    //'DAYS_5' => '5日',
                    //'DAYS_7' => '7日',
                    //'DAYS_10' => '10日',
                    //'DAYS_21' => '21日',
                    //'DAYS_30' => '30日',
                    'GTC' => '無期限',
                ])->default(isset($template->fixed_listing_end_at) ? $template->fixed_listing_end_at : 'GTC');
                //$form2->date('fixed_listing_start_at',trans('admin.fixed_listing_start_at'))->format('YYYY-MM-DD');
                $form2->currency('fixed_buyout_price', trans('admin.fixed_buyout_price'))->digits(2)->symbol('$');

                // APIに交渉許可ない
                // $form->select('is_fixed_negotiable_price',trans('admin.is_fixed_negotiable_price'))->options([0 => '許可しない',1 => '許可する']);
                // $form->text('fixed_negotiable_price_message',trans('admin.fixed_negotiable_price_message'));
                $form2->number('fixed_quantity', trans('admin.fixed_quantity'))->min(1)->default(1);

                $form2->radio('best_offer_enabled', trans('admin.best_offer_enabled'))->options([
                    false => 'ベストオファーを許可しない',
                    true => 'ベストオファーを許可する',
                ])->when(true, function (Form $form3) use ($form, $template) {
                    $form3->radio('is_auto_accept_price', trans('admin.auto_accept_price'))->options([
                        false => '自動承認しない',
                        true => '自動承認する',
                    ])->when(true, function (Form $form4) use ($form, $template) {
                        $form4->currency('auto_accept_price', trans('admin.auto_accept_price'))->digits(2)->symbol('$')->default(isset($template->auto_accept_price) ? $template->auto_accept_price : 0);
                    })->default(isset($template->is_auto_accept_price) ? $template->is_auto_accept_price : false)->help(trans('admin.help.auto_accept_price'));

                    $form3->radio('is_auto_decline_price', trans('admin.auto_decline_price'))->options([
                        false => '自動拒否しない',
                        true => '自動拒否する',
                    ])->when(true, function (Form $form4) use ($form, $template) {
                        $form4->currency('auto_decline_price', trans('admin.auto_decline_price'))->digits(2)->symbol('$')->default(isset($template->auto_decline_price) ? $template->auto_decline_price : 0);
                        ;
                    })->default(isset($template->is_auto_decline_price) ? $template->is_auto_decline_price : false)->help(trans('admin.help.auto_decline_price'));
                })->default(isset($template->best_offer_enabled) ? $template->best_offer_enabled : false);
                //$form->grossProfit('gross_profit1','粗利');
            })->when(2, function (Form $form2) use ($form, $template) {
                $form2->select('auction_listing_end_at', trans('admin.auction_listing_end_at'))->options([
                    'DAYS_1' => '1日',
                    'DAYS_3' => '3日',
                    'DAYS_5' => '5日',
                    'DAYS_7' => '7日',
                    'DAYS_10' => '10日',
                    //'DAYS_21' => '21日',
                    //'DAYS_30' => '30日',
                ])->default(isset($template->auction_listing_end_at) ? $template->auction_listing_end_at : 'DAYS_10');
                //$form2->date('auction_listing_start_at',trans('admin.auction_listing_start_at'))->format('YYYY-MM-DD')->default(isset($template->auction_listing_start_at) ? $template->auction_listing_start_at : date('Y-m-d'));
                $form2->currency('auction_start_price', trans('admin.auction_start_price'))->digits(2)->symbol('$')->default(isset($template->auction_start_price) ? $template->auction_start_price : 0);
                $form2->currency('auction_buyout_price', trans('admin.auction_buyout_price'))->digits(2)->symbol('$')->default(isset($template->auction_buyout_price) ? $template->auction_buyout_price : 0);
                $form2->hidden('auction_lowest_price')->default(0);
                //$form2->currency('auction_lowest_price',trans('admin.auction_lowest_price'))->digits(2)->symbol('$')->default(isset($template->auction_lowest_price) ? $template->auction_lowest_price : 0);

                // APIに交渉許可ない
                //$form->select('is_auction_negotiable_price',trans('admin.is_auction_negotiable_price'))->options([0 => '許可しない',1 => '許可する']);
                //$form->text('auction_negotiable_price_message',trans('admin.auction_negotiable_price_message'));
                $form2->number('auction_quantity', trans('admin.auction_quantity'))->min(1)->default(isset($template->auction_quantity) ? $template->auction_quantity : 1);

                //$form->grossProfit('gross_profit2','粗利');
            })->default(isset($template->format) ? $template->format : 1)->required();

            $form->select('is_private_listing', trans('admin.is_private_listing'))->options([
                0 => '無効',
                1 => '有効',
            ])->help(__('admin.help.is_private_listing'));
            //$form->select('is_charity',trans('admin.is_charity'))->options([
            //  0 => 'FALSE',
            //  1 => 'TRUE',
            //]);
            //$form->number('tax',trans('admin.tax'))->min(0)->max(100)->default(0);
            //$form->hidden('tax')->default(0);
            //$form->select('payment_rule',trans('admin.payment_rule'))->options([
            //  0 => 'FALSE',
            //  1 => 'TRUE',
            //]);
            //$form->select('delivery_rule',trans('admin.delivery_rule'))->options([
            //  0 => 'FALSE',
            //  1 => 'TRUE',
            //]);
            //$form->select('return_rule',trans('admin.return_rule'))->options([
            //  0 => 'FALSE',
            //  1 => 'TRUE',
            //]);
            // $form->text('product_size',trans('admin.product_size'));

            //$form->select('product_size_unit',trans('admin.product_size_unit'))->options([
            //	'CENTIMETER' => 'センチメートル',
            //	'METER' => 'メートル',
            //	'INCH' => 'インチ',
            //	'FEET' => 'フィート',
            //]);
            //$form->decimal('product_size_height',trans('admin.product_size_height'));
            //$form->decimal('product_size_width',trans('admin.product_size_width'));
            //$form->decimal('product_size_length',trans('admin.product_size_length'));
            //
            //$form->select('product_weight_unit',trans('admin.product_weight_unit'))->options([
            //	'GRAM' => 'グラム',
            //	'KILOGRAM' => 'キログラム',
            //	'POUND' => 'パウンド',
            //	'OUNCE' => 'オンス',
            //]);
            //$form->decimal('product_weight',trans('admin.product_weight'));

            //$form->select('delivery_source_area',trans('admin.delivery_source_area'))->options([
            //	'EBAY_US' => 'US(ebay.com)',
            //	//'EBAY_JP' => '日本(ebay.co.jp)',
            //	//'EBAY_CA' => 'カナダ(ebay.ca)',
            //	//'EBAY_GB' => '英国(ebay.co.uk)',
            //	//'EBAY_AU' => 'オーストラリア(ebay.com.au)',
            //	//'EBAY_AT' => 'オーストリア(ebay.at)',
            //	//'EBAY_BE' => 'ベルギー(www.ebay.be)',
            //	//'EBAY_FR' => 'フランス(ebay.fr)',
            //	//'EBAY_FR' => 'フランス(www.ebay.fr)',
            //])->when('EBAY_US',function(Form $form){
            //
            ////})->when('EBAY_JP',function(Form $form){
            ////	$form->select('fullfilment_policy_id',trans('admin.fullfilment_policy_id'))->options($this->getFulfillmentPolicy('EBAY_JP'));
            ////	$form->select('payment_policy_id',trans('admin.payment_policy_id'))->options($this->getPaymentPolicy('EBAY_JP'));
            ////	$form->select('return_policy_id',trans('admin.return_policy_id'))->options($this->getReturnPolicy('EBAY_JP'));
            ////})->required()->default('EBAY_US');
            //})->default('EBAY_US');

            $form->hidden('delivery_source_area')->default('EBAY_US');
            $form->select('fullfilment_policy_id', trans('admin.fullfilment_policy_id'))->options($this->getFulfillmentPolicy('EBAY_US'))->default(isset($template->fullfilment_policy_id) ? $template->fullfilment_policy_id : 0)->required();
            $form->select('payment_policy_id', trans('admin.payment_policy_id'))->options($this->getPaymentPolicy('EBAY_US'))->default(isset($template->payment_policy_id) ? $template->payment_policy_id : 0)->required();
            $form->select('return_policy_id', trans('admin.return_policy_id'))->options($this->getReturnPolicy('EBAY_US'))->default(isset($template->return_policy_id) ? $template->return_policy_id : 0)->required();

            $btn_script = <<<JS
	$('select[name=merchant_location_key]').empty();
	$.ajax({
		type: 'GET',
		url: '/admin/exhibits/merchant_location_keys',
	}).done(function(result){
		$.each(result.data,function(idx,row){
			$('select[name=merchant_location_key]').append($('<option>').attr({value: idx}).text(row));
		});
		$('select[name=merchant_location_key]').select2({"allowClear":false});
	});
JS;

            $form->selectMerchantLocationKey('merchant_location_key', trans('admin.merchant_location_key'))
                ->options($this->getMearchantLocation('EBAY_US'))->default(isset($template->merchant_location_key) ? $template->merchant_location_key : 0)->attribute('script', $btn_script)->help('更新ボタンは追加した新発送元地域ポリシーを初回表示させる時のみ使用')->required();
            //$form->button(trans('admin.reload_merchant_location_key'))->on('click',$btn_script);
            $form->button(trans('admin.view_merchant_location_key'))->on('click', <<<JS
window.open('/admin/exhibits/merchant_location_key',null,'width=600,height=500');
JS);
        });

        $form->column(4 / 12, function ($form) use ($access_token, $is_ebay_auth) {
            if ($is_ebay_auth) {
                $form->ebayDescription('ebay_description_id', trans('admin.ebay_description_id'))->help(__('admin.help.ebay_description_caution'));
            }

            //$form->searchCategories('search_category','カテゴリー検索');

            if ($is_ebay_auth && Admin::user()->member_type == 1) {
                $form->translation('translation', '翻訳');
            }
            $form->grossProfit('grossProfit', '粗利試算')->help('粗利計算方法は▶<a target="_blank" href="https://www.youtube.com/watch?v=AgAAads22l0">こちら</a>');

            // 商品説明テンプレはオミット
            //$form->select('description_template_selector', trans('admin.description_template'))->options(function ($id) {
            //	if (Admin::user()->isRole('member') || Admin::user()->isRole('NPmember')) {
            //		return \App\Models\DescriptionTemplate::whereIn('admin_user_id',[Admin::user()->id,0])->pluck('name', 'id');
            //	} else {
            //		return \App\Models\DescriptionTemplate::pluck('name', 'id');
            //	}
            //});
            //
            //$form->ckeditor('description_template_content', '')->options([
            //	'toolbar' => [],
            //	'allowedContent' => 'p h1 h2 h3 div big small tt code kbd samp var del ins cite q span  strong em s pre; ol ul li; blockquote; table tbody thead tr td; br; hr; a[!href]; img[!src,width,height];',
            //]);

        });

        $form->submitted(function (Form $form) {
            //$form->ignore('description_template_selector');
            //$form->ignore('description_template_content');
            $form->ignore('ebay_description_id');
            $form->ignore('get_ebay_description');
            $form->ignore('ebay_description_text');
            $form->ignore('default_form_aspects');
        });


        $form->saving(function (Form $form) {
            Log::setDefaultDriver('exhibits');
            Log::info('-- Exhibit Saving  USERID:' . $form->admin_user_id . ' --');
            Log::info($form->form_aspects);

            // SKUから「JAN:」プレフィックスを除去
            if ($form->sku && strpos($form->sku, 'JAN:') === 0) {
                $form->sku = trim(substr($form->sku, 4));
                Log::info('SKU保存時に「JAN:」プレフィックスを除去: ' . $form->sku);
            } else if ($form->sku && strpos($form->sku, 'JAN ') === 0) {
                $form->sku = trim(substr($form->sku, 4));
                Log::info('SKU保存時に「JAN 」プレフィックスを除去: ' . $form->sku);
            }

            /* 再帰的に入れうをフィルタリングする */
            function array_filter_recursive($arr): array
            {
                if ($arr) {
                    foreach ($arr as $key => $val) {
                        if (is_array($val)) {
                            $val = array_filter_recursive($val);
                        }
                        if (!empty($val)) {
                            $key2 = str_replace('_', ' ', $key);
                            $res[$key2] = $val;
                        }
                    }
                }
                return $res ?? [];
            }

            $original_aspects = [];
            if ($form->original_aspect_keys) {
                foreach ($form->original_aspect_keys as $key => $val) {
                    if ($val) {
                        $aspect_key = str_replace('_', ' ', $val);
                        $aspect_val = isset($form->original_aspect_values[$key]) ? $form->original_aspect_values[$key] : 'N/A';
                        $original_aspects[$aspect_key] = [$aspect_val];
                    }
                }
            }


            $form_aspects = array_filter_recursive($form->form_aspects);

            $aspects = json_encode(array_merge($form_aspects, $original_aspects));



            $form->category_id = $form->form_category_id;
            $form->condition = $form->form_condition;

            $form_condition_descriptors = $form->form_condition_descriptors;
            $form->condition_descriptors = $form_condition_descriptors ? json_encode($form_condition_descriptors) : null;

            $form->aspects = $aspects;
            if ($form->is_lot_sales != 1) {
                $form->is_lot_sales = 0;
            }
            if (!$form->exhibit_template_id) {
                $form->exhibit_template_id = 0;
            }

            unset($form->form_aspects);
            unset($form->original_aspect_keys);
            unset($form->original_aspect_values);
            unset($form->form_category_id);
            unset($form->form_condition);
            unset($form->form_condition_descriptors);
        });

        $_condition_enum = $this->condition_enum;

        $form->saved(function (Form $form) use ($exhibit, $_condition_enum) {
            $is_error = true;

            Log::setDefaultDriver('exhibits');
            Log::info('-- Exhibit Saved  USERID:' . $form->admin_user_id . ' --');

            //// 出品処理
            // １．在庫登録APIで在庫を作る
            // https://developer.ebay.com/api-docs/sell/inventory/resources/inventory_item/methods/createOrReplaceInventoryItem
            $client = null;
            $client = new GuzzleClient();
            $sku = urlencode($form->model()->sku);

            $image_urls = [];
            if ($form->model()->product_image) {
                foreach (explode(',', $form->model()->product_image) as $row) {
                    $image_urls[] = config('filesystems.disks.admin.url') . '/' . $row;
                }
            }

            Log::info($form->model()->aspects);
            $aspects = json_decode($form->model()->aspects, true);

            if ($form->model()->format == 1) {
                $quantity = $form->model()->fixed_quantity;
            } else {
                $quantity = $form->model()->auction_quantity;
            }

            $condition = $_condition_enum[$form->model()->condition];

            // リクエスト内容
            $request_body = [
                // 商品情報のメイン部分 https://developer.ebay.com/api-docs/sell/inventory/types/slr:Product
                'product' => [
                    'title' => $form->model()->display_title, //タイトル
                    'aspects' => $aspects,
                    'description' => $form->model()->item_description,
                    'imageUrls' => $image_urls,
                ],
                // 状態 https://developer.ebay.com/api-docs/sell/inventory/types/slr:ConditionEnum
                'condition' => $condition,
                'conditionDescription' => ($form->model()->condition_description) ? $form->model()->condition_description : 'N/A',
                // 在庫数量指定とか？ https://developer.ebay.com/api-docs/sell/inventory/types/slr:Availability
                'availability' => [
                    'shipToLocationAvailability' => [
                        'quantity' => $quantity, // 在庫数量
                    ],
                ],
            ];

            // JANコード処理：referer_serviceがjancodeの場合、UPCとして追加を試行
            if ($form->model()->referer_service == 'jancode' && $form->model()->referer_id) {
                // JANコード取得と前処理
                $raw_jancode = $form->model()->referer_id;
                Log::info('JANコード処理開始（元データ）: ' . $raw_jancode);

                // 「JAN: 」プレフィックスを除去
                $jancode = $raw_jancode;
                if (strpos($jancode, 'JAN:') === 0) {
                    $jancode = trim(substr($jancode, 4)); // 「JAN:」（4文字）を除去してトリム
                    Log::info('「JAN: 」プレフィックスを除去: ' . $jancode);
                } else if (strpos($jancode, 'JAN ') === 0) {
                    $jancode = trim(substr($jancode, 4)); // 「JAN 」（4文字）を除去してトリム
                    Log::info('「JAN 」プレフィックスを除去: ' . $jancode);
                }

                // 数字以外の文字を除去（ハイフンやスペースなど）
                $jancode = preg_replace('/[^0-9]/', '', $jancode);
                Log::info('数字以外を除去後: ' . $jancode);

                // JANコードのバリデーション（13桁で数字のみ）
                if (preg_match('/^\d{13}$/', $jancode)) {
                    $request_body['product']['upc'] = [$jancode];
                    Log::info('JANコードをUPCとして設定: ' . $jancode);
                } else if (preg_match('/^\d{8}$/', $jancode)) {
                    // 8桁の場合はEAN-8として処理
                    $request_body['product']['upc'] = [$jancode];
                    Log::info('EAN-8コードをUPCとして設定: ' . $jancode);
                } else {
                    Log::warning('無効なJANコード形式（13桁または8桁の数字でない）: ' . $jancode);
                    Log::warning('元データ: ' . $raw_jancode);
                    // 無効な形式の場合はUPCを設定しない
                }
            } else if ($form->model()->referer_service == 'jancode') {
                Log::warning('JANコードサービスが選択されているが、referer_idが空です');
            }

            Log::info('=== eBay Inventory Item 作成処理開始 ===');
            Log::info('SKU: ' . $sku);
            Log::info('JANコード設定状況: ' . (isset($request_body['product']['upc']) ? 'あり (' . implode(',', $request_body['product']['upc']) . ')' : 'なし'));
            Log::info('Request Body: ' . json_encode($request_body, JSON_UNESCAPED_UNICODE));

            // 最初の試行: 元のリクエストボディで実行
            $response = $client->request('PUT', config('ebay.api_endpoint') . '/sell/inventory/v1/inventory_item/' . $sku, [
                'http_errors' => false,
                'headers' => [
                    'Authorization' => [
                        'Bearer ' . Admin::user()->access_token,
                    ],
                    'Accept' => ['application/json'],
                    'Content-Type' => ['application/json'],
                    'Content-Language' => ['en-US'],
                ],
                'json' => $request_body,
            ]);
            $inventory_return = $response->getBody()->getContents();

            Log::info('PUT ' . config('ebay.api_endpoint') . '/sell/inventory/v1/inventory_item/' . $sku);
            Log::info(" Response \n HTTP Status: " . $response->getStatusCode() . " \n Body :" . $inventory_return);

            Exhibit::where('id', $form->model()->id)->update([
                'api_res' => $inventory_return,
            ]);

            // inventory_item作成でエラーが発生した場合の包括的な処理
            if ($response->getStatusCode() != 200 && $response->getStatusCode() != 201 && $response->getStatusCode() != 204) {
                $error_response = json_decode($inventory_return, true);
                Log::error('=== [add_new_trading_api] 初回試行失敗 ===');
                Log::error('HTTPステータス: ' . $response->getStatusCode());
                Log::error('エラーレスポンス: ' . $inventory_return);

                $retry_success = false;

                // 段階的フォールバック処理
                if (isset($error_response['errors'])) {
                    foreach ($error_response['errors'] as $error) {
                        $error_message = $error['message'] ?? '';
                        $error_domain = $error['domain'] ?? '';
                        $error_code = $error['errorId'] ?? '';

                        // フォールバック1: JANコード/UPCエラーの場合
                        if (
                            strpos($error_message, 'could not be found') !== false ||
                            strpos($error_message, 'not available in the system') !== false ||
                            strpos($error_domain, 'UPC') !== false ||
                            strpos($error_domain, 'EAN') !== false ||
                            in_array($error_code, [25751, 25752, 25753])
                        ) {

                            Log::warning('=== [add_new_trading_api] UPC/JANエラー検出、段階的復旧開始 ===');
                            Log::warning('エラーメッセージ: ' . $error_message);
                            Log::warning('エラードメイン: ' . $error_domain);
                            Log::warning('エラーコード: ' . $error_code);
                            Log::warning('問題のJANコード: ' . (isset($request_body['product']['upc']) ? json_encode($request_body['product']['upc']) : 'なし'));

                            // フォールバック1-1: UPC/JANコードのみを除去
                            if (isset($request_body['product']['upc'])) {
                                $original_upc = $request_body['product']['upc'];
                                unset($request_body['product']['upc']);
                                Log::info('=== [add_new_trading_api] フォールバック1-1: UPCのみ除去 ===');
                                Log::info('除去したUPC: ' . json_encode($original_upc));

                                $retry_response = $this->tryInventoryItemRequest($client, $sku, $request_body);

                                if ($retry_response['success']) {
                                    Log::info('=== [add_new_trading_api] フォールバック1-1成功 ===');
                                    Exhibit::where('id', $form->model()->id)->update([
                                        'api_res' => $retry_response['body'],
                                    ]);
                                    $response = $retry_response['response'];
                                    $inventory_return = $retry_response['body'];
                                    $retry_success = true;
                                    break;
                                }

                                // フォールバック1-2: UPC + aspects も除去
                                if (isset($request_body['product']['aspects'])) {
                                    $original_aspects = $request_body['product']['aspects'];
                                    unset($request_body['product']['aspects']);
                                    Log::info('=== [add_new_trading_api] フォールバック1-2: UPC + aspects除去 ===');

                                    $retry_response = $this->tryInventoryItemRequest($client, $sku, $request_body);

                                    if ($retry_response['success']) {
                                        Log::info('=== [add_new_trading_api] フォールバック1-2成功 ===');
                                        Exhibit::where('id', $form->model()->id)->update([
                                            'api_res' => $retry_response['body'],
                                        ]);
                                        $response = $retry_response['response'];
                                        $inventory_return = $retry_response['body'];
                                        $retry_success = true;
                                        break;
                                    }

                                    // aspects を戻す
                                    $request_body['product']['aspects'] = $original_aspects;
                                }

                                // フォールバック1-3: productセクション全体を除去
                                Log::info('=== [add_new_trading_api] フォールバック1-3: productセクション全体除去 ===');
                                $original_product = $request_body['product'];
                                unset($request_body['product']);

                                $retry_response = $this->tryInventoryItemRequest($client, $sku, $request_body);

                                if ($retry_response['success']) {
                                    Log::info('=== [add_new_trading_api] フォールバック1-3成功 ===');
                                    Exhibit::where('id', $form->model()->id)->update([
                                        'api_res' => $retry_response['body'],
                                    ]);
                                    $response = $retry_response['response'];
                                    $inventory_return = $retry_response['body'];
                                    $retry_success = true;
                                    break;
                                }

                                // 全て復元
                                $request_body['product'] = $original_product;
                            }
                        }

                        // フォールバック2: その他のエラーの場合
                        Log::warning('=== [add_new_trading_api] その他のエラー検出 ===');
                        Log::warning('エラーメッセージ: ' . $error_message);
                        Log::warning('エラードメイン: ' . $error_domain);
                        Log::warning('エラーコード: ' . $error_code);
                    }
                }

                if (!$retry_success) {
                    Log::error('=== [add_new_trading_api] 全てのフォールバック処理が失敗 ===');
                    Log::error('最終エラーレスポンス: ' . $inventory_return);
                }
            }

            if ($response->getStatusCode() == 200 || $response->getStatusCode() == 201 || $response->getStatusCode() == 204) {
                // 成功なら継続
                Exhibit::where('id', $form->model()->id)->update([
                    'status' => 1,
                ]);

                // 出品処理
                $format = '';
                $save_price = 0;
                if ($form->model()->format == 1) {
                    $format = 'FIXED_PRICE';
                    $pricing_summary = [
                        'price' => [
                            'currency' => 'USD',
                            'value' => $form->model()->fixed_buyout_price,
                        ]
                    ];
                    $save_price = $form->model()->fixed_buyout_price;
                    $availableQuantity = $form->model()->fixed_quantity;
                    //$quantityLimitPerBuyer = $form->model()->quantity_limit_per_buyer;
                    //$date = new \DateTime($form->model()->fixed_listing_start_at, new \DateTimeZone("Asia/Tokyo"));
                    //$date->setTimezone(new \DateTimeZone('UTC'));
                    //$listing_start_date = $date->format('Y-m-d\TH:i:s').'Z';
                    $listing_policies = [
                        'fulfillmentPolicyId' => $form->model()->fullfilment_policy_id,
                        'paymentPolicyId' => $form->model()->payment_policy_id,
                        'returnPolicyId' => $form->model()->return_policy_id,
                    ];
                    if ($form->model()->best_offer_enabled) {
                        $listing_policies["bestOfferTerms"] = [
                            'bestOfferEnabled' => true,
                        ];
                        if ($form->model()->is_auto_accept_price) {
                            $listing_policies["bestOfferTerms"]['autoAcceptPrice']["value"] = "" . $form->model()->auto_accept_price;
                            $listing_policies["bestOfferTerms"]['autoAcceptPrice']["currency"] = "USD";
                        }
                        if ($form->model()->is_auto_decline_price) {
                            $listing_policies["bestOfferTerms"]['autoDeclinePrice']["value"] = "" . $form->model()->auto_decline_price;
                            $listing_policies["bestOfferTerms"]['autoDeclinePrice']["currency"] = "USD";
                        }
                    }
                } else if ($form->model()->format == 2) {
                    $format = 'AUCTION';
                    $pricing_summary = [
                        'auctionStartPrice' => [
                            'currency' => 'USD',
                            'value' => $form->model()->auction_start_price,
                        ],
                        'auctionReservePrice' => [
                            'currency' => 'USD',
                            'value' => $form->model()->auction_buyout_price,
                        ],
                    ];
                    $save_price = $form->model()->auction_buyout_price;

                    $form->model()->auction_quantity = 1;
                    $form->model()->quantity_limit_per_buyer = 1;
                    $availableQuantity = $form->model()->auction_quantity;
                    //$quantityLimitPerBuyer = $form->model()->quantity_limit_per_buyer;
                    //$date = new \DateTime($form->model()->auction_listing_start_at, new \DateTimeZone("Asia/Tokyo"));
                    //$date->setTimezone(new \DateTimeZone('UTC'));
                    //$listing_start_date = $date->format('Y-m-d\TH:i:s').'Z';

                    $listing_policies = [
                        'fulfillmentPolicyId' => $form->model()->fullfilment_policy_id,
                        'paymentPolicyId' => $form->model()->payment_policy_id,
                        'returnPolicyId' => $form->model()->return_policy_id,
                    ];
                }

                Exhibit::where('id', $form->model()->id)->update([
                    'price' => $save_price,
                ]);

                if ($format) {
                    $status = 1;
                    //if($form->model()->tax != 0){
                    //	$tax = [
                    //		'applyTax' => true,
                    //		'vatPercentage' => $form->model()->tax,
                    //	];
                    //} else {
                    //
                    //}
                    $tax = [
                        'applyTax' => false,
                        'vatPercentage' => 0,
                    ];

                    if ($form->model()->category_id == 183050 || $form->model()->category_id == 183454 || $form->model()->category_id == 261328) {
                    }

                    // ２． 出品APIをキック
                    // https://developer.ebay.com/api-docs/sell/inventory/resources/offer/methods/createOffer
                    $request_body = [
                        // 必須項目3つ
                        'sku' => $form->model()->sku,
                        // マーケットプレイスID 一旦USで
                        // https://developer.ebay.com/api-docs/sell/inventory/types/slr:MarketplaceEnum
                        'marketplaceId' => $form->model()->delivery_source_area,
                        'format' => $format,
                        // 事実上必須
                        'pricingSummary' => $pricing_summary,
                        'listingDescription' => $form->model()->display_product_description,
                        'merchantLocationKey' => $form->model()->merchant_location_key,
                        'availableQuantity' => $availableQuantity,
                        //'quantityLimitPerBuyer' => $quantityLimitPerBuyer,
                        'categoryId' => $form->model()->category_id,
                        //
                        'listingPolicies' => $listing_policies,
                        //
                        'hideBuyerDetails' => ($form->model()->is_private_listing ? "false" : "true"),
                        //'listingStartDate' => $listing_start_date,
                        'tax' => $tax,
                        'includeCatalogProductDetails' => false,
                    ];
                    // offer_idが登録されてなかったら出品されてないハズ
                    if ($form->model()->offer_id) {
                        // 更新API
                        $response = $client->request('PUT', config('ebay.api_endpoint') . '/sell/inventory/v1/offer/' . $form->model()->offer_id, [
                            'http_errors' => false,
                            'headers' => [
                                'Authorization' => [
                                    'Bearer ' . Admin::user()->access_token,
                                ],
                                'Accept' => ['application/json'],
                                'Content-Type' => ['application/json'],
                                'Content-Language' => ['en-US'],
                            ],
                            'json' => $request_body,
                        ]);
                        $temp = DB::table('exhibits')->where('id', $form->model()->id)->update([
                            'api_res' => $response->getBody()->getContents(),
                        ]);
                        Log::info('PUT ' . config('ebay.api_endpoint') . '/sell/inventory/v1/offer/' . $form->model()->offer_id);
                        Log::info('Request Body: ' . json_encode($request_body));
                        Log::info(" Response \n HTTP Status: " . $response->getStatusCode() . " \n Body :" . $response->getBody()->getContents());

                        if ($response->getStatusCode() == 200) {
                            DB::table('exhibits')->where('id', $form->model()->id)->update([
                                'status' => 1,
                            ]);
                        }
                    } else {
                        // 新規登録API（まずPOSTで試行、409エラーの場合はPUTで更新）
                        $response = $client->request('POST', config('ebay.api_endpoint') . '/sell/inventory/v1/offer', [
                            'http_errors' => false,
                            'headers' => [
                                'Authorization' => [
                                    'Bearer ' . Admin::user()->access_token,
                                ],
                                'Accept' => ['application/json'],
                                'Content-Type' => ['application/json'],
                                'Content-Language' => ['en-US'],
                            ],
                            'json' => $request_body,
                        ]);
                        $response_body = $response->getBody()->getContents();

                        Log::info('POST ' . config('ebay.api_endpoint') . '/sell/inventory/v1/offer');
                        Log::info('Request Body: ' . json_encode($request_body));
                        Log::info(" Response \n HTTP Status: " . $response->getStatusCode() . " \n Body :" . $response_body);

                        // 409エラー（Offer already exists）の場合は、既存のオファーを取得してPUTで更新
                        if ($response->getStatusCode() == 409) {
                            $error_response = json_decode($response_body, true);
                            Log::info('Offer already exists - attempting to find and update existing offer');

                            // 既存のオファーを検索
                            $search_response = $client->request('GET', config('ebay.api_endpoint') . '/sell/inventory/v1/offer?sku=' . urlencode($form->model()->sku), [
                                'http_errors' => false,
                                'headers' => [
                                    'Authorization' => [
                                        'Bearer ' . Admin::user()->access_token,
                                    ],
                                    'Accept' => ['application/json'],
                                    'Content-Type' => ['application/json'],
                                    'Content-Language' => ['en-US'],
                                ],
                            ]);

                            $search_body = $search_response->getBody()->getContents();
                            Log::info('GET ' . config('ebay.api_endpoint') . '/sell/inventory/v1/offer?sku=' . urlencode($form->model()->sku));
                            Log::info(" Search Response \n HTTP Status: " . $search_response->getStatusCode() . " \n Body :" . $search_body);

                            if ($search_response->getStatusCode() == 200) {
                                $search_result = json_decode($search_body, true);
                                if (isset($search_result['offers']) && count($search_result['offers']) > 0) {
                                    $existing_offer_id = $search_result['offers'][0]['offerId'];
                                    Log::info('Found existing offer ID: ' . $existing_offer_id);

                                    // 既存のオファーをPUTで更新
                                    $response = $client->request('PUT', config('ebay.api_endpoint') . '/sell/inventory/v1/offer/' . $existing_offer_id, [
                                        'http_errors' => false,
                                        'headers' => [
                                            'Authorization' => [
                                                'Bearer ' . Admin::user()->access_token,
                                            ],
                                            'Accept' => ['application/json'],
                                            'Content-Type' => ['application/json'],
                                            'Content-Language' => ['en-US'],
                                        ],
                                        'json' => $request_body,
                                    ]);
                                    $response_body = $response->getBody()->getContents();

                                    Log::info('PUT ' . config('ebay.api_endpoint') . '/sell/inventory/v1/offer/' . $existing_offer_id);
                                    Log::info(" Update Response \n HTTP Status: " . $response->getStatusCode() . " \n Body :" . $response_body);

                                    if ($response->getStatusCode() == 200) {
                                        $content = json_decode($response_body, false);
                                        DB::table('exhibits')->where('id', $form->model()->id)->update([
                                            'offer_id' => $existing_offer_id,
                                            'status' => 1,
                                        ]);
                                        $form->model()->offer_id = $existing_offer_id;
                                    }
                                }
                            }
                        } elseif ($response->getStatusCode() == 201) {
                            $content = json_decode($response_body, false);
                            DB::table('exhibits')->where('id', $form->model()->id)->update([
                                'offer_id' => $content->offerId,
                                'status' => 1,
                            ]);
                            $form->model()->offer_id = $content->offerId;
                        }

                        $temp = DB::table('exhibits')->where('id', $form->model()->id)->update([
                            'api_res' => $response_body,
                        ]);
                    }
                }
                if ($form->model()->offer_id) {
                    $is_publish = false;
                    // publishOffer
                    $exhibit_data = DB::table('exhibits')->where('id', $form->model()->id)->first();
                    $publish_result = $exhibit_data->publish_result;
                    if ($publish_result) {
                        $publish = json_decode($publish_result, true);
                        if ($publish) {
                            if (isset($publish["listingId"])) {
                                // publish済み
                                $is_publish = true;
                                DB::table('exhibits')->where('id', $form->model()->id)->update([
                                    'status' => 2,
                                    'item_id' => $publish["listingId"],
                                ]);
                                $is_error = false;
                            }
                        }
                    }
                    if (!$is_publish) {
                        // https://developer.ebay.com/api-docs/sell/inventory/resources/offer/methods/publishOffer
                        $response2 = $client->request('POST', config('ebay.api_endpoint') . '/sell/inventory/v1/offer/' . $form->model()->offer_id . '/publish/', [
                            'http_errors' => false,
                            'headers' => [
                                'Authorization' => [
                                    'Bearer ' . Admin::user()->access_token,
                                ],
                                'Accept' => ['application/json'],
                                'Content-Type' => ['application/json'],
                                'Content-Language' => ['en-US'],
                            ],
                        ]);

                        $resbody = $response2->getBody()->getContents();
                        $resjson = json_decode($resbody, true);
                        $str = json_encode($resjson);
                        Log::info('POST ' . config('ebay.api_endpoint') . '/sell/inventory/v1/offer/' . $form->model()->offer_id . '/publish/');
                        Log::info(" Response \n HTTP Status: " . $response2->getStatusCode() . " \n Body :" . $str);

                        DB::table('exhibits')->where('id', $form->model()->id)->update([
                            'publish_result' => $str,
                        ]);

                        if ($response2->getStatusCode() == 200 && isset($resjson["listingId"])) {
                            DB::table('exhibits')->where('id', $form->model()->id)->update([
                                'status' => 2,
                                'item_id' => $resjson["listingId"],
                            ]);
                            $is_error = false;
                        }
                    }
                }
            }

            if ($is_error) {
                return redirect()->to('admin/exhibits/' . $form->model()->id . '/edit');
            } else {
                return redirect()->to('admin/exhibits');
            }
        });

        $form->tools(function (Tools $tools) {
            $tools->disableDelete();
            $tools->disableView();
            //$tools->add(new Delete);
        });

        $form->footer(function ($footer) use ($is_ebay_auth) {
            // Temporarily disabled eBay auth check for submit button
            // if (!$is_ebay_auth) {
            //     $footer->disableSubmit();
            // }
            $footer->disableReset();
        });

        return $form;
    }

    private function getFulfillmentPolicy($marketplace_id)
    {
        //return ['0' => 'ポリシー未選択'];

        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/account/v1/fulfillment_policy?marketplace_id=' . $marketplace_id, [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        Log::setDefaultDriver('exhibits');
        Log::info('-- getFulfillmentPolicy --');
        Log::info($response_body);

        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        if (!isset($obj["fulfillmentPolicies"])) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        $temp = ['0' => 'ポリシー未選択'];
        ;
        foreach ($obj["fulfillmentPolicies"] as $row) {
            if (isset($row["name"]) && $row["marketplaceId"] == $marketplace_id) {
                $temp[$row['fulfillmentPolicyId']] = $row['name'];
            }
        }
        return $temp;
    }

    private function getPaymentPolicy($marketplace_id)
    {
        //return ['0' => 'ポリシー未選択'];

        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/account/v1/payment_policy?marketplace_id=' . $marketplace_id, [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        if (!isset($obj["paymentPolicies"])) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        $temp = ['0' => 'ポリシー未選択'];
        ;
        foreach ($obj["paymentPolicies"] as $row) {
            if (isset($row["name"]) && $row["marketplaceId"] == $marketplace_id) {
                $temp[$row['paymentPolicyId']] = $row['name'];
            }
        }
        return $temp;
    }

    private function getReturnPolicy($marketplace_id)
    {

        //return ['0' => 'ポリシー未選択'];

        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/account/v1/return_policy?marketplace_id=' . $marketplace_id, [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        if (!isset($obj["returnPolicies"])) {
            // 失敗？
            return ['0' => 'ポリシー未選択'];
        }
        $temp = ['0' => 'ポリシー未選択'];
        ;
        foreach ($obj["returnPolicies"] as $row) {
            if (isset($row["name"]) && $row["marketplaceId"] == $marketplace_id) {
                $temp[$row['returnPolicyId']] = $row['name'];
            }
        }
        return $temp;
    }

    private function getMearchantLocation($marketplace_id)
    {
        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/inventory/v1/location', [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return ['' => 'ロケーション未選択'];
        }
        if (!isset($obj["locations"])) {
            // 失敗？
            return ['' => 'ロケーション未選択'];
        }
        $temp = ['' => 'ロケーション未選択'];
        foreach ($obj["locations"] as $row) {
            if (isset($row["merchantLocationKey"])) {
                $temp[$row['merchantLocationKey']] = $row['merchantLocationKey'];
            }
        }
        return $temp;
    }

    public function getTranslation(Request $request)
    {
        $text = $request->input('text');
        $ret = [
            'text' => '',
            'status' => 'error',
        ];
        if (!$text) {
            return response()->json($ret);
        }

        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('POST', 'https://api-free.deepl.com/v2/translate', [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'DeepL-Auth-Key 8cab8d2d-2d06-d118-9db3-974bf25ce014:fx',
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
            ],
            'json' => [
                'text' => [$text],
                "target_lang" => "EN",
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            $ret["status"] = 'error1';
            return response()->json($ret);
        }
        if (!isset($obj["translations"])) {
            $ret["status"] = 'error2';
            return response()->json($ret);
        }
        if (!isset($obj["translations"][0]["text"])) {
            $ret["status"] = 'error3';
            return response()->json($ret);
        }
        $ret["status"] = 'success';
        $ret["text"] = $obj["translations"][0]["text"];
        return response()->json($ret);
    }

    public function getMerchantLocationKeys(Request $request)
    {

        $ret = [
            'data' => [],
            'status' => 'error',
        ];

        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/inventory/v1/location', [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            $ret["data"] = ['' => 'ロケーション未設定'];
            $ret["status"] = 'error1';
            return response()->json($ret);
        }
        if (!isset($obj["locations"])) {
            // 失敗？
            $ret["data"] = ['' => 'ロケーション未設定'];
            $ret["status"] = 'error2';
            return response()->json($ret);
        }
        $temp = ['' => 'ロケーション未設定'];
        $details = ['' => []];
        foreach ($obj["locations"] as $row) {
            if (isset($row["merchantLocationKey"])) {
                $temp[$row['merchantLocationKey']] = $row['merchantLocationKey'];
                $details[$row['merchantLocationKey']] = $row['location']['address'];
            }
        }
        $ret["data"] = $temp;
        $ret["status"] = 'success';
        $ret["details"] = $details;
        return response()->json($ret);
    }

    public function viewMerchantLocationKey()
    {
        return view('admin.extensions.mearchant_location_key');
    }

    public function setMerchantLocationKey(Request $request, $id)
    {
        $client = null;
        $client = new GuzzleClient();

        $response = $client->request('POST', config('ebay.api_endpoint') . '/sell/inventory/v1/location/' . $id, [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
            'json' => [
                "location" => [
                    "address" => [
                        "city" => $request->city,
                        "country" => $request->country,
                    ]
                ]
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() == 204) {
            // 新規登録成功
            return response()->json([
                'status' => 'success',
                'code' => $response->getStatusCode(),
                'body' => [],
                'mode' => 'insert',
            ]);
        } else if ($response->getStatusCode() == 409) {
            // 重複してるらしいから更新を試してみる
            $response = $client->request('POST', config('ebay.api_endpoint') . '/sell/inventory/v1/location/' . $id . '/update_location_details', [
                'http_errors' => false,
                'headers' => [
                    'Authorization' => [
                        'Bearer ' . Admin::user()->access_token,
                    ],
                    'Accept' => ['application/json'],
                    'Content-Type' => ['application/json'],
                    'Content-Language' => ['en-US'],
                ],
                'json' => [
                    "location" => [
                        "address" => [
                            "city" => $request->city,
                            "country" => $request->country,
                        ],
                    ],
                ],
            ]);
            $response_body = $response->getBody()->getContents();
            $obj = json_decode($response_body, true);
            if ($response->getStatusCode() == 204) {
                // 更新成功
                return response()->json([
                    'status' => 'success',
                    'code' => $response->getStatusCode(),
                    'body' => [],
                    'mode' => 'update',
                ]);
            } else {
                return response()->json([
                    'status' => 'error1',
                    'code' => $response->getStatusCode(),
                    'body' => $obj,
                    'mode' => 'update',
                ]);
            }
        } else {
            // 失敗？
            return response()->json([
                'status' => 'error',
                'code' => $response->getStatusCode(),
                'body' => $obj,
                'mode' => 'insert',
            ]);
        }
    }


    // 既存の商品から詳細とかを取得する
    public function getEbayItem(Request $request)
    {
        $id = $request->input('id');
        $token = $request->input('token');
        if (!$id) {
            return response()->json([
                'result' => 'failed',
                'message' => 'invalid param:id',
            ]);
        }
        if (!$token) {
            return response()->json([
                'result' => 'failed',
                'message' => 'invalid param:token',
            ]);
        }
        if (filter_var($id, FILTER_VALIDATE_URL)) {
            // URLっぽい
            $path = parse_url($id, PHP_URL_PATH);
            // IDだけを抽出
            $id = pathinfo($path, PATHINFO_FILENAME);
        } else {
            // URLでは無いっぽい
        }

        // まずBrowse APIで取得を試行
        $ret = $this->getItemByBrowseAPI($id, $token);

        if ($ret['result'] === 'success') {
            // Browse APIで成功した場合、不足している情報をTrading APIで補完
            $tradingData = $this->getItemByTradingAPI($id, $token);
            if ($tradingData['result'] === 'success') {
                // Trading APIのデータでBrowse APIのデータを補完
                $ret['data'] = $this->mergeItemData($ret['data'], $tradingData['data']);
            }
            return response()->json($ret);
        } else {
            // Browse APIで失敗した場合、Trading APIを試行
            return response()->json($this->getItemByTradingAPI($id, $token));
        }
    }

    // getItemByBrowseAPI メソッドの修正
    private function getItemByBrowseAPI($id, $token)
    {
        $client = new GuzzleClient();

        $response = $client->request('GET', config('ebay.api_endpoint') . '/buy/browse/v1/item/v1|' . $id . '|0', [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . $token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
                'X-EBAY-C-MARKETPLACE-ID' => ['EBAY_US'],
                'X-EBAY-C-ENDUSERCTX' => ['contextualLocation=country=US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);

        Log::setDefaultDriver('exhibits');
        Log::info('Browse API Response: ' . $response_body);

        if ($response->getStatusCode() != 200) {
            return [
                'result' => 'failed',
                'data' => $obj,
                'id' => $id,
                'source' => 'browse_api'
            ];
        }

        $ret = [];
        $ret["category_id"] = isset($obj["categoryId"]) ? $obj["categoryId"] : '';
        $ret["title"] = isset($obj["title"]) ? $obj["title"] : '';

        // より詳細な説明情報を取得
        $ret["description"] = '';
        if (isset($obj["description"])) {
            $ret["description"] = $obj["description"];
        } elseif (isset($obj["shortDescription"])) {
            $ret["description"] = $obj["shortDescription"];
        }

        $ret["condition"] = isset($obj["conditionId"]) ? $obj["conditionId"] : '';
        $ret["condition_descriptors"] = isset($obj["conditionDescriptors"]) ? $obj["conditionDescriptors"] : [];
        $ret["localized_aspects"] = [];

        if (isset($obj["localizedAspects"])) {
            foreach ($obj["localizedAspects"] as $row) {
                $ret["localized_aspects"][$row["name"]][0] = $row["value"];
            }
        }

        // 追加情報も取得
        if (isset($obj["product"]["aspects"])) {
            foreach ($obj["product"]["aspects"] as $aspect) {
                $name = $aspect["name"];
                $values = isset($aspect["values"]) ? $aspect["values"] : [];
                if (!isset($ret["localized_aspects"][$name])) {
                    $ret["localized_aspects"][$name] = $values;
                }
            }
        }

        return [
            'result' => 'success',
            'data' => $ret,
            'source' => 'browse_api'
        ];
    }

    // Trading API用の新規メソッド（修正版）
    private function getItemByTradingAPI($id, $token)
    {
        $client = new GuzzleClient();

        // Trading APIのXMLリクエストを構築（ユーザートークン使用）
        $xmlRequest = '<?xml version="1.0" encoding="utf-8"?>
    <GetItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
        <RequesterCredentials>
            <eBayAuthToken>' . $token . '</eBayAuthToken>
        </RequesterCredentials>
        <ItemID>' . $id . '</ItemID>
        <DetailLevel>ReturnAll</DetailLevel>
        <IncludeItemSpecifics>true</IncludeItemSpecifics>
    </GetItemRequest>';

        $response = $client->request('POST', config('ebay.traditional.api'), [
            'http_errors' => false,
            'headers' => [
                'X-EBAY-API-COMPATIBILITY-LEVEL' => config('ebay.traditional.level'),
                'X-EBAY-API-DEV-NAME' => config('ebay.dev_id'),
                'X-EBAY-API-APP-NAME' => config('ebay.app_id'),
                'X-EBAY-API-CERT-NAME' => config('ebay.cert_id'),
                'X-EBAY-API-CALL-NAME' => 'GetItem',
                'X-EBAY-API-SITEID' => '0',
                'Content-Type' => 'text/xml',
            ],
            'body' => $xmlRequest,
        ]);

        $response_body = $response->getBody()->getContents();

        Log::setDefaultDriver('exhibits');
        Log::info('Trading API Request XML: ' . $xmlRequest);
        Log::info('Trading API Response: ' . $response_body);

        if ($response->getStatusCode() != 200) {
            return [
                'result' => 'failed',
                'data' => ['message' => 'Trading API request failed', 'status_code' => $response->getStatusCode()],
                'id' => $id,
                'source' => 'trading_api'
            ];
        }

        // XMLレスポンスをパース
        $xml = simplexml_load_string($response_body);

        if (!$xml || (string) $xml->Ack !== 'Success') {
            $errorMessage = 'Trading API returned error';
            if ($xml && isset($xml->Errors)) {
                $errorMessage = (string) $xml->Errors->LongMessage;
            }
            return [
                'result' => 'failed',
                'data' => ['message' => $errorMessage],
                'id' => $id,
                'source' => 'trading_api'
            ];
        }

        $item = $xml->Item;
        $ret = [];

        $ret["category_id"] = (string) $item->PrimaryCategory->CategoryID;
        $ret["title"] = (string) $item->Title;
        $ret["description"] = (string) $item->Description;
        $ret["condition"] = (string) $item->ConditionID;
        $ret["condition_descriptors"] = [];
        $ret["localized_aspects"] = [];

        // ItemSpecificsから商品詳細を取得
        if (isset($item->ItemSpecifics->NameValueList)) {
            foreach ($item->ItemSpecifics->NameValueList as $spec) {
                $name = (string) $spec->Name;
                $values = [];
                // 複数の値がある場合に対応
                if (isset($spec->Value)) {
                    if (is_array($spec->Value)) {
                        foreach ($spec->Value as $value) {
                            $values[] = (string) $value;
                        }
                    } else {
                        $values[] = (string) $spec->Value;
                    }
                }
                $ret["localized_aspects"][$name] = $values;
            }
        }

        return [
            'result' => 'success',
            'data' => $ret,
            'source' => 'trading_api'
        ];
    }

    // Browse APIとTrading APIのデータをマージするメソッド
    private function mergeItemData($browseData, $tradingData)
    {
        // Browse APIのデータを基本とし、不足している項目をTrading APIで補完
        $merged = $browseData;

        // 各項目をチェックして、Browse APIで取得できなかった項目をTrading APIで補完
        if (empty($merged["description"]) && !empty($tradingData["description"])) {
            $merged["description"] = $tradingData["description"];
        }

        if (empty($merged["condition"]) && !empty($tradingData["condition"])) {
            $merged["condition"] = $tradingData["condition"];
        }

        if (empty($merged["condition_descriptors"]) && !empty($tradingData["condition_descriptors"])) {
            $merged["condition_descriptors"] = $tradingData["condition_descriptors"];
        }

        // localized_aspectsをマージ（Trading APIの項目で補完）
        if (!empty($tradingData["localized_aspects"])) {
            foreach ($tradingData["localized_aspects"] as $key => $value) {
                if (!isset($merged["localized_aspects"][$key])) {
                    $merged["localized_aspects"][$key] = $value;
                }
            }
        }

        // データソース情報を追加（デバッグ用）
        $merged["data_source"] = "browse_api_with_trading_supplement";

        return $merged;
    }

    // 特定カテゴリーの項目を取得返送する
    public function getCategoryAspect(Request $request)
    {
        $id = $request->input('id');
        $token = $request->input('token');
        $marketplace_id = $request->input('marketplace_id');

        if (!$id) {
            return response()->json([
                'result' => 'failed',
                'message' => 'invalid param:id',
            ]);
        }
        if (!$token) {
            return response()->json([
                'result' => 'failed',
                'message' => 'invalid param:token',
            ]);
        }
        if (!$marketplace_id) {
            $marketplace_id = 'EBAY_US';
        }

        $client = null;
        $client = new GuzzleClient();

        //// マーケットプレイス毎のカテゴリーツリーIDを取得
        $response = $client->request('GET', config('ebay.api_endpoint') . '/commerce/taxonomy/v1/get_default_category_tree_id?marketplace_id=' . $marketplace_id, [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . $token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);
        if ($response->getStatusCode() != 200) {
            // 失敗？
            return response()->json([
                'result' => 'failed',
                'data' => $obj,
            ]);
        }
        $category_tree_id = $obj["categoryTreeId"];
        $category_treeVersion = $obj["categoryTreeVersion"];

        $client = null;
        $client = new GuzzleClient();

        //// カテゴリーツリーIDに紐づく各種カテゴリーを取得
        // くっそ重いのでBatch等でデイリーでなんとかする
        /*
        $response = $client->request('GET',config('ebay.api_endpoint').'/commerce/taxonomy/v1/category_tree/'.$category_tree_id,[
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer '.Admin::user()->access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body,true);
        */

        // カテゴリー別の項目を取得
        $response = $client->request('GET', config('ebay.api_endpoint') . '/commerce/taxonomy/v1/category_tree/' . $category_tree_id . '/get_item_aspects_for_category?category_id=' . $id, [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . $token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);


        if ($response->getStatusCode() != 200) {
            // 失敗？
            return response()->json([
                'result' => 'failed',
                'data' => $obj,
            ]);
        }

        if (!isset($obj["aspects"])) {
            // 失敗？
            return response()->json([
                'result' => 'failed',
                'data' => $obj,
            ]);
        }

        $ret = [];
        // 特定条件の
        $dependents = [];

        // 各項目
        foreach ($obj["aspects"] as $row) {
            // 項目名
            $localizedAspectName = $row["localizedAspectName"];
            $aspectValues = [];

            // アスペクトに紐づく項目
            $applicableForLocalizedAspectName = []; // 紐づく項目のセーブ用
            $is_dependent = false;

            // 項目情報
            $constraint = $row["aspectConstraint"];
            // 必須
            $required = $constraint["aspectRequired"];
            // データタイプ
            $dataType = $constraint["aspectDataType"];
            // フォーマット、データタイプがDATEかNUMBERのときにある
            $format = isset($constraint["aspectFormat"]) ? $constraint["aspectFormat"] : '';
            // アスペクト最大長
            $maxLength = isset($constraint["aspectMaxLength"]) ? $constraint["aspectMaxLength"] : -1;
            // 入力タイプ テキストorセレクトのみ
            $mode = $constraint["aspectMode"];
            // 推奨orオプション
            $usage = $constraint["aspectUsage"];
            // 期待される必須の時間？
            $requiredByDate = isset($constraint["expectedRequiredByDate"]) ? $constraint["expectedRequiredByDate"] : "";
            // 複数設定可能か？
            $itemToAspectCardinality = $constraint["itemToAspectCardinality"];
            // バリエーションに有効？？
            $enabledForVariations = $constraint["aspectEnabledForVariations"];

            if (isset($row['aspectValues'])) {
                foreach ($row['aspectValues'] as $row2) {
                    //$row2 = $row["aspectValues"][$i];
                    // プリセットのセレクトできる項目名
                    $aspectValues[] = $row2['localizedValue'];

                    // 依存するルールチェック
                    if (isset($row2["valueConstraints"])) {
                        // なにかの項目に依存するらしい
                        $is_dependent = true;
                        foreach ($row2["valueConstraints"] as $row3) {
                            // 紐づく項目たちを保存しておく
                            $applicableForLocalizedAspectName[] = $row3["applicableForLocalizedAspectName"];
                            // 紐づく項目名とそれが該当する値たちを保存
                            foreach ($row3["applicableForLocalizedAspectValues"] as $row4) {
                                // このキーだったときだけlocalizedValueを出現させる
                                $dependents[$row3["applicableForLocalizedAspectName"]][$row4][$localizedAspectName][] = $row2['localizedValue'];
                            }
                        }
                    }
                }
                $applicableForLocalizedAspectName = array_unique($applicableForLocalizedAspectName);
            }

            $ret['aspects'][] = [
                'name' => $localizedAspectName,
                'required' => $required,
                'dataType' => $dataType,
                'format' => $format,
                'maxLength' => $maxLength,
                'mode' => $mode,
                'usage' => $usage,
                'requiredByDate' => $requiredByDate,
                'itemToAspectCardinality' => $itemToAspectCardinality,
                'enabledForVariations' => $enabledForVariations,
                'applicableForLocalizedAspectName' => $applicableForLocalizedAspectName,
                'aspectValues' => $aspectValues
            ];
        }
        $ret["dependents"] = $dependents;

        // コンディション情報の項目を取得
        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/metadata/v1/marketplace/' . $marketplace_id . '/get_item_condition_policies', [
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer ' . $token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
            'query' => [
                'filter' => 'categoryIds:{' . $id . '}'
            ],
        ]);

        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body, true);

        $ret["condition_policies"] = $obj;

        return response()->json([
            'result' => 'success',
            'data' => $ret,
        ]);
    }

    /**
     * eBay Inventory Item APIリクエストを実行するヘルパーメソッド
     */
    private function tryInventoryItemRequest($client, $sku, $request_body, $form_model_id = null)
    {
        try {
            $response = $client->request('PUT', config('ebay.api_endpoint') . '/sell/inventory/v1/inventory_item/' . $sku, [
                'http_errors' => false,
                'headers' => [
                    'Authorization' => [
                        'Bearer ' . Admin::user()->access_token,
                    ],
                    'Accept' => ['application/json'],
                    'Content-Type' => ['application/json'],
                    'Content-Language' => ['en-US'],
                ],
                'json' => $request_body,
            ]);

            $body = $response->getBody()->getContents();
            $success = ($response->getStatusCode() == 200 || $response->getStatusCode() == 201 || $response->getStatusCode() == 204);

            Log::info('tryInventoryItemRequest結果: HTTP' . $response->getStatusCode() . ' / 成功: ' . ($success ? 'Yes' : 'No'));
            Log::info('tryInventoryItemRequestレスポンス: ' . $body);

            return [
                'success' => $success,
                'response' => $response,
                'body' => $body,
                'status_code' => $response->getStatusCode()
            ];
        } catch (Exception $e) {
            Log::error('tryInventoryItemRequest例外: ' . $e->getMessage());
            return [
                'success' => false,
                'response' => null,
                'body' => 'Exception: ' . $e->getMessage(),
                'status_code' => 500
            ];
        }
    }

    /**
     * SKUを生成する
     *
     * @param array $productInfo
     * @return string
     */
    private function generateSKU($productInfo)
    {
        $timestamp = date('YmdHis');
        $baseSKU = $productInfo['base_sku'] ?? 'PROD';
        $uniqueSKU = "{$baseSKU}_{$timestamp}";

        // SKUの重複チェック
        $existingSKU = DB::table('exhibits')
            ->where('sku', $uniqueSKU)
            ->first();

        if ($existingSKU) {
            // 重複している場合は新しいタイムスタンプで再試行
            return $this->generateSKU($productInfo);
        }

        return $uniqueSKU;
    }

    /**
     * 商品情報取得時のSKU生成処理
     *
     * @param array $productInfo
     * @return string
     */
    public function generateProductSKU($productInfo)
    {
        $baseSKU = '';
        
        // 商品情報からベースSKUを生成
        if (isset($productInfo['title'])) {
            // タイトルから最初の3文字を取得（英数字のみ）
            $title = preg_replace('/[^a-zA-Z0-9]/', '', $productInfo['title']);
            $baseSKU = substr($title, 0, 3);
        }
        
        if (empty($baseSKU)) {
            $baseSKU = 'PROD';
        }
        
        $productInfo['base_sku'] = $baseSKU;
        return $this->generateSKU($productInfo);
    }

    /**
     * SKUの状態を更新する
     *
     * @param string $sku
     * @param string $status
     * @param string|null $errorMessage
     * @return void
     */
    private function updateSKUStatus($sku, $status, $errorMessage = null)
    {
        $updateData = [
            'sku_status' => $status,
            'sku_retry_count' => DB::raw('sku_retry_count + 1')
        ];

        if ($status === 'deleted') {
            $updateData['sku_deleted_at'] = now();
        } elseif ($status === 'error') {
            $updateData['sku_error_message'] = $errorMessage;
        }

        DB::table('exhibits')
            ->where('sku', $sku)
            ->update($updateData);
    }

    /**
     * SKU履歴を記録する
     *
     * @param string $sku
     * @param string $action
     * @param array $details
     * @return void
     */
    private function recordSKUHistory($sku, $action, $details)
    {
        DB::table('sku_history')->insert([
            'sku' => $sku,
            'action' => $action,
            'details' => json_encode($details),
            'action_date' => now()
        ]);
    }

    /**
     * 古いSKUをクリーンアップする
     *
     * @return void
     */
    private function cleanupOldSKUs()
    {
        DB::table('exhibits')
            ->where('sku_status', 'deleted')
            ->where('sku_deleted_at', '<', now()->subDays(30))
            ->update(['sku_status' => 'active']);
    }

    /**
     * 出品処理を実行する
     *
     * @param array $productInfo
     * @return array
     */
    private function createListing($productInfo)
    {
        try {
            // 一意のSKUを生成
            $uniqueSKU = $this->generateSKU($productInfo);
            
            // 在庫アイテムの作成
            $inventoryItem = [
                'sku' => $uniqueSKU,
                'product' => [
                    'title' => $productInfo['title'],
                    'description' => $productInfo['description'],
                    'aspects' => $productInfo['aspects'] ?? [],
                    'imageUrls' => $productInfo['imageUrls'] ?? []
                ]
            ];

            // 在庫アイテムの作成を試行
            $inventoryResponse = $this->tryInventoryItemRequest(
                $this->getClient(),
                $uniqueSKU,
                $inventoryItem
            );
            
            // オファーの作成
            $offer = [
                'sku' => $uniqueSKU,
                'price' => $productInfo['price'],
                'quantity' => $productInfo['quantity'],
                'listingDescription' => $productInfo['description'],
                'listingPolicies' => [
                    'fulfillmentPolicyId' => $this->getFulfillmentPolicy($productInfo['marketplace_id']),
                    'paymentPolicyId' => $this->getPaymentPolicy($productInfo['marketplace_id']),
                    'returnPolicyId' => $this->getReturnPolicy($productInfo['marketplace_id'])
                ]
            ];

            // オファーの作成を試行
            $offerResponse = $this->createOffer($offer);
            
            // オファーの公開
            $this->publishOffer($offerResponse['offerId']);

            // SKU履歴の記録
            $this->recordSKUHistory($uniqueSKU, 'create', [
                'inventoryResponse' => $inventoryResponse,
                'offerResponse' => $offerResponse
            ]);

            return [
                'success' => true,
                'sku' => $uniqueSKU,
                'inventoryItem' => $inventoryResponse,
                'offer' => $offerResponse
            ];

        } catch (\Exception $error) {
            // エラーハンドリング
            if (strpos($error->getMessage(), 'offer entry already exists') !== false) {
                // SKU重複エラーの場合、新しいSKUで再試行
                $this->updateSKUStatus($uniqueSKU, 'error', $error->getMessage());
                return $this->createListing($productInfo);
            }

            // その他のエラー
            $this->updateSKUStatus($uniqueSKU, 'error', $error->getMessage());
            throw $error;
        }
    }

    /**
     * 商品情報取得APIのレスポンス処理
     *
     * @param array $response
     * @return array
     */
    private function handleProductInfoResponse($response)
    {
        if ($response['status'] === 'ok') {
            $productInfo = $response['product'];
            
            // SKUを自動生成
            $generatedSKU = $this->generateProductSKU($productInfo);
            
            // フォームに設定する値を返す
            return [
                'title' => $productInfo['title'] ?? '',
                'product_description' => $productInfo['description'] ?? '',
                'sku' => $generatedSKU,
                'product_image' => $productInfo['product_image'] ?? '',
                'referer_price' => $productInfo['price'] ?? 0
            ];
        }
        
        return [];
    }

    public function getAmazonItem(Request $request)
    {
        $url = $request->input('url');
        
        if (empty($url)) {
            return response()->json([
                'status' => 'error',
                'message' => 'URLが指定されていません。'
            ]);
        }

        // URLがアマゾンのものかチェック
        if (!preg_match('/amazon\.(co\.jp|com)/', $url)) {
            return response()->json([
                'status' => 'error',
                'message' => 'アマゾンのURLではありません。'
            ]);
        }

        try {
            $command = "node " . base_path('add_new/get_amazon_product.js') . " " . escapeshellarg($url);
            $output = shell_exec($command);
            $productData = json_decode($output, true);

            if (!$productData || $productData['state'] === 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => '商品情報の取得に失敗しました。'
                ]);
            }

            // 商品情報を整形
            $formattedData = [
                'title' => $productData['product_title'],
                'product_description' => $productData['product_comment'],
                'price' => floatval(str_replace(',', '', $productData['price'])),
                'images' => $productData['images'],
                'asin' => $productData['asin'],
                'brand' => $productData['brand'],
                'category' => $productData['category'],
                'referer_service' => 'amazon',
                'referer_id' => $productData['asin']
            ];

            return response()->json([
                'status' => 'success',
                'data' => $formattedData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '商品情報の取得中にエラーが発生しました: ' . $e->getMessage()
            ]);
        }
    }
}

<?php

namespace App\Admin\Controllers;

use Illuminate\Http\Request;

use App\Models\Exhibit;
use App\Models\ExhibitTemplate;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use GuzzleHttp\Client as GuzzleClient;

use Illuminate\Support\Facades\DB;

class ExhibitController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '出品情報';

/*
以下の必要JSは「/srv/app/app/Admin/bootstrap.php」で指定
/js/get_product_ui.css
/js/get_product_ui.js
*/

//アップロード画像表示エリア
protected $image_sample_html = <<<'HTMLSTRING'
<div class="sample_image_display_area"></div>
<script>
	setDisplayImage();
</script>
HTMLSTRING;

//商品情報取得ボタン
protected $get_product_html = <<<'HTMLSTRING'
<div class="get_product_info_area"></div>
<script>
	<!--[タイトル、説明、画像のセレクターを指定]-->
	get_product_info_config['title_set_selecter'] = 'input[name="title"]';
	get_product_info_config['comment_set_selecter'] = 'textarea[name="product_description"]';
	get_product_info_config['image_set_selecter'] = '#hidden_product_image';
	get_product_info_config['image_sample_set_selecter'] = '.sample_image_display_area';
	setGetProductUI();
</script>
HTMLSTRING;

//画像アップロードボタン
protected $image_uploder_html = <<<'HTMLSTRING'
<div class="get_uploder_area"></div>
<script>
	setUploaderUI();
</script>
HTMLSTRING;

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Exhibit());
		
		$grid->column('id', 'ID')->sortable();
		if(!Admin::user()->isRole('member')){
			$grid->column('admin_user_id', trans('admin.username'));
		}
		$grid->sku(trans('admin.sku'));
		$grid->title(trans('admin.title'));
		$grid->price(trans('admin.price'));

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
		$row = Exhibit::findOrFail($id);
        $show = new Show($row);
		
		$show->field('id','ID');
		$show->field('title',trans('admin.title'));
		$show->field('category_id',trans('admin.category_id'));
		$show->field('sku',trans('admin.sku'));
		$show->field('stock_quantity',trans('admin.stock_quantity'));
		$show->field('price',trans('admin.price'));
		$show->field('condition',trans('admin.condition'));
		$show->image('product_image',trans('admin.product_image'));
		$show->field('is_scale',trans('admin.is_scale'));
		$show->field('is_advertising',trans('admin.is_advertising'));
		$show->field('color',trans('admin.color'));
		$show->field('ca_prop_65',trans('admin.ca_prop_65'));
		$show->field('is_lot_sales',trans('admin.is_lot_sales'));
		$show->field('product_type',trans('admin.product_type'));
		$show->field('production_area',trans('admin.production_area'));
		$show->field('origin_date',trans('admin.origin_date'));
		$show->field('product_description',trans('admin.product_description'));
		$show->field('format',trans('admin.format'))->using([
			0 => '未選択',
			1 => '固定',
			2 => 'オークション',
		]);
		
		if($row->format == 1){
			$show->field('fixed_listing_end_at',trans('admin.fixed_listing_end_at'))->format('YYYY-MM-DD');
			$show->field('fixed_listing_start_at',trans('admin.fixed_listing_start_at'))->format('YYYY-MM-DD');
			$show->field('fixed_buyout_price',trans('admin.fixed_buyout_price'))->min(0);
			$show->field('is_fixed_negotiable_price',trans('admin.is_fixed_negotiable_price'))->options([0 => '許可しない',1 => '許可する']);
			$show->field('fixed_negotiable_price_message',trans('admin.fixed_negotiable_price_message'));
			$show->field('fixed_quantity',trans('admin.fixed_quantity'))->min(0);
		} else if($row->format == 2){
			$show->field('auction_listing_end_at',trans('admin.auction_listing_end_at'))->format('YYYY-MM-DD');
			$show->field('auction_listing_start_at',trans('admin.auction_listing_start_at'))->format('YYYY-MM-DD');
			$show->field('auction_start_price',trans('admin.auction_start_price'))->min(0);
			$show->field('auction_buyout_price',trans('admin.auction_buyout_price'))->min(0);
			$show->field('auction_lowest_price',trans('admin.auction_lowest_price'))->min(0);
			$show->field('is_auction_negotiable_price',trans('admin.is_auction_negotiable_price'))->options([0 => '許可しない',1 => '許可する']);
			$show->field('auction_negotiable_price_message',trans('admin.auction_negotiable_price_message'));
			$show->field('auction_quantity',trans('admin.auction_quantity'))->min(0);
		}

		$show->field('is_private_listing',trans('admin.is_private_listing'))->using([
			0 => 'FALSE',
			1 => 'TRUE',
		]);
		$show->field('is_charity',trans('admin.is_charity'))->using([
			0 => 'FALSE',
			1 => 'TRUE',
		]);
		$show->field('tax',trans('admin.tax'));
		$show->field('payment_rule',trans('admin.payment_rule'))->using([
			0 => 'FALSE',
			1 => 'TRUE',
		]);
		$show->field('delivery_rule',trans('admin.delivery_rule'))->using([
			0 => 'FALSE',
			1 => 'TRUE',
		]);
		$show->field('return_rule',trans('admin.return_rule'))->using([
			0 => 'FALSE',
			1 => 'TRUE',
		]);
		$show->field('product_size',trans('admin.product_size'));
		$show->field('product_weight',trans('admin.product_weight'));
		$show->field('delivery_source_area',trans('admin.delivery_source_area'));
		

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
		$exhibit = new Exhibit();
		
		$form = new Form($exhibit);
		
		// クエリ文字取得 & js生成
		$template_id = isset($_GET["template"]) ? $_GET["template"] : null;
		$script = <<<SCRIPT
$('select[name=exhibit_template_id]').change(function(){
	let val = $(this).val();
	if(val > 0){
		let ret = window.confirm("テンプレートを呼び出しますか？");
		if(ret == true){
			window.location = $(location).attr('origin') + $(location).attr('pathname') + '?template=' + val;
		}
	}
});

SCRIPT;
		Admin::script($script);
		
		// テンプレのセレクトボックスを生成
		$templates = new ExhibitTemplate();
		$template = ExhibitTemplate::where('id',$template_id)->first();
		$arr = array();
		foreach($templates::where('admin_user_id', Admin::user()->id)->get() as $row){
			$arr[$row->id] = $row->title;
		}
		$form->select('exhibit_template_id',trans('admin.exhibit_template_id'))->options($arr)->default(isset($template->id) ? $template->id : 0);
		
		$form->hidden('admin_user_id')->default(Admin::user()->id);
		
		// 
		$form->html($this->get_product_html);
		$form->text('title',trans('admin.title'))->default(isset($template->title) ? $template->title : '');
		
		//$form->image('product_image',trans('admin.product_image'));
		
		$form->number('category_id',trans('admin.category_id'))->min(0);
		$form->text('sku',trans('admin.sku'))->default(isset($template->sku) ? $template->sku : '')->required();
		$form->number('stock_quantity',trans('admin.stock_quantity'))->min(0)->required();
		$form->number('quantity_limit_per_buyer',trans('admin.quantity_limit_per_buyer'))->min(0)->required();
		$form->decimal('price',trans('admin.price'))->required();
		$form->select('condition',trans('admin.condition'))->options([
			'NEW' => '新品(未開封)',
			'LIKE_NEW' => '新古品',
			'NEW_OTHER' => '新品(開封済)',
			'NEW_WITH_DEFECTS' => '新品(欠損)',
			// 'MANUFACTURER_REFURBISHED' => 'メーカー再生品', // もう使えない
			// 'CERTIFIED_REFURBISHED' => 'メーカー再生品', // これももう使えないらしい
			// 'EXCELLENT_REFURBISHED' => 'メーカー再生品', // カテゴリーがスマホのみ利用可能らしい
			// 'VERY_GOOD_REFURBISHED' => 'メーカー修理品', // カテゴリーがスマホのみ利用可能らしい
			// 'GOOD_REFURBISHED',
			// 'SELLER_REFURBISHED' => '修理品',
			'USED_EXCELLENT' => '中古(最優良)',
			'USED_VERY_GOOD' => '中古(優良)',
			'USED_GOOD' => '中古(良)',
			'USED_ACCEPTABLE' => '中古',
			'FOR_PARTS_OR_NOT_WORKING' => 'ジャンク品',
		]);
		$form->text('condition_description',trans('admin.condition_description'));
		//$form->image('product_image',trans('admin.product_image'))->default(isset($template->product_image) ? $template->product_image : '');
		$form->hidden('product_image')->attribute(['id' => 'hidden_product_image']);//画像はパスをhiddenで保持
		$form->html($this->image_sample_html, trans('admin.product_image'));
		$form->html($this->image_uploder_html);
		
		// APIで指定できない
		//$form->select('is_scale',trans('admin.is_scale'))->options([
		//	0 => '拡大許可しない',
		//	1 => '拡大許可する',
		//])->default(isset($template->is_scale) ? $template->is_scale : 0);
		//$form->select('is_advertising',trans('admin.is_advertising'))->options([
		//	0 => '広告許可しない',
		//	1 => '広告許可する',
		//])->default(isset($template->is_advertising) ? $template->is_advertising : 0);
		//$form->text('color',trans('admin.color'));
		
		// APIで指定できない？
		// $form->text('ca_prop_65',trans('admin.ca_prop_65'));
		$form->radio('is_lot_sales',trans('admin.is_lot_sales'))->options([
			0 => '許可しない',
			1 => '許可する'
		])->when(1,function(Form $form){
			$form->number('lot_size',trans('admin.lot_size'))->min(0);
		});
		$form->select('product_type',trans('admin.product_type'))->options([
			0 => '未選択',
			1 => 'オリジナル',
			2 => '複製品',
		]);
		
		// APIで指定できない
		//$form->number('production_area',trans('admin.production_area'));
		//$form->date('origin_date',trans('admin.origin_date'))->format('YYYY-MM-DD');
		$form->textarea('product_description',trans('admin.product_description'));
		
		$form->radio('format',trans('admin.format'))->options([
			0 => '未選択',
			1 => '固定',
			2 => 'オークション',
		])->when(1,function(Form $form){
			$form->select('fixed_listing_end_at',trans('admin.fixed_listing_end_at'))->options([
				'DAYS_1' => '1日',
				'DAYS_3' => '3日',
				'DAYS_5' => '5日',
				'DAYS_7' => '7日',
				'DAYS_10' => '10日',
				'DAYS_21' => '21日',
				'DAYS_30' => '30日',
				'GTC' => '無期限',
			]);
			$form->date('fixed_listing_start_at',trans('admin.fixed_listing_start_at'))->format('YYYY-MM-DD');
			$form->number('fixed_buyout_price',trans('admin.fixed_buyout_price'))->min(0);
			
			// APIに交渉許可ない
			// $form->select('is_fixed_negotiable_price',trans('admin.is_fixed_negotiable_price'))->options([0 => '許可しない',1 => '許可する']);
			// $form->text('fixed_negotiable_price_message',trans('admin.fixed_negotiable_price_message'));
			$form->number('fixed_quantity',trans('admin.fixed_quantity'))->min(0);
		})->when(2,function(Form $form){
			$form->select('auction_listing_end_at',trans('admin.auction_listing_end_at'))->options([
				'DAYS_1' => '1日',
				'DAYS_3' => '3日',
				'DAYS_5' => '5日',
				'DAYS_7' => '7日',
				'DAYS_10' => '10日',
				'DAYS_21' => '21日',
				'DAYS_30' => '30日',
			]);
			$form->date('auction_listing_start_at',trans('admin.auction_listing_start_at'))->format('YYYY-MM-DD');
			$form->number('auction_start_price',trans('admin.auction_start_price'))->min(0);
			$form->number('auction_buyout_price',trans('admin.auction_buyout_price'))->min(0);
			$form->number('auction_lowest_price',trans('admin.auction_lowest_price'))->min(0);
			
			// APIに交渉許可ない
			//$form->select('is_auction_negotiable_price',trans('admin.is_auction_negotiable_price'))->options([0 => '許可しない',1 => '許可する']);
			//$form->text('auction_negotiable_price_message',trans('admin.auction_negotiable_price_message'));
			$form->number('auction_quantity',trans('admin.auction_quantity'))->min(0);
		});
		
		
		$form->select('is_private_listing',trans('admin.is_private_listing'))->options([
			0 => '無効',
			1 => '有効',
		]);
		//$form->select('is_charity',trans('admin.is_charity'))->options([
		//	0 => 'FALSE',
		//	1 => 'TRUE',
		//]);
		$form->number('tax',trans('admin.tax'))->min(0)->max(100);
		//$form->select('payment_rule',trans('admin.payment_rule'))->options([
		//	0 => 'FALSE',
		//	1 => 'TRUE',
		//]);
		//$form->select('delivery_rule',trans('admin.delivery_rule'))->options([
		//	0 => 'FALSE',
		//	1 => 'TRUE',
		//]);
		//$form->select('return_rule',trans('admin.return_rule'))->options([
		//	0 => 'FALSE',
		//	1 => 'TRUE',
		//]);
		// $form->text('product_size',trans('admin.product_size'));
		
		$form->select('product_size_unit',trans('admin.product_size_unit'))->options([
			'CENTIMETER' => 'センチメートル',
			'METER' => 'メートル',
			'INCH' => 'インチ',
			'FEET' => 'フィート',
		]);
		$form->decimal('product_size_height',trans('admin.product_size_height'));
		$form->decimal('product_size_width',trans('admin.product_size_width'));
		$form->decimal('product_size_length',trans('admin.product_size_length'));
		
		$form->select('product_weight_unit',trans('admin.product_weight_unit'))->options([
			'GRAM' => 'グラム',
			'KILOGRAM' => 'キログラム',
			'POUND' => 'パウンド',
			'OUNCE' => 'オンス',
		]);
		$form->decimal('product_weight',trans('admin.product_weight'));
		
		$form->select('delivery_source_area',trans('admin.delivery_source_area'))->options([
			'EBAY_US' => 'US(ebay.com)',
			'EBAY_JP' => '日本(ebay.co.jp)',
			//'EBAY_CA' => 'カナダ(ebay.ca)',
			//'EBAY_GB' => '英国(ebay.co.uk)',
			//'EBAY_AU' => 'オーストラリア(ebay.com.au)',
			//'EBAY_AT' => 'オーストリア(ebay.at)',
			//'EBAY_BE' => 'ベルギー(www.ebay.be)',
			//'EBAY_FR' => 'フランス(ebay.fr)',
			//'EBAY_FR' => 'フランス(www.ebay.fr)',
		])->when('EBAY_US',function(Form $form){
			$form->select('fullfilment_policy_id',trans('admin.fullfilment_policy_id'))->options($this->getFulfillmentPolicy('EBAY_US'));
			$form->select('payment_policy_id',trans('admin.payment_policy_id'))->options($this->getPaymentPolicy('EBAY_US'));
			$form->select('return_policy_id',trans('admin.return_policy_id'))->options($this->getReturnPolicy('EBAY_US'));
		})->when('EBAY_JP',function(Form $form){
			$form->select('fullfilment_policy_id',trans('admin.fullfilment_policy_id'))->options($this->getFulfillmentPolicy('EBAY_JP'));
			$form->select('payment_policy_id',trans('admin.payment_policy_id'))->options($this->getPaymentPolicy('EBAY_JP'));
			$form->select('return_policy_id',trans('admin.return_policy_id'))->options($this->getReturnPolicy('EBAY_JP'));
		})->required();
		
		$form->select('merchant_location_key',trans('admin.merchant_location_key'))->options(function(){
			$client = null;
			$client = new GuzzleClient();
			
			$response = $client->request('GET',env('EBAY_API_ENDPOINT').'/sell/inventory/v1/location',[
				'http_errors' => false,
				'headers' => [
					'Authorization' => [
						'Bearer '.Admin::user()->access_token,
					],
					'Accept' => ['application/json'],
					'Content-Type' => ['application/json'],
					'Content-Language' => ['en-US'],
				],
			]);
			
			$response_body = $response->getBody()->getContents();
			$obj = json_decode($response_body,true); 
			if($response->getStatusCode() != 200){
				// 失敗？
				return ['' => 'ロケーション未設定'];
			}
			if(!isset($obj["locations"])){
				// 失敗？
				return ['' => 'ロケーション未設定'];
			}
			$temp = ['' => 'ロケーション未設定'];;
			foreach($obj["locations"] as $row){
				if(isset($row["name"])) {
					$temp[$row['merchantLocationKey']] = $row['name'];
				}
			}
			return $temp;
		})->required();
		
		
		
		$form->saving(function(Form $form){
			if($form->is_lot_sales != 1){
				$form->is_lot_sales = 0;
			}
			//// 出品処理
			
			// １．在庫登録APIで在庫を作る
			// https://developer.ebay.com/api-docs/sell/inventory/resources/inventory_item/methods/createOrReplaceInventoryItem
			$client = null;
			$client = new GuzzleClient();
			$sku = $form->model()->sku;
			// リクエスト内容
			$request_body = [
				// 商品情報のメイン部分 https://developer.ebay.com/api-docs/sell/inventory/types/slr:Product
				'product' => [
					'title' => $form->model()->title, //タイトル
					'aspects' => [ // 追加の詳細情報らしい
						'Feature' => ["Water resistance", "GPS"],
						'CPU' => ["Dual-Core Processor"],
					],
					'description' => $form->model()->product_description,
					// 'upc' => ["888462079525"],
					'imageUrls' => [
						// TODO: アップロードが確定したら
						$form->model()->product_image,
						//"http://store.storeimages.cdn-apple.com/4973/as-images.apple.com/is/image/AppleInc/aos/published/images/4/2/42/stainless/42-stainless-sport-white-grid?wid=332&hei=392&fmt=jpeg&qlt=95&op_sharpen=0&resMode=bicub&op_usm=0.5,0.5,0,0&iccEmbed=0&layer=comp&.v=1472247760390",
						//"http://store.storeimages.cdn-apple.com/4973/as-images.apple.com/is/image/AppleInc/aos/published/images/4/2/42/ceramic/42-ceramic-sport-cloud-grid?wid=332&hei=392&fmt=jpeg&qlt=95&op_sharpen=0&resMode=bicub&op_usm=0.5,0.5,0,0&iccEmbed=0&layer=comp&.v=1472247758007",
					],
				],
				// 状態 https://developer.ebay.com/api-docs/sell/inventory/types/slr:ConditionEnum
				'condition' => $form->model()->condition,
				'conditionDescription' => $form->model()->condition_description,
				// 発送方法？梱包の詳細情報 https://developer.ebay.com/api-docs/sell/inventory/types/slr:PackageWeightAndSize
				'packageWeightAndSize' => [
					'dimensions' => [
						'height' => $form->model()->product_size_height,
						'length' => $form->model()->product_size_length,
						'width' => $form->model()->product_size_width,
						'unit' => $form->model()->product_size_unit,
					],
					'packageType' => 'MAILING_BOX',
					'weight' => [
						'value' => $form->model()->product_weight,
						'unit' => $form->model()->product_weight_unit,
					],
				],
				// 在庫数量指定とか？ https://developer.ebay.com/api-docs/sell/inventory/types/slr:Availability
				'availability' => [
					'shipToLocationAvailability' => [
						'quantity' => $form->model()->stock_quantity, // 在庫数量
					],
				],
			];
			
			$response = $client->request('PUT',env('EBAY_API_ENDPOINT').'/sell/inventory/v1/inventory_item/'.$sku,[
				'http_errors' => false,
				'headers' => [
					'Authorization' => [
						'Bearer '.Admin::user()->access_token,
					],
					'Accept' => ['application/json'],
					'Content-Type' => ['application/json'],
					'Content-Language' => ['en-US'],
				],
				'json' => $request_body,
			]);
			
			echo json_encode($request_body);
			
		});
		
		$form->saved(function(Form $form) use ($exhibit){
			$client = null;
			$client = new GuzzleClient();
			$sku = $form->model()->sku;
			
			$format = '';
			if($form->model()->format == 1){
				$format = 'FIXED_PRICE';
				$pricing_summary = [
					'price' => [
						'currency' => 'USD',
						'value' => $form->model()->fixed_buyout_price,
					]
				];
				$availableQuantity = $form->model()->fixed_quantity;
				$quantityLimitPerBuyer = $form->model()->quantity_limit_per_buyer;
				$date = new \DateTime($form->model()->fixed_listing_start_at, new \DateTimeZone("Asia/Tokyo"));
				$date->setTimezone(new \DateTimeZone('UTC'));
				$listing_start_date = $date->format('Y-m-d\TH:i:s').'Z';
			} else if($form->model()->format == 2) {
				$format = 'AUCTION';
				$pricing_summary = [
					'auctionStartPrice' => [
						'currency' => 'USD',
						'value' => $form->model()->auction_start_price,
					],
					'auctionReservePrice' => [
						'currency' => 'USD',
						'value' => $form->model()->auction_buyout_price,
					],
				];
				$form->model()->auction_quantity = 1;
				$form->model()->quantity_limit_per_buyer = 1;
				$availableQuantity = $form->model()->auction_quantity;
				$quantityLimitPerBuyer = $form->model()->quantity_limit_per_buyer;
				$date = new \DateTime($form->model()->auction_listing_start_at, new \DateTimeZone("Asia/Tokyo"));
				$date->setTimezone(new \DateTimeZone('UTC'));
				$listing_start_date = $date->format('Y-m-d\TH:i:s').'Z';
			}
			
			if($format){
				if($form->model()->tax != 0){
					$tax = [
						'applyTax' => true,
						'vatPercentage' => $form->model()->tax,
					];
				} else {
					$tax = [
						'applyTax' => false,
						'vatPercentage' => 0,
					];
				}
				
				// ２． 出品APIをキック
				// https://developer.ebay.com/api-docs/sell/inventory/resources/offer/methods/createOffer
				$request_body = [
					// 必須項目3つ
					'sku' => $form->model()->sku,
					// マーケットプレイスID 一旦USで
					// https://developer.ebay.com/api-docs/sell/inventory/types/slr:MarketplaceEnum
					'marketplaceId' => $form->model()->delivery_source_area,
					'format' => $format,
					// 事実上必須
					'pricingSummary' => $pricing_summary,
					'listingDescription' => $form->model()->product_description,
					'merchantLocationKey' => $form->model()->merchant_location_key,
					'availableQuantity' => $availableQuantity,
					'quantityLimitPerBuyer' => $quantityLimitPerBuyer,
					'categoryId' => $form->model()->category_id,
					// 
					'listingPolicies' => [
						'fulfillmentPolicyId' => $form->model()->fullfilment_policy_id,
						'paymentPolicyId' => $form->model()->payment_policy_id,
						'returnPolicyId' => $form->model()->return_policy_id,
					],
					//
					'hideBuyerDetails' => ($form->model()->is_private_listing ? "false" : "true"),
					'listingStartDate' => $listing_start_date,
					'Tax' => $tax,
					'includeCatalogProductDetails' => false,
				];
				if($form->model()->offer_id){
					// 更新API
					$response = $client->request('PUT',env('EBAY_API_ENDPOINT').'/sell/inventory/v1/offer/'.$form->model()->offer_id,[
						'http_errors' => false,
						'headers' => [
							'Authorization' => [
								'Bearer '.Admin::user()->access_token,
							],
							'Accept' => ['application/json'],
							'Content-Type' => ['application/json'],
							'Content-Language' => ['en-US'],
						],
						'json' => $request_body,
					]);
					$temp = DB::table('exhibits')->where('id',$form->model()->id)->update([
						'api_res' => $response->getBody()->getContents(),
					]);
				} else {
					// 新規登録API
					$response = $client->request('PUT',env('EBAY_API_ENDPOINT').'/sell/inventory/v1/offer',[
						'http_errors' => false,
						'headers' => [
							'Authorization' => [
								'Bearer '.Admin::user()->access_token,
							],
							'Accept' => ['application/json'],
							'Content-Type' => ['application/json'],
							'Content-Language' => ['en-US'],
						],
						'json' => $request_body,
					]);
					
					$temp = DB::table('exhibits')->where('id',$form->model()->id)->update([
						'api_res' => $response->getBody()->getContents(),
						//'api_res' => json_encode($request_body),
					]);
					
					if($response->getStatusCode() == 201){
						$content = json_decode($response->getBody()->getContents(),true);
						DB::table('exhibits')->where('id',$form->model()->id)->update([
							'offer_id' => $content["offerId"],
						]);
					}
				}
				
				
				//$temp = $exhibit->find($this->model()->id);
				//$temp->api_res = $response->getBody()->getContents();
				//$temp->save();
			}
		});
		
        return $form;
    }
	
	private function getFulfillmentPolicy($marketplace_id){
		$client = null;
		$client = new GuzzleClient();
		
		$response = $client->request('GET',env('EBAY_API_ENDPOINT').'/sell/account/v1/fulfillment_policy?marketplace_id='.$marketplace_id,[
			'http_errors' => false,
			'headers' => [
				'Authorization' => [
					'Bearer '.Admin::user()->access_token,
				],
				'Accept' => ['application/json'],
				'Content-Type' => ['application/json'],
				'Content-Language' => ['en-US'],
			],
		]);
		
		$response_body = $response->getBody()->getContents();
		$obj = json_decode($response_body,true); 
		if($response->getStatusCode() != 200){
			// 失敗？
			return ['0' => 'ポリシー未選択'];
		}
		if(!isset($obj["fulfillmentPolicies"])){
			// 失敗？
			return ['0' => 'ポリシー未選択'];
		}
		$temp = ['0' => 'ポリシー未選択'];;
		foreach($obj["fulfillmentPolicies"] as $row){
			if(isset($row["name"]) && $row["marketplaceId"] == $marketplace_id) {
				$temp[$row['fulfillmentPolicyId']] = $row['name'];
			}
		}
		return $temp;
	}
	
	private function getPaymentPolicy($marketplace_id){
		$client = null;
		$client = new GuzzleClient();
		
		$response = $client->request('GET',env('EBAY_API_ENDPOINT').'/sell/account/v1/payment_policy?marketplace_id='.$marketplace_id,[
			'http_errors' => false,
			'headers' => [
				'Authorization' => [
					'Bearer '.Admin::user()->access_token,
				],
				'Accept' => ['application/json'],
				'Content-Type' => ['application/json'],
				'Content-Language' => ['en-US'],
			],
		]);
		
		$response_body = $response->getBody()->getContents();
		$obj = json_decode($response_body,true); 
		if($response->getStatusCode() != 200){
			// 失敗？
			return ['0' => 'ポリシー未選択'];
		}
		if(!isset($obj["paymentPolicies"])){
			// 失敗？
			return ['0' => 'ポリシー未選択'];
		}
		$temp = ['0' => 'ポリシー未選択'];;
		foreach($obj["paymentPolicies"] as $row){
			if(isset($row["name"]) && $row["marketplaceId"] == $marketplace_id) {
				$temp[$row['paymentPolicyId']] = $row['name'];
			}
		}
		return $temp;
	}
	
	private function getReturnPolicy($marketplace_id){
		$client = null;
		$client = new GuzzleClient();
		
		$response = $client->request('GET',env('EBAY_API_ENDPOINT').'/sell/account/v1/return_policy?marketplace_id='.$marketplace_id,[
			'http_errors' => false,
			'headers' => [
				'Authorization' => [
					'Bearer '.Admin::user()->access_token,
				],
				'Accept' => ['application/json'],
				'Content-Type' => ['application/json'],
				'Content-Language' => ['en-US'],
			],
		]);
		
		$response_body = $response->getBody()->getContents();
		$obj = json_decode($response_body,true); 
		if($response->getStatusCode() != 200){
			// 失敗？
			return ['0' => 'ポリシー未選択'];
		}
		if(!isset($obj["returnPolicies"])){
			// 失敗？
			return ['0' => 'ポリシー未選択'];
		}
		$temp = ['0' => 'ポリシー未選択'];;
		foreach($obj["returnPolicies"] as $row){
			if(isset($row["name"]) && $row["marketplaceId"] == $marketplace_id) {
				$temp[$row['returnPolicyId']] = $row['name'];
			}
		}
		return $temp;
	}
}

<?php

namespace App\Admin\Controllers;

use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

use App\Utilities\HtmlDomUtility;

class AdminapiController
{

	public function get_product_info(Request $request)
	{
		$result = false;

		$mode = ( ($request->mode) ? ($request->mode) : "" );
		$url = ( ($request->url) ? ($request->url) : "" );

		var_dump($mode);
		var_dump($url);
		exit();
	}
}

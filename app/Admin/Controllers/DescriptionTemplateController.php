<?php

namespace App\Admin\Controllers;

use App\Models\DescriptionTemplate;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Carbon\Carbon;

class DescriptionTemplateController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '商品説明テンプレート';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new DescriptionTemplate());

		if (Admin::user()->isRole('member') || Admin::user()->isRole('NPmember')) {
            $grid->model()->where('admin_user_id',Admin::user()->id);
        }
		
		if (!Admin::user()->isRole('member') && !Admin::user()->isRole('NPmember')) {
			$grid->column('id', __('Id'))->link(function($row){
				return route('admin.description_templates.edit', $row->id);
			},'')->sortable();
		}
        
        $grid->column('name', __('admin.name'))->link(function($row){
			return route('admin.description_templates.edit', $row->id);
		},'')->sortable();
		// 作成日時
        //$grid->created_at(__('admin.created_at'))->display(function () {
        //    return Carbon::parse($this->created_at)->format('Y-m-d H:i:s');
        //})->sortable();
		//
        //// 更新日時
        //$grid->updated_at(__('admin.updated_at'))->display(function () {
        //    return Carbon::parse($this->updated_at)->format('Y-m-d H:i:s');
        //})->sortable();
		
		$grid->filter(function($filter){
            $filter->disableIdFilter();
			$filter->like('name',trans('admin.name'));
		});

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(DescriptionTemplate::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('name', __('Name'));
        //$show->field('description', __('Description'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new DescriptionTemplate());

        $form->text('name', __('Name'));
		$user_id = 0;
		if (Admin::user()->isRole('member') || Admin::user()->isRole('NPmember')) {
			$user_id = Admin::user()->id;
		}
        $form->hidden('admin_user_id')->default($user_id);
        $form->ckeditor('description', trans('admin.product_description'));

        return $form;
    }
}

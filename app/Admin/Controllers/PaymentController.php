<?php

namespace App\Admin\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Encore\Admin\Facades\Admin;

class PaymentController extends AdminController
{
    public function payment(Request $request)
    {
        $user = Admin::user();

        if ($user->member_type != 1) {
            return redirect('/admin');
        }

        $user_id = $user->id;
        Log::setDefaultDriver('payment');
        Log::info('-- eBay連携処理開始 UserID : '.$user_id.' --');

        return redirect('/admin');
    }

    public function success(Request $request)
    {
        return view('admin.payment.result_success');
    }

    public function error(Request $request)
    {
        return view('admin.payment.result_error');
    }
}

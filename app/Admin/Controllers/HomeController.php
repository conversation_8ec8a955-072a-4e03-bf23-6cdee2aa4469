<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Controllers\Dashboard;
use Encore\Admin\Layout\Column;
use Encore\Admin\Layout\Content;
use Encore\Admin\Layout\Row;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    public function index(Content $content)
    {
		//return $content
        //    ->title('Dashboard')
        //    ->description('Description...')
        //    ->row(Dashboard::title())
        //    ->row(function (Row $row) {
		//
        //        $row->column(4, function (Column $column) {
        //            $column->append(Dashboard::environment());
        //        });
		//
        //        $row->column(4, function (Column $column) {
        //            $column->append(Dashboard::extensions());
        //        });
		//
        //        $row->column(4, function (Column $column) {
        //            $column->append(Dashboard::dependencies());
        //        });
        //    });

        return $content
            ->row(function (Row $row) {
                $row->column(12, function (Column $column) {
                    $column->append($this->ebayOAuth());
                    //$column->append($this->ebayAuthnAuth());
                });
				$row->column(12, function (Column $column) {
                    $column->append($this->contractStatus());
                    //$column->append($this->ebayAuthnAuth());
                });
            });
    }

	public static function ebayOAuth(){
		$class = config('admin.database.users_model');
		$userModel = new $class;
		$user = $userModel->where('id',Admin::user()->id)->first();

		$token = $user->ebay_token;
		$expires_in = $user->refresh_token_expires_in;
		$username = $user->ebay_username;
		if(!$expires_in){
			$expires_in = "未ログイン";
		}

		$url_query = [
			'client_id' => config('ebay.client_id'),
			'response_type' => 'code',
			'redirect_uri' => config('ebay.ru_name'),
			'scope' => 'https://api.ebay.com/oauth/api_scope https://api.ebay.com/oauth/api_scope/sell.inventory https://api.ebay.com/oauth/api_scope/sell.account https://api.ebay.com/oauth/api_scope/sell.fulfillment https://api.ebay.com/oauth/api_scope/commerce.identity.readonly',
		];

		$url = config('ebay.auth_endpoint').'/oauth2/authorize?'.http_build_query($url_query);

		$data = [
			'token' => $token,
			'expires_in' => $expires_in,
			'url' => $url,
			'username' => $username,
		];

		return view('ebay_oauth', compact('data'));
	}

	public static function contractStatus(){
		$class = config('admin.database.users_model');
		$userModel = new $class;
		$user = $userModel->where('id',Admin::user()->id)->first();

		$payment_history = DB::table('payment_history')->where('admin_users_id',Admin::user()->id)->where('result_status',1)->orderBy('created_at','DESC')->first();

		if($user->member_type == 1 && $payment_history){
			$created_at = $payment_history->created_at;
			$next = date('Y-m-d', strtotime($created_at.' +1 month'));
		} else {
			$next = '-';
		}

		$data = [
			'next' => $next,
		];
		return view('contract_status', compact('data'));
	}

	public static function ebayAuthnAuth(){
		$class = config('admin.database.users_model');
		$userModel = new $class;
		$user = $userModel->where('id',Admin::user()->id)->first();

		$token = $user->ebay_token;
		$expires_in = $user->refresh_token_expires_in;
		if(!$expires_in){
			$expires_in = "未ログイン";
		}

$xmlstr = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<GetSessionIDRequest xmlns="urn:ebay:apis:eBLBaseComponents">
  <RuName>%REPLACE_EBAY_RU_NAME%</RuName>
</GetSessionIDRequest>
XML;
		$xmlstr = str_replace("%REPLACE_EBAY_RU_NAME%", config('ebay.ru_name'), $xmlstr);
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, config('ebay.traditional.api'));
		curl_setopt($curl, CURLOPT_POST, true);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($curl, CURLOPT_HTTPHEADER, [
			'Content-Type: text/xml',
			'X-EBAY-API-CALL-NAME: GetSessionID',
			'X-EBAY-API-COMPATIBILITY-LEVEL: '.config('ebay.traditional.level'),
			'X-EBAY-API-DEV-NAME: '.config('ebay.dev_name'),
			'X-EBAY-API-APP-NAME: '.config('ebay.client_id'),
			'X-EBAY-API-CERT-NAME: '.config('ebay.client_secret'),
			'X-EBAY-API-SITEID: 0',
		]);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $xmlstr);

		$result = curl_exec($curl);
		curl_close($curl);

		//echo $result;
		$result_xml = simplexml_load_string($result);
		$xml = json_decode(json_encode($result_xml), true);
		$session_id = "";
		//var_dump($xml);
		if(isset($xml["SessionID"])){
			$session_id = $xml["SessionID"];
		}

		$class = config('admin.database.users_model');
		$userModel = new $class;
		$user = $userModel->where('id',Admin::user()->id)->first();
		$user->oauth_code = $session_id;
		$user->save();

		$url_query = [
			'SignIn' => '',
			'runame' => config('ebay.ru_name'),
			'SessID' => $session_id,
		];
		$url = config('ebay.traditional.auth').'?'.http_build_query($url_query);

		$data = [
			'token' => $token,
			'expires_in' => $expires_in,
			'url' => $url,
		];

		return view('ebay_oauth', compact('data'));
	}


}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client as GuzzleClient;
use App\Models\Categories;

class GetEBayCategories extends Command
{
	/**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:get-ebay-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh Ebay Categories';
	
	private static $order = 0;
	
	public function handle(): void
	{
		$admin = DB::table('admin_users')->where('id',1)->first();
		$access_token = $admin->access_token;
		
		$client = null;
        $client = new GuzzleClient();
		
		//// カテゴリーツリーIDに紐づく各種カテゴリーを取得
        // くっそ重いのでBatch等でデイリーでなんとかする
		$response = $client->request('GET',config('ebay.api_endpoint').'/commerce/taxonomy/v1/category_tree/0',[
            'http_errors' => false,
            'headers' => [
                'Authorization' => [
                    'Bearer '.$access_token,
                ],
                'Accept' => ['application/json'],
                'Content-Type' => ['application/json'],
                'Content-Language' => ['en-US'],
            ],
        ]);
        
        $response_body = $response->getBody()->getContents();
        $obj = json_decode($response_body,true); 
		
		if($response->getStatusCode() != 200){
            // 失敗？
			echo "failed!! ".$response->getStatusCode();
            return;
        }
		if(!isset($obj["rootCategoryNode"])){
            // 失敗？
			echo "failed!! ";
            return;
        }
		
		$tree_id = $obj["categoryTreeId"];
		$tree_version = $obj["categoryTreeVersion"];
		
		$tree_nodes = $obj["rootCategoryNode"]["childCategoryTreeNodes"];
		
		
		$this->saveCategory($tree_nodes,0,'',$tree_id,$tree_version);
		
	}
	
	private function saveCategory($tree_nodes,$parent_id,$parent_tree_name,$tree_id,$tree_version){
		foreach($tree_nodes as $node){
			$category_id = isset($node["category"]["categoryId"]) ? $node["category"]["categoryId"] : -1;
			$category_name = isset($node["category"]["categoryName"]) ? $node["category"]["categoryName"] : "";
			if($category_id == -1 || $category_name == ""){
				continue;
			}
			$level = isset($node["categoryTreeNodeLevel"]) ? $node["categoryTreeNodeLevel"] : 0;
			self::$order++;
			
			Categories::upsert([
				'id' => $category_id,
				'name' => $category_name,
				'parent_id' => $parent_id,
				'tree_node_level' => $level,
				'tree_id' => $tree_id,
				'tree_version' => $tree_version,
				'tree_order' => self::$order,
				'parent_tree_name' => $parent_tree_name,
			],['id']);
			
			
			$tree_name = $parent_tree_name."/".$category_name;
			
			$children = isset($node["childCategoryTreeNodes"]) ? $node["childCategoryTreeNodes"] : [];
			if($children){
				$this->saveCategory($children,$category_id,$tree_name,$tree_id,$tree_version);
			}
		}
	}
	
}
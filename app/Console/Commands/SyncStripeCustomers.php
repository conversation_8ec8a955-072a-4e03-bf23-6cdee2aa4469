<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Customer;

class SyncStripeCustomers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stripe:sync-customers {--email= : 特定のユーザーメールを指定して同期} {--create-missing : 見つからないユーザーを作成} {--customer-id= : 特定のStripe顧客IDを同期}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Stripe顧客情報とユーザーを同期します';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        $email = $this->option('email');
        $customerId = $this->option('customer-id');
        $createMissing = $this->option('create-missing');

        $this->info('Stripe顧客同期を開始します');

        if ($customerId) {
            $this->syncCustomer($customerId, $createMissing);
            return 0;
        }

        if ($email) {
            $this->syncUserByEmail($email);
            return 0;
        }

        // 全てのStripe顧客を取得
        $customers = Customer::all(['limit' => 100]);
        $this->info('Stripe顧客数: ' . count($customers->data));

        foreach ($customers->data as $customer) {
            $this->syncStripeCustomer($customer, $createMissing);
        }

        $this->info('同期が完了しました');

        return 0;
    }

    /**
     * 特定のStripe顧客IDを同期
     */
    protected function syncCustomer($customerId, $createMissing = false)
    {
        try {
            $customer = Customer::retrieve($customerId);
            $this->syncStripeCustomer($customer, $createMissing);
        } catch (\Exception $e) {
            $this->error('顧客の取得に失敗しました: ' . $e->getMessage());
            Log::error('Stripe customer retrieval failed', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 特定のメールアドレスを持つユーザーを同期
     */
    protected function syncUserByEmail($email)
    {
        $user = DB::table('admin_users')->where('username', $email)->first();

        if (!$user) {
            $this->error("ユーザーが見つかりません: {$email}");
            return;
        }

        $this->info("ユーザーを検索: ID {$user->id}, Email {$user->username}");

        if ($user->stripe_customer_id) {
            $this->info("既存のStripe顧客ID: {$user->stripe_customer_id}");

            try {
                $customer = Customer::retrieve($user->stripe_customer_id);
                $this->info("Stripeから顧客情報を確認: {$customer->email}");

                if ($customer->email != $user->username) {
                    if ($this->confirm("Stripeの顧客メール({$customer->email})とユーザーメール({$user->username})が一致しません。更新しますか？")) {
                        Customer::update($customer->id, [
                            'email' => $user->username
                        ]);
                        $this->info("Stripe顧客情報を更新しました");
                    }
                }

                $this->info("同期完了");
            } catch (\Exception $e) {
                $this->error("Stripe顧客の取得に失敗: {$e->getMessage()}");

                if ($this->confirm("新しいStripe顧客を作成しますか？")) {
                    $this->createStripeCustomer($user);
                }
            }
        } else {
            $this->warn("ユーザーにStripe顧客IDがありません");

            if ($this->confirm("新しいStripe顧客を作成しますか？")) {
                $this->createStripeCustomer($user);
            }
        }
    }

    /**
     * Stripe顧客情報とユーザーを同期
     */
    protected function syncStripeCustomer($customer, $createMissing = false)
    {
        $this->info("Stripe顧客: {$customer->id}, Email: {$customer->email}");

        if (!$customer->email) {
            $this->warn("顧客にメールアドレスがありません");
            return;
        }

        // メールアドレスでユーザーを検索
        $user = DB::table('admin_users')->where('username', $customer->email)->first();

        if (!$user) {
            // 大文字小文字を区別せずに検索
            $user = DB::table('admin_users')
                ->whereRaw('LOWER(username) = ?', [strtolower($customer->email)])
                ->first();

            if ($user) {
                $this->info("大文字小文字を区別せずにユーザーを発見: ID {$user->id}, Email {$user->username}");
            }
        }

        if ($user) {
            $this->info("ユーザーを発見: ID {$user->id}, Email {$user->username}");

            // すでに別のStripe顧客IDがある場合
            if ($user->stripe_customer_id && $user->stripe_customer_id != $customer->id) {
                $this->warn("ユーザーは別のStripe顧客ID({$user->stripe_customer_id})に関連付けられています");

                if ($this->confirm("Stripe顧客IDを更新しますか？ ({$user->stripe_customer_id} -> {$customer->id})")) {
                    DB::table('admin_users')
                        ->where('id', $user->id)
                        ->update([
                            'stripe_customer_id' => $customer->id,
                            'updated_at' => now()
                        ]);
                    $this->info("ユーザーのStripe顧客IDを更新しました");
                }
            }
            // Stripe顧客IDがない場合は更新
            elseif (!$user->stripe_customer_id) {
                DB::table('admin_users')
                    ->where('id', $user->id)
                    ->update([
                        'stripe_customer_id' => $customer->id,
                        'updated_at' => now()
                    ]);
                $this->info("ユーザーのStripe顧客IDを設定しました");
            } else {
                $this->info("ユーザーは既に正しいStripe顧客IDに関連付けられています");
            }
        } else {
            $this->warn("メールアドレス {$customer->email} に一致するユーザーが見つかりません");

            // Stripe顧客IDですでに関連付けられているユーザーを検索
            $userByCustomerId = DB::table('admin_users')
                ->where('stripe_customer_id', $customer->id)
                ->first();

            if ($userByCustomerId) {
                $this->info("Stripe顧客IDに関連付けられたユーザーを発見: ID {$userByCustomerId->id}, Email {$userByCustomerId->username}");

                if ($userByCustomerId->username != $customer->email) {
                    if ($this->confirm("ユーザーメール({$userByCustomerId->username})とStripe顧客メール({$customer->email})が一致しません。ユーザーメールを更新しますか？")) {
                        DB::table('admin_users')
                            ->where('id', $userByCustomerId->id)
                            ->update([
                                'username' => $customer->email,
                                'updated_at' => now()
                            ]);
                        $this->info("ユーザーメールを更新しました");
                    }
                }
            }
            // ユーザーが見つからず、自動作成が有効な場合
            elseif ($createMissing) {
                if ($this->confirm("ユーザーを新規作成しますか？ (Email: {$customer->email})")) {
                    try {
                        $userId = DB::table('admin_users')->insertGetId([
                            'username' => $customer->email,
                            'name' => $customer->name ?? explode('@', $customer->email)[0],
                            'password' => bcrypt(str_random(16)),
                            'stripe_customer_id' => $customer->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        $this->info("新しいユーザーを作成しました: ID {$userId}");
                    } catch (\Exception $e) {
                        $this->error("ユーザー作成に失敗しました: {$e->getMessage()}");
                    }
                }
            }
        }
    }

    /**
     * ユーザーに新しいStripe顧客を作成
     */
    protected function createStripeCustomer($user)
    {
        try {
            $customer = Customer::create([
                'email' => $user->username,
                'name' => $user->name ?? null,
                'metadata' => [
                    'user_id' => $user->id
                ]
            ]);

            DB::table('admin_users')
                ->where('id', $user->id)
                ->update([
                    'stripe_customer_id' => $customer->id,
                    'updated_at' => now()
                ]);

            $this->info("新しいStripe顧客を作成しました: {$customer->id}");
            return true;
        } catch (\Exception $e) {
            $this->error("Stripe顧客の作成に失敗しました: {$e->getMessage()}");
            return false;
        }
    }
}

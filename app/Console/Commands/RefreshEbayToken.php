<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Guz<PERSON>Http\Client as GuzzleClient;

class RefreshEbayToken extends Command
{
    protected $signature = 'command:refresh-ebay-token';
    protected $description = 'Refresh Ebay Token';

    public function handle(): void
    {
        Log::setDefaultDriver('batch');

        DB::table('admin_users')
            ->whereNotNull('refresh_token')
            ->where('member_type', '!=', 1)
            ->update([
                'oauth_code' => '',
                'access_token' => null,
                'access_token_expires_in' => null,
                'refresh_token' => null,
                'ebay_id' => null,
                'ebay_username' => null,
                'refresh_token_expires_in' => null,
                'updated_at' => now(),
            ]);

        $this->info("トークン削除完了：member_type != 1 のユーザー");
        Log::info("トークン削除完了：member_type != 1 のユーザー");

        $adminUsers = DB::table('admin_users')
            ->whereNotNull('refresh_token')
            ->where('permission_type', 1)
            ->get();

        $exclusion_users = [0];

        foreach ($adminUsers as $user) {
            $ebay_id = $user->ebay_id;
            $user_id = $user->id;

            $exclusionUser = DB::table('admin_users')
                ->whereNotNull('refresh_token')
                ->where('permission_type', 3)
                ->where('ebay_id', $ebay_id)
                ->first();

            if ($exclusionUser) {
                $exclusion_users[] = $exclusionUser->id;
            }

            $this->refreshAccessToken($user_id, $user->refresh_token);

            if ($exclusionUser) {
                $this->refreshAccessToken($exclusionUser->id, $user->refresh_token, "[Clone]");
            }
        }

        $users = DB::table('admin_users')
            ->whereNotNull('refresh_token')
            ->where('permission_type', 3)
            ->whereNotIn('id', $exclusion_users)
            ->get();

        foreach ($users as $user) {
            $this->refreshAccessToken($user->id, $user->refresh_token);
        }
    }

    private function refreshAccessToken(int $user_id, string $refresh_token, string $prefix = ''): void
    {
        try {
            $base64encoded = base64_encode(config('ebay.client_id') . ":" . config('ebay.client_secret'));
            $client = new GuzzleClient();

            $response = $client->request('POST', config('ebay.api_endpoint') . '/identity/v1/oauth2/token', [
                'headers' => [
                    'Authorization' => ['Basic ' . $base64encoded],
                    'Content-Type' => ['application/x-www-form-urlencoded'],
                ],
                'form_params' => [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $refresh_token,
                    'scope' => implode(' ', [
                        'https://api.ebay.com/oauth/api_scope',
                        'https://api.ebay.com/oauth/api_scope/sell.inventory',
                        'https://api.ebay.com/oauth/api_scope/sell.account',
                        'https://api.ebay.com/oauth/api_scope/sell.fulfillment',
                        'https://api.ebay.com/oauth/api_scope/commerce.identity.readonly',
                    ]),
                ],
                'http_errors' => false,
            ]);

            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            if ($statusCode !== 200) {
                $date = now();
                $message = "[{$date}] Error!! {$prefix} UserID: {$user_id} Status:{$statusCode} Body:{$body}";
                $this->error($message);
                Log::error($message);
                return;
            }

            $obj_response = json_decode($body);
            $expires_at = now()->addSeconds($obj_response->expires_in);

            $userModel = app(config('admin.database.users_model'));
            $user = $userModel->find($user_id);
            $user->access_token = $obj_response->access_token;
            $user->access_token_expires_in = $expires_at;
            $user->save();

            $date = now();
            $message = "[{$date}] {$prefix} UserID: {$user_id} AccessToken: {$obj_response->access_token} ExpiresIn: {$expires_at}";
            $this->info($message);
            Log::info($message);
        } catch (\Exception $e) {
            $date = now();
            $message = "[{$date}] Exception: {$prefix} UserID: {$user_id} Msg: {$e->getMessage()}";
            $this->error($message);
            Log::error($message);
        }
    }
}

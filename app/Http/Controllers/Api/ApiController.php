<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

use App\Utilities\HtmlDomUtility;
use Guz<PERSON>Http\Client as GuzzleClient;

use Exception;

use Illuminate\Support\Facades\Auth;
use Encore\Admin\Facades\Admin;
use Illuminate\Support\Facades\Log;

class ApiController extends Controller
{
	//

	public function fileUpload(Request $request)
	{
		//+++++++++++++++++++++++++++++++++++++++++++++++
		$result = false;
		$result = array(
			"status" => "ng",
			"url" => "",
			"path" => "",
			"filename" => "",
		);
		//+++++++++++++++++++++++++++++++++++++++++++++++
		if ($_SERVER['REQUEST_METHOD'] == 'POST') {
			$idx = $request->input('idx') ? $request->input('idx') : 0;
			if (empty($_FILES['upfile']['tmp_name']) || empty($_FILES['upfile']['name'])) {
			}
			list($origin_name, $file_type) = explode('.', $_FILES['upfile']['name']);
			$filename = bin2hex(openssl_random_pseudo_bytes(16)) . '_' . $origin_name . '.' . $file_type;
			sleep(1);

			$filename = "";
			if (array_key_exists("type", $_FILES['upfile'])) {
				if ($_FILES['upfile']['type'] == "image/gif") {
					$filename = time() . "_{$idx}.gif";
				}
				if ($_FILES['upfile']['type'] == "image/png") {
					$filename = time() . "_{$idx}.png";
				}
				if ($_FILES['upfile']['type'] == "image/jpeg") {
					$filename = time() . "_{$idx}.jpg";
				}
			}
			if ($filename != "") {
				$fileurl = ($request->getUriForPath('')) . "/uploads/images/" . $filename;
				$filepath = "/srv/app/public/uploads/images/" . $filename;

				$upload_success = move_uploaded_file($_FILES['upfile']['tmp_name'], $filepath);
				if ($upload_success) {
					$result = array(
						"status" => "ok",
						"url" => $fileurl,
						"path" => $filepath,
						"filename" => "images/" . $filename,
					);
				}
			}
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++
		//+++++++++++++++++++++++++++++++++++++++++++++++
		return $result;
	}

	public function moveProductImage(Request $request)
	{
		//+++++++++++++++++++++++++++++++++++++++++++++++
		$result = false;
		$url = $request["url"];
		$path = $request["path"];
		$result = array(
			"status" => "ng",
			"url" => $url,
			"path" => $path,
		);
		//+++++++++++++++++++++++++++++++++++++++++++++++
		$rename_url = str_replace('/tmp/', '/images/', $url);
		$rename_path = str_replace('/tmp/', '/images/', $path);
		if (rename($path, $rename_path)) {
			$result = array(
				"status" => "ok",
				"url" => $rename_url,
				"path" => $rename_path,
			);
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++
		return $result;
	}

	public function getProductInfo(Request $request)
	{
		$mode = $request->input('mode');
		$url = $request->input('url');
		$product = array();
		$result = array(
			"status" => "ng",
			"mode" => $mode,
			"url" => $url,
			"product" => $product,
		);
		//+++++++++++++++++++++++++++++++++++++++++++++++
		//eBay
		if ($mode == "ebay") {
			$url = "https://www.ebay.com/itm/" . $url;
			$html = HtmlDomUtility::getHtml($url);
			$dom = HtmlDomUtility::getHtmlToDom($html);
			$product_title = "";
			$obj_product_title_doms = $dom->find('head title');
			foreach ($obj_product_title_doms as $sub_dom) {
				$product["product_title"] = $sub_dom->text();
				break;
			}
			$product_comment = "";
			$obj_product_comment_doms = $dom->find('iframe#desc_ifr');
			if (count($obj_product_comment_doms) > 0) {
				$comment_url = $obj_product_comment_doms[0]->getAttribute('src');
				if ($comment_url) {
					$comment_html = HtmlDomUtility::getHtml($comment_url);
					$comment_dom = HtmlDomUtility::getHtmlToDom($comment_html);

					$dom2 = $comment_dom->find('#ds_div');
					if (count($dom2) > 0) {
						$product_comment = $dom2[0]->innertext();
					}
					$comment_dom->clear();
					$product["product_comment"] = $product_comment;
				}
			}
			$product_price = "";
			$product["product_price"] = $product_price;

			$debug = [];

			$arr_product_image = array();
			$image_count = 0;
			$obj_product_image_doms = $dom->find('div.ux-image-carousel-item.image-treatment.image > img');
			//$debug = array();
			foreach ($obj_product_image_doms as $dom) {
				$img_src = isset($dom->attr["src"]) ? $dom->attr["src"] : false;
				if (!$img_src) {
					if (isset($dom->attr["data-src"])) {
						$img_src = $dom->attr["data-src"];
					} else {
						continue;
					}
				}
				$ch = curl_init();
				//ここからオプション
				curl_setopt($ch, CURLOPT_URL, $img_src);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  //証明書検証しない
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  //レスポンスを表示するか
				curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);  //"Location: " ヘッダの内容をたどる
				curl_setopt($ch, CURLOPT_MAXREDIRS, 10);  //"Location: " ヘッダの内容をたどる深さ
				curl_setopt($ch, CURLOPT_HTTPHEADER, ['User-Agent: AppleWebKit/10000']);
				//ここまでオプション
				$output = curl_exec($ch);   //cURL セッションを実行する
				$info = curl_getinfo($ch);
				$errno = curl_errno($ch);
				$error = curl_error($ch);
				curl_close($ch);
				if (CURLE_OK !== $errno) {
					//error
				} else {
					$scheme = 'data:application/octet-stream;base64,';
					$image_size = getimagesize($scheme . base64_encode($output));
					$filename = "";
					if (array_key_exists("mime", $image_size)) {
						if ($image_size["mime"] == "image/gif") {
							$filename = time() . "_" . $image_count . ".gif";
							$image_count += 1;
						}
						if ($image_size["mime"] == "image/png") {
							$filename = time() . "_" . $image_count . ".png";
							$image_count += 1;
						}
						if ($image_size["mime"] == "image/jpeg") {
							$filename = time() . "_" . $image_count . ".jpg";
							$image_count += 1;
						}
					}
					if ($filename != "") {
						$fileurl = ($request->getUriForPath('')) . "/uploads/tmp/" . $filename;
						$filepath = "/srv/app/public/uploads/tmp/" . $filename;
						$fp = fopen($filepath, 'x');
						fwrite($fp, $output);
						fclose($fp);
						$arr_product_image[] = array(
							"url" => $fileurl,
							"path" => $filepath,
						);
					}
				}
				//++++++++++++++++++++++++++++
				$product["product_image"] = $arr_product_image;
				//++++++++++++++++++++++++++++
			}
			$dom->clear();

			$result = array(
				"status" => "ok",
				"mode" => $mode,
				"url" => $url,
				"product" => $product,
				"debug" => $debug,
			);
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++
		//YahooAuctionsDetail
		if ($mode == "yahoo") {
			Log::debug('デバッグ用のログです');
			//-----------------------------------------------
			$url = "https://page.auctions.yahoo.co.jp/jp/auction/" . $url;
			$html = HtmlDomUtility::getHtml($url);
			$dom = HtmlDomUtility::getHtmlToDom($html);
			Log::debug($dom);
			//-----------------------------------------------
			Log::debug('デバッグ用のログです1');
			$product_title = "";
			// $obj_product_title_doms = $dom->find('.ProductTitle__title');
			$obj_product_title_doms = $dom->find('.styles_name__u228e');
			Log::debug($obj_product_title_doms);
			foreach ($obj_product_title_doms as $sub_dom) {
				$sub_dom2 = $sub_dom->find('.ProductTitle__text');
				foreach ($sub_dom2 as $sub_dom3) {
					$product_title = $sub_dom3->text();
					//++++++++++++++++++++++++++++
					$product["product_title"] = $product_title;
					//++++++++++++++++++++++++++++
				}
			}
			//-----------------------------------------------
			Log::debug('デバッグ用のログです2');
			$product_comment = "";
			$obj_product_comment_doms = $dom->find('.ProductExplanation');
			Log::debug($obj_product_comment_doms);
			foreach ($obj_product_comment_doms as $sub_dom) {
				$sub_dom2 = $sub_dom->find('.ProductExplanation__body');
				foreach ($sub_dom2 as $sub_dom3) {
					$sub_dom4 = $sub_dom3->find('.ProductExplanation__commentBody');
					foreach ($sub_dom4 as $sub_dom5) {
						$product_comment = $sub_dom5->innertext;
						//++++++++++++++++++++++++++++
						$product["product_comment"] = $product_comment;
						//++++++++++++++++++++++++++++
					}
				}
			}
			//-----------------------------------------------
			Log::debug('デバッグ用のログです3');
			$product_price = "";
			$obj_product_price_doms = $dom->find('.Price__value');
			Log::debug($obj_product_price_doms);
			foreach ($obj_product_price_doms as $sub_dom) {
				$sub_dom2 = $sub_dom->find('text');
				if (count($sub_dom2) > 0) {
					$product_price = $sub_dom2[0];
					$product["product_price"] = preg_replace('/[^0-9]/', '', trim($product_price->plaintext));
				}
			}
			//-----------------------------------------------
			Log::debug('デバッグ用のログです4');
			$arr_product_image = array();
			$image_count = 0;
			// $obj_product_image_doms = $dom->find('.ProductImage__images');
			$obj_product_image_doms = $dom->find('.styles_thumbnailItems__DGa51');
			Log::debug($obj_product_image_doms);
			foreach ($obj_product_image_doms as $sub_dom) {
				Log::debug($sub_dom);
				$sub_dom2 = $sub_dom->find('.ProductImage__image');
				foreach ($sub_dom2 as $sub_dom3) {
					$sub_dom4 = $sub_dom3->find('img');
					foreach ($sub_dom4 as $sub_dom5) {
						$product_image_url = $sub_dom5->src;
						//++++++++++++++++++++++++++++
						$ch = curl_init();
						//ここからオプション
						curl_setopt($ch, CURLOPT_URL, $product_image_url);
						curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  //証明書検証しない
						curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  //レスポンスを表示するか
						curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);  //"Location: " ヘッダの内容をたどる
						curl_setopt($ch, CURLOPT_MAXREDIRS, 10);  //"Location: " ヘッダの内容をたどる深さ
						curl_setopt($ch, CURLOPT_HTTPHEADER, ['User-Agent: AppleWebKit/10000']);
						//ここまでオプション
						$output = curl_exec($ch);   //cURL セッションを実行する
						$info = curl_getinfo($ch);

						$errno = curl_errno($ch);
						$error = curl_error($ch);
						curl_close($ch);
						if (CURLE_OK !== $errno) {
							//error
						} else {
							$scheme = 'data:application/octet-stream;base64,';
							$image_size = getimagesize($scheme . base64_encode($output));
							$filename = "";
							if (array_key_exists("mime", $image_size)) {
								if ($image_size["mime"] == "image/gif") {
									$filename = time() . "_" . $image_count . ".gif";
									$image_count += 1;
								}
								if ($image_size["mime"] == "image/png") {
									$filename = time() . "_" . $image_count . ".png";
									$image_count += 1;
								}
								if ($image_size["mime"] == "image/jpeg") {
									$filename = time() . "_" . $image_count . ".jpg";
									$image_count += 1;
								}
							}
							if ($filename != "") {
								$fileurl = ($request->getUriForPath('')) . "/uploads/tmp/" . $filename;
								$filepath = "/srv/app/public/uploads/tmp/" . $filename;
								$fp = fopen($filepath, 'x');
								fwrite($fp, $output);
								fclose($fp);
								$arr_product_image[] = array(
									"url" => $fileurl,
									"path" => $filepath,
								);
							}
						}
						//++++++++++++++++++++++++++++
						$product["product_image"] = $arr_product_image;
						//++++++++++++++++++++++++++++
					}
				}
			}
			//-----------------------------------------------
			Log::debug('デバッグ用のログです5');
			$dom->clear();
			//-----------------------------------------------
			$result = array(
				"status" => "ok",
				"mode" => $mode,
				"url" => $url,
				"product" => $product,
			);
			Log::debug($result);
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++
		//mercariDetail
		if ($mode == "mercari") {
			//-----------------------------------------------
			$ret = [
				'product_title' => '',
				'product_comment' => '',
				'images' => [],
			];
			$url = 'https://jp.mercari.com/item/' . $url;
			//$output = shell_exec('sudo -u ec2-user /home/<USER>/.nvm/versions/node/v14.17.3/bin/node /srv/app/nodejs/get_mercari_product.js '.$url);
			$output = shell_exec('cd /srv/app && node nodejs/get_mercari_product.js ' . escapeshellarg($url) . ' 2>&1');
			//$output = shell_exec('pwd');

			$product["output"] = $output;
			$ret = json_decode($output, true);

			$product["product_title"] = isset($ret["product_title"]) ? $ret["product_title"] : '';
			$product["product_comment"] = isset($ret["product_comment"]) ? $ret["product_comment"] : '';
			$product["product_price"] = isset($ret["price"]) ? str_replace(',', '', $ret["price"]) : '';

			$arr_product_image = array();
			$image_count = 0;
			if (isset($ret["images"])) {
				foreach ($ret["images"] as $image) {
					$ch = curl_init();
					//ここからオプション
					curl_setopt($ch, CURLOPT_URL, $image);
					curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  //証明書検証しない
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  //レスポンスを表示するか
					curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);  //"Location: " ヘッダの内容をたどる
					curl_setopt($ch, CURLOPT_MAXREDIRS, 10);  //"Location: " ヘッダの内容をたどる深さ
					curl_setopt($ch, CURLOPT_HTTPHEADER, ['User-Agent: AppleWebKit/10000']);
					//ここまでオプション
					$output = curl_exec($ch);   //cURL セッションを実行する
					$info = curl_getinfo($ch);

					$errno = curl_errno($ch);
					$error = curl_error($ch);
					curl_close($ch);
					if (CURLE_OK !== $errno) {
						//error
					} else {
						$scheme = 'data:application/octet-stream;base64,';
						$image_size = getimagesize($scheme . base64_encode($output));
						$filename = "";
						if (array_key_exists("mime", $image_size)) {
							if ($image_size["mime"] == "image/gif") {
								$filename = time() . "_" . $image_count . ".gif";
								$image_count += 1;
							}
							if ($image_size["mime"] == "image/png") {
								$filename = time() . "_" . $image_count . ".png";
								$image_count += 1;
							}
							if ($image_size["mime"] == "image/jpeg") {
								$filename = time() . "_" . $image_count . ".jpg";
								$image_count += 1;
							}
						}
						if ($filename != "") {
							$fileurl = ($request->getUriForPath('')) . "/uploads/tmp/" . $filename;
							$filepath = "/srv/app/public/uploads/tmp/" . $filename;
							$fp = fopen($filepath, 'x');
							fwrite($fp, $output);
							fclose($fp);
							$arr_product_image[] = array(
								"url" => $fileurl,
								"path" => $filepath,
							);
						}
					}
					//++++++++++++++++++++++++++++
					$product["product_image"] = $arr_product_image;
				}
			}

			//-----------------------------------------------
			$result = array(
				"status" => "ok",
				"mode" => $mode,
				"url" => $url,
				"product" => $product,
				"html" => '',
			);
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++
		//AmazonDetail
		if ($mode == "amazon") {
			//-----------------------------------------------
			$ret = [
				'product_title' => '',
				'product_comment' => '',
				'images' => [],
			];
			$url = 'https://www.amazon.co.jp/dp/' . $url;
			//$output = shell_exec('sudo -u ec2-user /home/<USER>/.nvm/versions/node/v14.17.3/bin/node /srv/app/nodejs/get_mercari_product.js '.$url);
			$output = shell_exec('cd /srv/app && node nodejs/get_amazon_product.js ' . escapeshellarg($url) . ' 2>&1');
			//$output = shell_exec('pwd');

			$product["output"] = $output;
			$ret = json_decode($output, true);

			$product["product_title"] = isset($ret["product_title"]) ? $ret["product_title"] : '';
			$product["product_comment"] = isset($ret["product_comment"]) ? $ret["product_comment"] : '';
			$product["product_price"] = isset($ret["price"]) ? str_replace(',', '', $ret["price"]) : '';

			$arr_product_image = array();
			$image_count = 0;
			if (isset($ret["images"])) {
				foreach ($ret["images"] as $image) {
					$ch = curl_init();
					//ここからオプション
					curl_setopt($ch, CURLOPT_URL, $image);
					curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  //証明書検証しない
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  //レスポンスを表示するか
					curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);  //"Location: " ヘッダの内容をたどる
					curl_setopt($ch, CURLOPT_MAXREDIRS, 10);  //"Location: " ヘッダの内容をたどる深さ
					curl_setopt($ch, CURLOPT_HTTPHEADER, ['User-Agent: AppleWebKit/10000']);
					//ここまでオプション
					$output = curl_exec($ch);   //cURL セッションを実行する
					$info = curl_getinfo($ch);

					$errno = curl_errno($ch);
					$error = curl_error($ch);
					curl_close($ch);
					if (CURLE_OK !== $errno) {
						//error
					} else {
						$scheme = 'data:application/octet-stream;base64,';
						$image_size = getimagesize($scheme . base64_encode($output));
						$filename = "";
						if (array_key_exists("mime", $image_size)) {
							if ($image_size["mime"] == "image/gif") {
								$filename = time() . "_" . $image_count . ".gif";
								$image_count += 1;
							}
							if ($image_size["mime"] == "image/png") {
								$filename = time() . "_" . $image_count . ".png";
								$image_count += 1;
							}
							if ($image_size["mime"] == "image/jpeg") {
								$filename = time() . "_" . $image_count . ".jpg";
								$image_count += 1;
							}
						}
						if ($filename != "") {
							$fileurl = ($request->getUriForPath('')) . "/uploads/tmp/" . $filename;
							$filepath = "/srv/app/public/uploads/tmp/" . $filename;
							$fp = fopen($filepath, 'x');
							fwrite($fp, $output);
							fclose($fp);
							$arr_product_image[] = array(
								"url" => $fileurl,
								"path" => $filepath,
							);
						}
					}
					//++++++++++++++++++++++++++++
					$product["product_image"] = $arr_product_image;
				}
			}

			//-----------------------------------------------
			$result = array(
				"status" => "ok",
				"mode" => $mode,
				"url" => $url,
				"product" => $product,
				"html" => '',
			);
		}

		if ($mode == 'jancode') {
			$client = null;
			$client = new GuzzleClient();

			$jancode = $url;
			// 一覧取得
			$response = $client->request('GET', 'https://shopping.yahooapis.jp/ShoppingWebService/V3/itemSearch', [
				'http_errors' => false,
				'headers' => [
					'Accept' => ['application/json'],
					'Content-Type' => ['application/json'],
				],
				'query' => [
					'appid' => 'dj00aiZpPTgzS2dqck1CZXFkQyZzPWNvbnN1bWVyc2VjcmV0Jng9NmE-',
					'jan_code' => $jancode,
					'sort' => '-score',
					'image_size' => 600,
				],
			]);

			$response_body = $response->getBody()->getContents();
			$obj = json_decode($response_body, true);
			if ($response->getStatusCode() != 200) {
				$result = array(
					"status" => "ng",
					"mode" => $mode,
					"url" => $url,
					"product" => [1],
				);
				return $result;
			}
			if (isset($obj["totalResultsAvailable"]) && $obj["totalResultsAvailable"] != 0) {
			} else {
				$result = array(
					"status" => "ng",
					"mode" => $mode,
					"url" => $url,
					"product" => [2],
				);
				return $result;
			}
			if (isset($obj["hits"]) && count($obj["hits"]) > 0) {
			} else {
				$result = array(
					"status" => "ng",
					"mode" => $mode,
					"url" => $url,
					"product" => [3],
				);
				return $result;
			}
			$item_code = $obj["hits"][0]["code"];

			error_log("item_code: " . $item_code);

			// 商品詳細取得
			$response = $client->request('GET', 'https://shopping.yahooapis.jp/ShoppingWebService/V1/json/itemLookup', [
				'http_errors' => false,
				'headers' => [
					'Accept' => ['application/json'],
					'Content-Type' => ['application/json'],
				],
				'query' => [
					'appid' => 'dj00aiZpPTgzS2dqck1CZXFkQyZzPWNvbnN1bWVyc2VjcmV0Jng9NmE-',
					'itemcode' => $item_code,
					'responsegroup' => 'large',
					'image_size' => 600,
				],
			]);

			$response_body = $response->getBody()->getContents();
			$obj = json_decode($response_body, true);

			if ($response->getStatusCode() != 200) {
				$result = array(
					"status" => "ng",
					"mode" => $mode,
					"url" => $url,
					"product" => [4],
				);
				return $result;
			}
			if (isset($obj["ResultSet"]) && isset($obj["ResultSet"][0]["Result"]) && isset($obj["ResultSet"][0]["Result"][0])) {
				//
				$data = $obj["ResultSet"][0]["Result"][0];// 商品ページURLを取得 


				$url = $data["Url"];
				error_log("Yahoo Shopping URL: " . $url);

				$output = shell_exec('cd /srv/app && node nodejs/get_yahoo_product.js ' . escapeshellarg($url) . ' 2>&1');
				$ret = json_decode($output, true);
				error_log("get_yahoo_product.js output: " . $output);
				
				
				$product["product_title"] = $data["Name"];
				$product["product_price"] = $data["Price"]["_value"];
				$product["product_comment"] = $data["Description"];

				$arr_product_image = array();
				$image_count = 0;

				$data_images = array();
				// $data_images[] = $data["Image"];
				$data_images[] = $data["ExImage"];

				// if (isset($data["RelatedImages"])) {
				// 	foreach ($data["RelatedImages"] as $key => $row) {
				// 		if ($key == "_container") continue;
				// 		$data_images[] = $row;
				// 	}
				// }

				foreach ($data_images as $row) {
					// $product_image_url = "";
					// if ($product_image_url == "" && isset($row["Small"]) && $row["Small"] != "") {
					// 	$product_image_url = $row["Small"];
					// }
					// if (isset($row["Medium"]) && $row["Medium"] != "") {
					// 	$product_image_url = $row["Medium"];
					// }
					// if ($product_image_url == "") continue;
					$product_image_url = $row["Url"];

					$ch = curl_init();
					//ここからオプション
					curl_setopt($ch, CURLOPT_URL, $product_image_url);
					curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  //証明書検証しない
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  //レスポンスを表示するか
					curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);  //"Location: " ヘッダの内容をたどる
					curl_setopt($ch, CURLOPT_MAXREDIRS, 10);  //"Location: " ヘッダの内容をたどる深さ
					curl_setopt($ch, CURLOPT_HTTPHEADER, ['User-Agent: AppleWebKit/10000']);

					//ここまでオプション
					$output = curl_exec($ch);   //cURL セッションを実行する
					$info = curl_getinfo($ch);
					$errno = curl_errno($ch);
					$error = curl_error($ch);
					curl_close($ch);
					if (CURLE_OK !== $errno) {
						//error
					} else {
						$scheme = 'data:application/octet-stream;base64,';
						$image_size = getimagesize($scheme . base64_encode($output));
						$filename = "";
						if (array_key_exists("mime", $image_size)) {
							if ($image_size["mime"] == "image/gif") {
								$filename = time() . "_" . $image_count . ".gif";
								$image_count += 1;
							}
							if ($image_size["mime"] == "image/png") {
								$filename = time() . "_" . $image_count . ".png";
								$image_count += 1;
							}
							if ($image_size["mime"] == "image/jpeg") {
								$filename = time() . "_" . $image_count . ".jpg";
								$image_count += 1;
							}
						}
						if ($filename != "") {
							$fileurl = ($request->getUriForPath('')) . "/uploads/tmp/" . $filename;
							$filepath = "/srv/app/public/uploads/tmp/" . $filename;
							$fp = fopen($filepath, 'x');
							fwrite($fp, $output);
							fclose($fp);
							$arr_product_image[] = array(
								"url" => $fileurl,
								"path" => $filepath,
								'origin' => $product_image_url,
							);
						}
					}
				}
				$product["product_image"] = $arr_product_image;

				$url = $data["Url"];
				$result = array(
					"status" => "ok",
					"mode" => $mode,
					"url" => $url,
					"product" => $product,
				);
			} else {
				$result = array(
					"status" => "ng",
					"mode" => $mode,
					"url" => $url,
					"product" => [5],
				);
				return $result;
			}
		}

		if ($mode == 'jancode_amazon') {

			//ここから
			// https://www.amazon.co.jp/s?k=7340055401104
			// https://www.amazon.co.jp/s?k=XXXXXX にアクセス
			// その最初の製品のURLを取得→それを$mode == 'amazon'に設定する


			//-----------------------------------------------
			// $ret = [
			// 	'product_title' => '',
			// 	'product_comment' => '',
			// 	'images' => [],
			// ];
			// $url = 'https://www.amazon.co.jp/s?k=' . $url;
			// //$output = shell_exec('sudo -u ec2-user /home/<USER>/.nvm/versions/node/v14.17.3/bin/node /srv/app/nodejs/get_mercari_product.js '.$url);
			// $output = shell_exec('sudo -u ec2-user /home/<USER>/.nvm/versions/node/v14.17.3/bin/node /srv/app/nodejs/get_amazon_product_jancode.js ' . $url);
			// //$output = shell_exec('pwd');

			// $product["output"] = $output;
			// $res = json_decode($output, true);
			// $product["res"] = $res;
			// $product["href"] = $res["href"][0];

			// $ret = [
			// 	'product_title' => '',
			// 	'product_comment' => '',
			// 	'images' => [],
			// ];
			// $url = 'https://www.amazon.co.jp' . $res["href"][0];
			//ここまでをコメントアウト。JANコードをASINに変更のため不要

			// https://www.amazon.co.jp/dp/B0BCJCNX84 のようなURLにアクセス
			$url = 'https://www.amazon.co.jp/dp/' . $url;
			//$output = shell_exec('sudo -u ec2-user /home/<USER>/.nvm/versions/node/v14.17.3/bin/node /srv/app/nodejs/get_mercari_product.js '.$url);
			$output = shell_exec('cd /srv/app && node nodejs/get_amazon_product.js ' . escapeshellarg($url) . ' 2>&1');
			//$output = shell_exec('pwd');

			$product["output"] = $output;
			$ret = json_decode($output, true);

			$product["product_title"] = isset($ret["product_title"]) ? $ret["product_title"] : '';
			$product["product_comment"] = isset($ret["product_comment"]) ? $ret["product_comment"] : '';
			$product["product_price"] = isset($ret["price"]) ? str_replace(',', '', $ret["price"]) : '';

			$arr_product_image = array();
			$image_count = 0;
			if (isset($ret["images"])) {
				foreach ($ret["images"] as $image) {
					$ch = curl_init();
					//ここからオプション
					curl_setopt($ch, CURLOPT_URL, $image);
					curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  //証明書検証しない
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  //レスポンスを表示するか
					curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);  //"Location: " ヘッダの内容をたどる
					curl_setopt($ch, CURLOPT_MAXREDIRS, 10);  //"Location: " ヘッダの内容をたどる深さ
					curl_setopt($ch, CURLOPT_HTTPHEADER, ['User-Agent: AppleWebKit/10000']);
					//ここまでオプション
					$output = curl_exec($ch);   //cURL セッションを実行する
					$info = curl_getinfo($ch);

					$errno = curl_errno($ch);
					$error = curl_error($ch);
					curl_close($ch);
					if (CURLE_OK !== $errno) {
						//error
					} else {
						$scheme = 'data:application/octet-stream;base64,';
						$image_size = getimagesize($scheme . base64_encode($output));
						$filename = "";
						if (array_key_exists("mime", $image_size)) {
							if ($image_size["mime"] == "image/gif") {
								$filename = time() . "_" . $image_count . ".gif";
								$image_count += 1;
							}
							if ($image_size["mime"] == "image/png") {
								$filename = time() . "_" . $image_count . ".png";
								$image_count += 1;
							}
							if ($image_size["mime"] == "image/jpeg") {
								$filename = time() . "_" . $image_count . ".jpg";
								$image_count += 1;
							}
						}
						if ($filename != "") {
							$fileurl = ($request->getUriForPath('')) . "/uploads/tmp/" . $filename;
							$filepath = "/srv/app/public/uploads/tmp/" . $filename;
							$fp = fopen($filepath, 'x');
							fwrite($fp, $output);
							fclose($fp);
							$arr_product_image[] = array(
								"url" => $fileurl,
								"path" => $filepath,
							);
						}
					}
					//++++++++++++++++++++++++++++
					$product["product_image"] = $arr_product_image;
				}
			}

			//-----------------------------------------------
			$result = array(
				"status" => "ok",
				"mode" => $mode,
				"url" => $url,
				"product" => $product,
				"html" => '',
			);
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++
		return $result;
	}


	public function getTranslation(Request $request)
	{
		$text = $request->input('text');
		$ret = [
			'text' => '',
			'status' => 'error',
		];
		if (!$text) {
			return response()->json($ret);
		}

		$client = null;
		$client = new GuzzleClient();

		$response = $client->request('POST', 'https://api-free.deepl.com/v2/translate', [
			'http_errors' => false,
			'headers' => [
				'Authorization' => [
					'DeepL-Auth-Key 8cab8d2d-2d06-d118-9db3-974bf25ce014:fx',
				],
				'Accept' => ['application/json'],
				'Content-Type' => ['application/json'],
			],
			'json' => [
				'text' => [$text],
				"target_lang" => "EN",
			],
		]);

		$response_body = $response->getBody()->getContents();
		$obj = json_decode($response_body, true);
		if ($response->getStatusCode() != 200) {
			$ret["status"] = 'error1';
			return response()->json($ret);
		}
		if (!isset($obj["translations"])) {
			$ret["status"] = 'error2';
			return response()->json($ret);
		}
		if (!isset($obj["translations"][0]["text"])) {
			$ret["status"] = 'error3';
			return response()->json($ret);
		}
		$ret["status"] = 'success';
		$ret["text"] = $obj["translations"][0]["text"];
		return response()->json($ret);
	}

	public function getMercariProduct(Request $request)
	{
		$id = $request->input('id');
		$ret = [
			'title' => '',
			'description' => '',
			'images' => [],
		];

		$output = shell_exec('cd /srv/app && node nodejs/get_mercari_product.js ' . escapeshellarg($id) . ' 2>&1');

		return response()->json(json_decode($output));
	}

	public function getDescriptionTemplateContent(Request $request, $id)
	{
		$template = \App\Models\DescriptionTemplate::where('id', $id)->first();
		$ret = false;
		$content = '';
		if ($template) {
			$ret = true;
			$content = $template->description;
		}
		return response()->json([
			'result' => $ret,
			'content' => $content,
		]);
	}

	public function getEBayDescription(Request $request)
	{
		$id = $request->input('id');
		if (filter_var($id, FILTER_VALIDATE_URL)) {
			// URLっぽい
			$url = $id;
		} else {
			// URLでは無いっぽい
			$url = "https://www.ebay.com/itm/" . $id;
		}

		$ret = false;
		$content = "";

		if ($id) {
			$html = HtmlDomUtility::getHtml($url);
			$dom = HtmlDomUtility::getHtmlToDom($html);

			$product_comment = "";
			$obj_product_comment_doms = $dom->find('iframe#desc_ifr');
			if (count($obj_product_comment_doms) > 0) {
				$comment_url = $obj_product_comment_doms[0]->getAttribute('src');
				if ($comment_url) {
					$comment_html = HtmlDomUtility::getHtml($comment_url);
					$comment_dom = HtmlDomUtility::getHtmlToDom($comment_html);

					$dom2 = $comment_dom->find('#ds_div');
					if (count($dom2) > 0) {
						$product_comment = $dom2[0]->innertext();
					}
					$comment_dom->clear();
					$content = $product_comment;
				}
			}

			$dom->clear();
			$ret = true;
		}


		return response()->json([
			'result' => $ret,
			'content' => $content,
			'id' => $id
		]);
	}

	public function getEBayCategories(Request $request)
	{
		$categories = DB::table('categories')->orderBy('tree_order')->get();
		if ($categories) {
			$data = [];
			foreach ($categories as $category) {

				$name = $category->name;
				$data[] = [
					'id' => $category->id,
					'name' => $name,
					'parent' => $category->parent_tree_name,
				];
			}
			return response()->json([
				'result' => 'success',
				'data' => $data,
			]);
		} else {
			return response()->json([
				'result' => 'failed',
			]);
		}
	}

	/**
	 * SKUを生成する
	 *
	 * @param Request $request
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function generateSKU(Request $request)
	{
		$title = $request->input('title');
		$mode = $request->input('mode');
		$url = $request->input('url');
		
		try {
			$exhibitController = new \App\Admin\Controllers\ExhibitController();
			$productInfo = [
				'title' => $title,
				'mode' => $mode,
				'url' => $url
			];
			
			$generatedSKU = $exhibitController->generateProductSKU($productInfo);
			
			return response()->json([
				'status' => 'ok',
				'sku' => $generatedSKU
			]);
		} catch (\Exception $e) {
			return response()->json([
				'status' => 'error',
				'message' => $e->getMessage()
			], 500);
		}
	}

	public function getYahooShoppingProduct(Request $request)
	{
		$id = $request->input('id');
		Log::info('Yahoo Shopping product fetch started', ['item_code' => $id]);

		if (empty($id)) {
			Log::warning('Yahoo Shopping product fetch failed: No item_code provided');
			return response()->json([
				'status' => 'error',
				'message' => 'item_codeが指定されていません。'
			]);
		}

		try {
			// Yahoo APIでURL取得
			$client = new \GuzzleHttp\Client();
			$appid = config('services.yahoo.appid');
			Log::info('Fetching Yahoo Shopping API', ['item_code' => $id, 'appid' => $appid]);

			$res = $client->request('GET', 'https://shopping.yahooapis.jp/ShoppingWebService/V3/itemSearch', [
				'query' => [
					'appid' => $appid,
					'itemcode' => $id,
				]
			]);

			$body = json_decode($res->getBody(), true);
			Log::debug('Yahoo Shopping API response', ['response' => $body]);

			if (empty($body['hits'][0]['url'])) {
				Log::warning('Yahoo Shopping product fetch failed: No product URL found', ['item_code' => $id]);
				return response()->json([
					'status' => 'error',
					'message' => '商品URLが取得できませんでした。'
				]);
			}

			$url = $body['hits'][0]['url'];
			Log::info('Running Node.js script for Yahoo Shopping', ['url' => $url]);
			
			$output = shell_exec('node ' . base_path('nodejs/get_yahoo_product.js') . ' ' . escapeshellarg($url));
			$ret = json_decode($output, true);
			
			Log::info('Yahoo Shopping product fetch completed', [
				'item_code' => $id,
				'title' => $ret['product_title'] ?? 'not found',
				'has_description' => !empty($ret['product_comment']),
				'has_price' => !empty($ret['price']),
				'image_count' => count($ret['images'] ?? []),
				'state' => $ret['state'] ?? 0
			]);

			return response()->json([
				'title' => $ret['product_title'] ?? '',
				'description' => $ret['product_comment'] ?? '',
				'price' => $ret['price'] ?? '',
				'images' => $ret['images'] ?? [],
				'state' => $ret['state'] ?? 0
			]);

		} catch (\Exception $e) {
			Log::error('Yahoo Shopping product fetch error', [
				'item_code' => $id,
				'error' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			
			return response()->json([
				'status' => 'error',
				'message' => '商品情報の取得に失敗しました。'
			]);
		}
	}

	public function getYahooAuctionProduct(Request $request)
	{
		$id = $request->input('id');
		Log::info('Yahoo Auction product fetch started', ['auction_id' => $id]);

		if (empty($id)) {
			Log::warning('Yahoo Auction product fetch failed: No auction_id provided');
			return response()->json([
				'status' => 'error',
				'message' => 'auction_idが指定されていません。'
			]);
		}

		try {
			$url = 'https://page.auctions.yahoo.co.jp/jp/auction/' . $id;
			Log::info('Running Node.js script for Yahoo Auction', ['url' => $url]);
			
			$output = shell_exec('node ' . base_path('nodejs/get_yahoo_auction_product.js') . ' ' . escapeshellarg($url));
			$ret = json_decode($output, true);

			Log::info('Yahoo Auction product fetch completed', [
				'auction_id' => $id,
				'title' => $ret['product_title'] ?? 'not found',
				'has_description' => !empty($ret['product_comment']),
				'has_price' => !empty($ret['price']),
				'image_count' => count($ret['images'] ?? []),
				'state' => $ret['state'] ?? 0
			]);

			return response()->json([
				'title' => $ret['product_title'] ?? '',
				'description' => $ret['product_comment'] ?? '',
				'price' => $ret['price'] ?? '',
				'images' => $ret['images'] ?? [],
				'state' => $ret['state'] ?? 0
			]);

		} catch (\Exception $e) {
			Log::error('Yahoo Auction product fetch error', [
				'auction_id' => $id,
				'error' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			
			return response()->json([
				'status' => 'error',
				'message' => '商品情報の取得に失敗しました。'
			]);
		}
	}
}

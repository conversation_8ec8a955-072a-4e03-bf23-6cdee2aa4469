<?php

namespace App\Http\Controllers\AdminAuth;

use App\Admin;
use Validator;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Auth\Authenticatable;

use Mail;


class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after login / registration.
     *
     * @var string
     */
    protected $redirectTo = '/admin/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //$this->middleware('admin.guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'email' => 'required|email|max:255|unique:admin_users,username',
            'name' => 'required|max:255',
            'password' => 'required|min:6|confirmed',
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return Admin
     */
    protected function create(array $data)
    {
		$userModel = config('admin.database.member_model');
		$hashPW = Hash::make( $data['password'] );
		
		$token = bin2hex(openssl_random_pseudo_bytes(16));
		
		// ユーザ登録
		$id = DB::table('admin_users')->insertGetId([
			'username' => $data['email'],
			'name' => $data['name'],
			'password' => $hashPW,
			'created_at' => date('Y-m-d H:i:s'),
			'updated_at' => date('Y-m-d H:i:s'),
			'member_type' => 0,
			'permission_type' => 3,
			'verification_token' => $token,
		]);
		
		DB::table('admin_role_users')->insert([
			'user_id' => $id,
			'role_id' => 4,
			'created_at' => date('Y-m-d H:i:s'),
			'updated_at' => date('Y-m-d H:i:s'),
		]);
		
		$this->sendEmail($data['email'],$data["name"],$token);
		
		
		return redirect()->route('user.view.sendmail');
        //return Admin::create([
        //    'email' => $data['email'],
        //    'name' => $data['name'],
        //    'password' => bcrypt($data['password']),
        //]);
    }
	
	protected function register(Request $request)
	{
        $this->validator($request->all())->validate();
		$user = $this->create($request->all());
        event(new Registered($user));

        //$this->guard()->login($user);

        if ($response = $this->registered($request, $user)) {
            return $response;
        }

        return $request->wantsJson()
                    ? new JsonResponse([], 201)
                    : redirect()->route('user.view.sendmail');
	}

    /**
     * Show the application registration form.
     *
     * @return \Illuminate\Http\Response
     */
    public function showRegistrationForm()
    {
        return view('admin.auth.register');
    }
	
	public function showSendedMail()
	{
		return view('admin.auth.senedmail');
	}
	
	// 仮登録メール
	public function sendEmail($email,$name,$token)
	{
		Mail::send(
			'emails.register',
			[
				'name' => $name,
				'email' => $email,
				'token' => $token,
			],
			function($m) use ($email,$name) {
				$m->from('<EMAIL>','sapocha-pro');
				$m->to($email,$name)->subject('sapocha-pro 仮登録メール');
			}
		);
	}

    /**
     * Get the guard to be used during registration.
     *
     * @return \Illuminate\Contracts\Auth\StatefulGuard
     */
    protected function guard()
    {
        //return Auth::guard('admin');
    }
	
	public function getVerification(Request $request, $token)
	{
		if (! $this->validateRequest($request)) {
            return view('admin.auth.registerfailed');
        }
		if(DB::table('admin_users')->where('username',$request->email)->where('verification_token',$token)->exists()){
			DB::table('admin_users')->where('username',$request->email)->where('verification_token',$token)->update([
				'verified' => 1,
				//'verification_token' => null,
			]);
			return view('admin.auth.registersuccess');
		} else {
			return view('admin.auth.registerfailed');
		}
		
	}
	
	function validateRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);

        return $validator->passes();
    }
}

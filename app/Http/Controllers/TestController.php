<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
//use Discogs;
use GuzzleHttp\Client as GuzzleClient;

class TestController extends Controller
{
    //
	public function index(){
		// ebayのトークン
		$ebay_token = 'v^1.1#i^1#I^3#f^0#r^0#p^3#t^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';
		
		// 商品登録API
		$client = null;
		$client = new GuzzleClient();
		$sku = '0002';
		
		// リクエスト内容
		$request_body = [
			// 商品情報のメイン部分 https://developer.ebay.com/api-docs/sell/inventory/types/slr:Product
			'product' => [
				'title' => 'Test listing - do not bid or buy - awesome Apple watch test 2', //タイトル
				'aspects' => [ // 追加の詳細情報らしい
					'Feature' => ["Water resistance", "GPS"],
					'CPU' => ["Dual-Core Processor"],
				],
				'description' => 'Test listing - do not bid or buy \n Built-in GPS. Water resistance to 50 meters.1 A new lightning-fast dual-core processor. And a display that\u2019s two times brighter than before. Full of features that help you stay active, motivated, and connected, Apple Watch Series 2 is designed for all the ways you move ',
				'upc' => ["888462079525"],
				'imageUrls' => [
					"http://store.storeimages.cdn-apple.com/4973/as-images.apple.com/is/image/AppleInc/aos/published/images/S/1/S1/42/S1-42-alu-silver-sport-white-grid?wid=332&hei=392&fmt=jpeg&qlt=95&op_sharpen=0&resMode=bicub&op_usm=0.5,0.5,0,0&iccEmbed=0&layer=comp&.v=1472247758975",
					"http://store.storeimages.cdn-apple.com/4973/as-images.apple.com/is/image/AppleInc/aos/published/images/4/2/42/stainless/42-stainless-sport-white-grid?wid=332&hei=392&fmt=jpeg&qlt=95&op_sharpen=0&resMode=bicub&op_usm=0.5,0.5,0,0&iccEmbed=0&layer=comp&.v=1472247760390",
					"http://store.storeimages.cdn-apple.com/4973/as-images.apple.com/is/image/AppleInc/aos/published/images/4/2/42/ceramic/42-ceramic-sport-cloud-grid?wid=332&hei=392&fmt=jpeg&qlt=95&op_sharpen=0&resMode=bicub&op_usm=0.5,0.5,0,0&iccEmbed=0&layer=comp&.v=1472247758007",
				],
				
			],
			// 状態 enumになってるからどっかで対応必要 https://developer.ebay.com/api-docs/sell/inventory/types/slr:ConditionEnum
			'condition' => 'NEW',
			// 発送方法？梱包の詳細情報 https://developer.ebay.com/api-docs/sell/inventory/types/slr:PackageWeightAndSize
			'packageWeightAndSize' => [
				'dimensions' => [
					'height' => 5,
					'length' => 10,
					'width' => 15,
					'unit' => 'INCH',
				],
				'packageType' => 'MAILING_BOX',
				'weight' => [
					'value' => 2,
					'unit' => 'POUND',
				],
			],
			// 在庫数量指定とか？ https://developer.ebay.com/api-docs/sell/inventory/types/slr:Availability
			'availability' => [
				'shipToLocationAvailability' => [
					'quantity' => 10,
				],
			],
		];
		
		//var_dump(json_encode($request_body));
		$response = $client->request('PUT','https://api.sandbox.ebay.com/sell/inventory/v1/inventory_item/'.$sku,[
			'http_errors' => false,
			'headers' => [
				'Authorization' => [
					'Bearer '.$ebay_token,
				],
				'Accept' => ['application/json'],
				'Content-Type' => ['application/json'],
				'Content-Language' => ['en-US'],
			],
			'json' => $request_body,
		]);
		
		
		var_dump($response->getStatusCode());
		$response_body = $response->getBody()->getContents();
		var_dump($response_body);
		//parse_str($response_body,$var);
		//var_dump($var);
		
		
		//// 在庫取得API
		//$client = null;
		//$client = new GuzzleClient();
		//$response = $client->request('GET','https://api.sandbox.ebay.com/sell/inventory/v1/inventory_item',[
		//	'http_errors' => false,
		//	'headers' => [
		//		'Authorization' => [
		//			'Bearer '.$ebay_token,
		//		],
		//		'Accept' => ['application/json'],
		//	]
		//]);
		//
		//
		//var_dump($response->getStatusCode());
		//$response_body = $response->getBody()->getContents();
		//var_dump($response_body);
		//parse_str($response_body,$var);
		//var_dump($var);
		
	}
	
	function checkProgram(){
		$ebay_token = $_GET['token'];
		if(!$ebay_token){
			// 失敗？
			return response()->json(['get' => $_POST],401);
		}
		
		$client = null;
		$client = new GuzzleClient();
		
		$response = $client->request('GET','https://api.sandbox.ebay.com/sell/account/v1/program/get_opted_in_programs',[
			'http_errors' => false,
			'headers' => [
				'Authorization' => [
					'Bearer '.$ebay_token,
				],
				'Accept' => ['application/json'],
				'Content-Type' => ['application/json'],
				'Content-Language' => ['en-US'],
			],
		]);
		$response_body = $response->getBody()->getContents();
		$obj = json_decode($response_body,true); 
		if($response->getStatusCode() != 200){
			// 失敗？
			return response()->json([
				$obj
			],$response->getStatusCode());
		}
		
		
		if(!isset($obj["programs"])){
			// 失敗？
			return response()->json([
				$obj
			],403);
		}
		$isOK = false;
		foreach($obj["programs"] as $row){
			if(isset($row["programType"]) && $row["programType"] == "SELLING_POLICY_MANAGEMENT") {
				$isOK = true;
				break;
			}
		}
		if(!$isOK){
			// 失敗？
			return response()->json([
				$obj
			],403);
		}
		
		return Response()->json();
		
	}
}

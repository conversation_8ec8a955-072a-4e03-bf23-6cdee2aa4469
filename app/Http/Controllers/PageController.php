<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Auth;

class PageController extends Controller
{
    //


    public function index (Request $request) 
    {
        return redirect("/");
    }

    public function about (Request $request) 
    {
        return view('pages.about'); 
    }

    public function company (Request $request) 
    {
        return view('pages.company'); 
    }

    public function faq (Request $request) 
    {
        return view('pages.faq'); 
    }

    public function sitemap (Request $request) 
    {
        return view('pages.sitemap'); 
    }

    public function terms_of_service (Request $request) 
    {
        return view('pages.terms_of_service'); 
    }

    public function privacy_policy (Request $request) 
    {
        return view('pages.privacy_policy'); 
    }

    public function commercial_transaction (Request $request) 
    {
        return view('pages.commercial_transaction'); 
    }

}
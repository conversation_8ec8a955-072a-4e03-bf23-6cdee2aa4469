<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Mail\ContactSendmail;

class ContactController extends Controller
{
	public function index()
	{
		//フォーム入力画ページのviewを表示
		return view('contact.index');
	}


	public function confirm(Request $request)
	{
		$request->validate([
			'email' => 'required|email',
			'title' => 'required',
			'body'  => 'required',
		]);
		
		//フォームから受け取ったすべてのinputの値を取得
		$inputs = $request->all();

		//入力内容確認ページのviewに変数を渡して表示
		return view('contact.confirm', [
			'inputs' => $inputs,
		]);
	}

	public function send(Request $request)
	{
		$request->validate([
            'email' => 'required|email',
            'title' => 'required',
            'body'  => 'required'
        ]);
		$action = $request->input('action');
		$inputs = $request->except('action');
		if($action !== 'submit'){
            return redirect()
                ->route('contact.index')
                ->withInput($inputs);
		} else {
			\Mail::to($inputs['email'])->bcc('<EMAIL>')->send(new ContactSendmail($inputs));
			$request->session()->regenerateToken();
			return view('contact.thanks');
		}
	}
}

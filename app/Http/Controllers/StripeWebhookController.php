<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Stripe\Stripe;
use Stripe\Webhook;
use Stripe\Customer;
use Stripe\Checkout\Session;
use App\Services\EbayTrialService;

class StripeWebhookController extends Controller
{
    protected $ebayTrialService;

    public function __construct(EbayTrialService $ebayTrialService)
    {
        $this->ebayTrialService = $ebayTrialService;
    }

    public function createCheckoutSession(Request $request)
    {
        try {
            // Stripe設定の確認
            $stripeSecret = config('services.stripe.secret');
            $stripePriceId = env('STRIPE_PRICE_ID', 'price_1REgk4GgRzB8rqk2NIHFfVfW');

            if (!$stripeSecret) {
                Log::error('Stripe secret key not configured');
                return redirect('/admin')->with('error', 'Stripe設定が正しくありません。');
            }

            Stripe::setApiKey($stripeSecret);

            $user = Auth::guard('admin')->user();
            if (!$user) {
                Log::error('Stripe checkout: Unauthorized access attempt');
                return redirect('/admin/auth/login')->with('error', 'ログインが必要です');
            }

            Log::info('Creating checkout session for user', [
                'user_id' => $user->id,
                'email' => $user->username,
                'existing_stripe_customer_id' => $user->stripe_customer_id
            ]);

            // 既存の顧客を取得または作成（強化版）
            $customer = null;
            $shouldCreateNewCustomer = false;

            if ($user->stripe_customer_id) {
                try {
                    // Stripe上で顧客が存在するかチェック
                    $customer = Customer::retrieve($user->stripe_customer_id);

                    // 顧客が削除されているかチェック
                    if (isset($customer->deleted) && $customer->deleted) {
                        Log::warning('Stripe customer was deleted', [
                            'user_id' => $user->id,
                            'deleted_customer_id' => $user->stripe_customer_id
                        ]);
                        $customer = null;
                        $shouldCreateNewCustomer = true;
                    } else {
                        Log::info('Using existing Stripe customer', [
                            'user_id' => $user->id,
                            'stripe_customer_id' => $customer->id
                        ]);
                    }
                } catch (\Stripe\Exception\InvalidRequestException $e) {
                    Log::warning('Stripe customer not found, creating new one', [
                        'user_id' => $user->id,
                        'old_customer_id' => $user->stripe_customer_id,
                        'error' => $e->getMessage()
                    ]);
                    $customer = null;
                    $shouldCreateNewCustomer = true;
                } catch (\Exception $e) {
                    Log::error('Error retrieving Stripe customer', [
                        'user_id' => $user->id,
                        'customer_id' => $user->stripe_customer_id,
                        'error' => $e->getMessage()
                    ]);
                    $customer = null;
                    $shouldCreateNewCustomer = true;
                }
            } else {
                $shouldCreateNewCustomer = true;
            }

            // 新しい顧客を作成
            if (!$customer || $shouldCreateNewCustomer) {
                try {
                    $customer = Customer::create([
                        'email' => $user->username,
                        'metadata' => [
                            'user_id' => $user->id,
                            'created_from' => 'checkout_session'
                        ]
                    ]);

                    // データベースを更新
                    DB::table('admin_users')->where('id', $user->id)->update([
                        'stripe_customer_id' => $customer->id,
                        'updated_at' => now(),
                    ]);

                    Log::info('Created new Stripe customer', [
                        'user_id' => $user->id,
                        'new_stripe_customer_id' => $customer->id,
                        'old_customer_id' => $user->stripe_customer_id
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to create new Stripe customer', [
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);
                    return redirect('/admin')->with('error', 'Stripe顧客の作成に失敗しました。再度お試しください。');
                }
            }

            // eBayアカウントの重複チェック
            $shouldStartTrial = true;
            if ($user->ebay_id) {
                $shouldStartTrial = !$this->ebayTrialService->isEbayAccountUsedBefore($user->ebay_id, $user->id);
                Log::info('eBay duplication check result', [
                    'user_id' => $user->id,
                    'ebay_id' => $user->ebay_id,
                    'should_start_trial' => $shouldStartTrial
                ]);
            }

            // セッションパラメータの準備
            $sessionParams = [
                'mode' => 'subscription',
                'line_items' => [
                    [
                        'price' => $stripePriceId,
                        'quantity' => 1,
                    ]
                ],
                'success_url' => url('/stripe/success'),
                'cancel_url' => url('/stripe/cancel'),
                'metadata' => [
                    'user_id' => $user->id,
                    'should_start_trial' => $shouldStartTrial ? 'true' : 'false'
                ],
                'client_reference_id' => (string)$user->id,
                'customer' => $customer->id,
            ];

            // 2ヶ月無料期間を設定
            if ($shouldStartTrial) {
                $sessionParams['subscription_data'] = [
                    'trial_period_days' => 60,
                    'metadata' => [
                        'trial_type' => 'two_month_trial',
                        'user_id' => $user->id
                    ]
                ];
                Log::info('Adding 2-month trial to subscription', ['user_id' => $user->id]);
            } else {
                Log::info('Skipping trial due to eBay duplication', ['user_id' => $user->id]);
            }

            $session = Session::create($sessionParams);

            Log::info('Checkout session created successfully', [
                'session_id' => $session->id,
                'user_id' => $user->id,
                'customer_id' => $customer->id,
                'session_url' => $session->url
            ]);

            return redirect($session->url);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            Log::error('Stripe InvalidRequestException', [
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage(),
                'stripe_error' => $e->getStripeCode(),
                'request_id' => $e->getRequestId()
            ]);
            return redirect('/admin')->with('error', 'Stripe設定に問題があります: ' . $e->getMessage());
        } catch (\Stripe\Exception\AuthenticationException $e) {
            Log::error('Stripe AuthenticationException', [
                'error' => $e->getMessage()
            ]);
            return redirect('/admin')->with('error', 'Stripe認証に失敗しました。APIキーを確認してください。');
        } catch (\Exception $e) {
            Log::error('Error creating checkout session', [
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect('/admin')->with('error', '決済処理中にエラーが発生しました。しばらく時間をおいて再度お試しください。');
        }
    }

    public function handle(Request $request)
    {
        Log::info('Stripe webhook received', ['headers' => $request->headers->all()]);

        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $secret = config('services.stripe.webhook_secret');

        if (!$sigHeader) {
            Log::error('Stripe webhook missing signature header');
            return response()->json(['error' => 'missing signature'], 400);
        }

        try {
            $event = Webhook::constructEvent($payload, $sigHeader, $secret);
            Log::info('Stripe webhook event constructed', ['event_type' => $event->type, 'event_id' => $event->id]);
        } catch (\Exception $e) {
            Log::error('Stripe webhook signature verification failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'invalid signature'], 400);
        }

        // ペイロードの内容をログに出力（デバッグ用）
        Log::debug('Stripe webhook payload', ['event_data' => json_decode($payload, true)]);

        try {
            switch ($event->type) {
                case 'checkout.session.completed':
                    $this->handleCheckoutSessionCompleted($event->data->object);
                    break;

                case 'invoice.paid':
                    $this->handleInvoicePaid($event->data->object);
                    break;

                case 'invoice.payment_failed':
                    $this->handlePaymentFailed($event->data->object);
                    break;

                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event->data->object);
                    break;

                case 'customer.subscription.trial_will_end':
                    $this->handleTrialWillEnd($event->data->object);
                    break;

                default:
                    Log::info('Unhandled event type', ['type' => $event->type]);
                    break;
            }
        } catch (\Exception $e) {
            Log::error('Error processing Stripe webhook', [
                'event_type' => $event->type,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['status' => 'error occurred but processed'], 200);
        }

        return response()->json(['status' => 'success'], 200);
    }

    private function handleCheckoutSessionCompleted($session)
    {
        Log::info('Processing checkout.session.completed', [
            'session_id' => $session->id,
            'customer_id' => $session->customer ?? null,
            'client_reference_id' => $session->client_reference_id ?? null,
            'metadata' => $session->metadata ?? null
        ]);

        $customerId = $session->customer ?? null;
        $userId = $session->client_reference_id ?? ($session->metadata->user_id ?? null);
        $shouldStartTrial = ($session->metadata->should_start_trial ?? 'true') === 'true';

        // 顧客詳細情報から直接メールアドレスを取得
        $email = null;
        if (isset($session->customer_details) && isset($session->customer_details->email)) {
            $email = $session->customer_details->email;
            Log::info('Found email from customer_details', ['email' => $email]);
        }

        if (!$customerId) {
            Log::error('checkout.session.completed: missing customer ID');
            return;
        }

        $user = $this->findUserForPayment($userId, $customerId, $email);

        if ($user) {
            try {
                $updateData = [
                    'member_type' => 1,
                    'stripe_customer_id' => $customerId,
                    'updated_at' => now(),
                ];

                // サブスクリプションIDを保存（もしあれば）
                if (isset($session->subscription)) {
                    $updateData['subscription_id'] = $session->subscription;
                }

                // 無料期間の設定
                if ($shouldStartTrial) {
                    $this->ebayTrialService->startTwoMonthTrial($user->id);
                    Log::info('Started 2-month trial for user', ['user_id' => $user->id]);
                } else {
                    // 無料期間なしの場合は即座に請求開始
                    $updateData['next_payment_date'] = Carbon::now()->addMonth()->toDateTimeString();
                    Log::info('No trial for user due to eBay duplication', ['user_id' => $user->id]);
                }

                $updated = DB::table('admin_users')->where('id', $user->id)->update($updateData);

                Log::info('checkout.session.completed: user upgraded', [
                    'user_id' => $user->id,
                    'rows_updated' => $updated,
                    'trial_started' => $shouldStartTrial
                ]);

                // eBay使用履歴を記録
                if ($user->ebay_id && $user->ebay_username) {
                    $this->ebayTrialService->recordEbayUsage($user->ebay_id, $user->ebay_username, $user->id);
                }
            } catch (\Exception $e) {
                Log::error('Database update failed', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        } else {
            Log::warning('checkout.session.completed: user not found', [
                'email' => $email,
                'customer_id' => $customerId,
                'client_reference_id' => $session->client_reference_id ?? null
            ]);
        }
    }

    private function handleInvoicePaid($invoice)
    {
        $customerId = $invoice->customer ?? null;
        $email = null;

        if (isset($invoice->customer_email)) {
            $email = $invoice->customer_email;
        } elseif (isset($invoice->customer_details) && isset($invoice->customer_details->email)) {
            $email = $invoice->customer_details->email;
        }

        Log::info('Processing invoice.paid', [
            'invoice_id' => $invoice->id,
            'customer_id' => $customerId,
            'email' => $email,
            'subscription_id' => $invoice->subscription ?? null
        ]);

        if (!$customerId) {
            Log::error('invoice.paid: missing customer ID');
            return;
        }

        $user = $this->findUserForPayment(null, $customerId, $email);

        if ($user) {
            try {
                $updateData = [
                    'member_type' => 1,
                    'updated_at' => now(),
                ];

                // Stripe顧客IDが異なる場合は更新
                if ($user->stripe_customer_id != $customerId) {
                    $updateData['stripe_customer_id'] = $customerId;
                    Log::info('Updating Stripe customer ID', [
                        'user_id' => $user->id,
                        'old_id' => $user->stripe_customer_id,
                        'new_id' => $customerId
                    ]);
                }

                // サブスクリプションIDを保存
                if (isset($invoice->subscription)) {
                    $updateData['subscription_id'] = $invoice->subscription;
                }

                // 無料期間中でない場合のみ次回支払日を更新
                $trialStatus = $this->ebayTrialService->getTrialStatus($user->id);
                if (!$trialStatus['trial_active']) {
                    $updateData['next_payment_date'] = Carbon::now()->addMonth()->toDateTimeString();
                }

                $updated = DB::table('admin_users')
                    ->where('id', $user->id)
                    ->update($updateData);

                Log::info('invoice.paid: user updated', [
                    'user_id' => $user->id,
                    'customer_id' => $customerId,
                    'rows_updated' => $updated,
                    'trial_active' => $trialStatus['trial_active']
                ]);
            } catch (\Exception $e) {
                Log::error('Database update failed', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        } else {
            Log::warning('invoice.paid: user not found', [
                'customer_id' => $customerId,
                'email' => $email
            ]);
        }
    }

    private function handlePaymentFailed($invoice)
    {
        $customerId = $invoice->customer ?? null;

        Log::info('Processing invoice.payment_failed', [
            'invoice_id' => $invoice->id,
            'customer_id' => $customerId,
            'subscription_id' => $invoice->subscription ?? null
        ]);

        if (!$customerId) {
            Log::error('invoice.payment_failed: missing customer ID');
            return;
        }

        $user = DB::table('admin_users')->where('stripe_customer_id', $customerId)->first();

        if ($user) {
            try {
                DB::table('admin_users')->where('id', $user->id)->update([
                    'oauth_code' => '',
                    'member_type' => 0,
                    'access_token' => null,
                    'access_token_expires_in' => null,
                    'refresh_token' => null,
                    'ebay_id' => null,
                    'ebay_username' => null,
                    'refresh_token_expires_in' => null,
                    'trial_started_at' => null,
                    'trial_ends_at' => null,
                    'subscription_id' => null,
                    'updated_at' => now(),
                ]);

                Log::info('invoice.payment_failed: user downgraded and data cleared', [
                    'user_id' => $user->id,
                    'customer_id' => $customerId,
                ]);
            } catch (\Exception $e) {
                Log::error('Database update failed', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        } else {
            Log::warning('invoice.payment_failed: user not found', ['customer_id' => $customerId]);
        }
    }

    private function handleSubscriptionDeleted($subscription)
    {
        $customerId = $subscription->customer ?? null;

        Log::info('Processing customer.subscription.deleted', [
            'subscription_id' => $subscription->id,
            'customer_id' => $customerId
        ]);

        if (!$customerId) {
            Log::error('customer.subscription.deleted: missing customer ID');
            return;
        }

        $user = DB::table('admin_users')->where('stripe_customer_id', $customerId)->first();

        if ($user) {
            try {
                DB::table('admin_users')->where('id', $user->id)->update([
                    'oauth_code' => '',
                    'member_type' => 0,
                    'access_token' => null,
                    'access_token_expires_in' => null,
                    'refresh_token' => null,
                    'ebay_id' => null,
                    'ebay_username' => null,
                    'refresh_token_expires_in' => null,
                    'trial_started_at' => null,
                    'trial_ends_at' => null,
                    'subscription_id' => null,
                    'updated_at' => now(),
                ]);

                Log::info('customer.subscription.deleted: user downgraded and data cleared', [
                    'user_id' => $user->id,
                    'customer_id' => $customerId,
                ]);
            } catch (\Exception $e) {
                Log::error('Database update failed', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        } else {
            Log::warning('customer.subscription.deleted: user not found', ['customer_id' => $customerId]);
        }
    }

    private function handleTrialWillEnd($subscription)
    {
        $customerId = $subscription->customer ?? null;

        Log::info('Processing customer.subscription.trial_will_end', [
            'subscription_id' => $subscription->id,
            'customer_id' => $customerId,
            'trial_end' => $subscription->trial_end ?? null
        ]);

        if (!$customerId) {
            Log::error('customer.subscription.trial_will_end: missing customer ID');
            return;
        }

        $user = DB::table('admin_users')->where('stripe_customer_id', $customerId)->first();

        if ($user) {
            // 無料期間終了の通知やメール送信などの処理を追加可能
            Log::info('Trial ending soon for user', [
                'user_id' => $user->id,
                'trial_end' => $subscription->trial_end
            ]);
        }
    }

    /**
     * 支払い処理でユーザーを特定するヘルパーメソッド
     */
    private function findUserForPayment($userId, $customerId, $email)
    {
        // 優先順位: client_reference_id > customer_id > email
        $user = null;

        if ($userId) {
            $user = DB::table('admin_users')->where('id', $userId)->first();
            Log::info('Looked up user by ID', ['user_id' => $userId, 'found' => !!$user]);
        }

        if (!$user && $customerId) {
            $user = DB::table('admin_users')->where('stripe_customer_id', $customerId)->first();
            Log::info('Looked up user by Stripe customer ID', ['customer_id' => $customerId, 'found' => !!$user]);
        }

        if (!$user && $email) {
            $user = DB::table('admin_users')->where('username', $email)->first();
            Log::info('Looked up user by email', ['email' => $email, 'found' => !!$user]);

            // 大文字小文字を区別せずに検索
            if (!$user) {
                $user = DB::table('admin_users')
                    ->whereRaw('LOWER(username) = ?', [strtolower($email)])
                    ->first();
                Log::info('Looked up user by case-insensitive email', ['email' => $email, 'found' => !!$user]);
            }
        }

        return $user;
    }

    public function success()
    {
        return view('stripe.success', [
            'title' => '支払いに成功しました',
            'message' => 'サブスクリプションの登録が完了しました。'
        ]);
    }

    public function cancel()
    {
        return view('stripe.cancel', [
            'title' => '支払いがキャンセルされました',
            'message' => '支払い処理はキャンセルされました。'
        ]);
    }

    /**
     * 無料期間ステータスを取得するAPI
     */
    public function getTrialStatus(Request $request)
    {
        $user = Auth::guard('admin')->user();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 401);
        }

        try {
            $trialStatus = $this->ebayTrialService->getTrialStatus($user->id);

            return response()->json([
                'status' => 'success',
                'has_trial' => $trialStatus['has_trial'],
                'trial_days_remaining' => $trialStatus['trial_days_remaining'],
                'trial_active' => $trialStatus['trial_active']
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting trial status', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['status' => 'error', 'message' => 'Internal error'], 500);
        }
    }
}

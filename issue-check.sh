 #!/bin/bash
echo -e "\n=== C-03: Disk Space Check ==="
  echo "Disk usage:"
  df -h | grep -E "(Filesystem|/dev|tmpfs)" | head -10

  echo -e "\nLarge log files (>50M):"
  find /srv/app/storage/logs /var/log -name "*.log" -size +50M -exec ls -lh {} \; 2>/dev/null

  echo -e "\n=== F-01: Migration Status ==="
  php artisan migrate:status 2>/dev/null | head -15

  echo -e "\n=== Laravel Scheduler Last Run ==="
  tail -20 storage/logs/laravel.log 2>/dev/null | grep -i "schedule\|command" | tail -5

  echo -e "\nValidation complete."

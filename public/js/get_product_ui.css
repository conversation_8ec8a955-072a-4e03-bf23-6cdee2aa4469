.modal_area {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 9999;
	display:none;
}

.modal_area_back {
	position: fixed;
	top: -10vh;
	left: -10vw;
	width: 120vw;
	height: 120vh;
	background-color: #666666;
	z-index: 9999;
	opacity: 0.6;
}

.modal_window {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	margin: auto;

	width: 95%;
	height: 95%;

	background-color: #ffffff;
	opacity: 1;
	z-index: 99999;

	padding: 1em;
	border-radius: 1em;
}

.modal_header {
}

.modal_header_close {
	display: inline-block;
	padding: 0.5em;
	border: solid 1px;
	cursor: pointer;
	float: right;
	clear: both;
}

.modal_body {
	clear: both;
	overflow-x: hidden;
	overflow-y: scroll;
	height: 90%;
}

.modal_body {
	overflow-x: hidden;
	overflow-y: scroll;
	height: 90%;
}

.modal_body_section{
}

.modal_help_section {
	display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 95%;
    flex-wrap: nowrap;
}
.modal_help_section > div {
	line-height: 0.5;
}

.select_tab_area{
	display: flex;

}

.select_tab {
	border: 1px solid #cccccc;
	padding: 1em;
	background-color: #dddddd;
	border-radius: 1em;
	margin: 0 0.5em;
}

.select_tab.selected {
	background-color: #666666;
	color: #ffffff;
}

.modal_url_input_area {
	padding: 1em;
	margin: 1em;
	border: 1px solid #666666;
	border-radius: 1em;
}

#yahoo_area{
	display: block;
}
#mercari_area{
	display: none;
}
#amazon_area{
	display: none;
}
#ebay_area{
	display: none;
}
#jancode_area{
	display: none;
}
#jancode_amazon_area{
	display: none;
}

#preview_url_area_body {
	padding: 1em;
	margin: 1em;
	border: 1px solid #666666;
	border-radius: 1em;
}

#preview_url_area {
	width: 100%;
}

#preview_title_area {
	width: 100%;
	border: 1px solid #cccccc;
	padding: 1em;
	margin: 1em 0;
}

	#preview_title_mst {
		width: 100%;
		margin: 1em 0;
	}
		#preview_title_mst_input {
			width: 100%;
			background-color: #cccccc;
		}

	#preview_title_change {
		width: 100%;
		margin: 1em 0;
	}
		input#preview_title_input {
			width: 100%;
		}

#preview_comment_area {
	width: 100%;
	border: 1px solid #cccccc;
	padding: 1em;
	margin: 1em 0;
}

	#preview_comment_html {
		margin: 1em 0;
		display: none;
	}

		iframe#preview_comment_html_body {
			width: 100%;
			/*height: 18em;*/
			height: 40vh;
			display: none;
		}

	#preview_comment_mst {
		margin: 1em 0;
	}

		#preview_comment_mst_input {
			width: 100%;
			height: 12em;
			background-color: #cccccc;
			resize: vertical;
			min-height: 3em;
		}

	div#preview_comment_change {
		margin: 1em 0;
	}
		#preview_comment_input {
			width: 100%;
			height: 12em;
			resize: vertical;
			min-height: 3em;
		}

.sample_image_display_thumbnail {
	width: 204px;
	height: 204px;
	display: inline-block;
	border: 1px solid #666666;
	position: relative;
}

.sample_image_display_thumbnail IMG {
	display: block;
	height: 200px;
	object-fit: contain;
	width: 100%;
}
.sample_image_display_thumbnail_delete {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    text-align: center;
    font-size: 15px;
	cursor: pointer;
}


#preview_image_area {
	width: 100%;
	border: 1px solid #cccccc;
	padding: 1em;
	margin: 1em 0;
}

	#preview_image_list {
		margin: 1em 0;
		display: flex;
		flex-wrap: wrap;
	}

		.preview_image_list_unit {
			margin: 0.5em;
		}
		.preview_image_list_unit.selected {
			background-color: #c9c9c9;
		}
			#preview_image_list_unit label {
				text-align: center;
			}
				.preview_image_list_unit_selecter {
					text-align: center;
				}

				.preview_image_list_unit_thumbnail {
					width: 200px;
					height: 200px;
					display: inline-block;
					border: 1px solid #666666;
				}

				.preview_image_list_unit_thumbnail IMG {
					display: block;
					height: 200px;
					object-fit: contain;
					width: 100%;
				}
/* =============================================== */
.ball-pulse-area{
	display: none;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9999999;
}
.ball-pulse {
    position: relative;
    top: 50%;
    width: 100%;
    text-align: center;
}
@-webkit-keyframes scale {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1; }
  45% {
    -webkit-transform: scale(0.1);
            transform: scale(0.1);
    opacity: 0.7; }
  80% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1; } }
@keyframes scale {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1; }
  45% {
    -webkit-transform: scale(0.1);
            transform: scale(0.1);
    opacity: 0.7; }
  80% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1; } }

.ball-pulse > div:nth-child(1) {
  -webkit-animation: scale 0.75s -0.24s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
          animation: scale 0.75s -0.24s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08); }

.ball-pulse > div:nth-child(2) {
  -webkit-animation: scale 0.75s -0.12s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
          animation: scale 0.75s -0.12s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08); }

.ball-pulse > div:nth-child(3) {
  -webkit-animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
          animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08); }

.ball-pulse > div {
  background-color: MediumBlue;
  width: 15px;
  height: 15px;
  border-radius: 100%;
  margin: 2px;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  display: inline-block; }
/* =============================================== */

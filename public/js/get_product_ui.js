var get_product_info_config = new Array();
get_product_info_config['title_set_selecter'] = '#title';
get_product_info_config['comment_set_selecter'] = '#comment';
get_product_info_config['image_set_selecter'] = '#image';
get_product_info_config['image_sample_set_selecter'] = '#sample';

var open_modal_button_html = `
<input type="button" name="input_open_modal_button" class="input_open_modal_button" value="参照元サイトから商品情報を取得">
`;
var modal_html = `
<div class="modal_area">
	<div class="modal_area_back">
	</div>
	<div class="modal_window">
		<div class="modal_header">
			<div class="modal_header_close">close</div>
		</div>
		<div class="modal_body">
			<div class="modal_body_section" id="select_area">
				<div class="select_tab_area">
					<div class="select_tab selected" id="yahoo_tab">ヤフオク</div>
					<div class="select_tab" id="mercari_tab">メルカリ</div>
					<div class="select_tab" id="amazon_tab">Amazon</div>
					<div class="select_tab" id="jancode_tab">JANコード(Yahoo!ショッピング)</div>
				</div>
			</div>
			<div class="modal_body_section" id="yahoo_area">
				<div class="modal_url_input_area">
					<div class="type_name_area">ヤフオク</div>
					<div class="input_url_area"><input type="text" name="input_get_product_url_y" value="" placeholder="ヤフオク出品詳細URL（https://page.auctions.yahoo.co.jp/jp/auction/m1234567890）もしくは出品ID（m1234567890）" style="width:90%;"></div>
					<div class="input_button_area"><input type="button" name="input_get_product_button_y" class="input_get_product_button_y" value="Get Info"></div>
				</div>
			</div>
			<div class="modal_body_section" id="mercari_area">
				<div class="modal_url_input_area">
					<div class="type_name_area">メルカリ</div>
					<div class="input_url_area"><input type="text" name="input_get_product_url_m" value="" placeholder="メルカリ出品詳細URL（https://jp.mercari.com/item/m12345678900）もしくは出品ID（m12345678900）" style="width:90%"></div>
					<div class="input_button_area"><input type="button" name="input_get_product_button_m" class="input_get_product_button_m" value="Get Info"></div>
				</div>
			</div>
			<div class="modal_body_section" id="amazon_area">
				<div class="modal_url_input_area">
					<div class="type_name_area">Amazon</div>
					<div class="input_url_area"><input type="text" name="input_get_product_url_a" value="" placeholder="ASINコード（A12B34C5DE）" style="width:90%"></div>
					<div class="input_button_area"><input type="button" name="input_get_product_button_a" class="input_get_product_button_a" value="Get Info"></div>
				</div>
			</div>
			<div class="modal_body_section" id="jancode_area">
				<div class="modal_url_input_area">
					<div class="type_name_area">JANコード(Yahoo!ショッピング)</div>
					<div class="input_url_area"><input type="text" name="input_get_product_url_j" value="" placeholder="451234567890"></div>
					<div class="input_button_area"><input type="button" name="input_get_product_button_j" class="input_get_product_button_j" value="Get Info"></div>
					
				</div>
			</div>
			<div class="modal_help_section">
				<div><span class="help-block">・&nbsp;ヤフオクの商品情報取得方法は▶<a href="https://www.youtube.com" target="_blank">こちら</a></span></div>
				<div><span class="help-block">・&nbsp;メルカリの商品情報取得方法は▶<a href="https://www.youtube.com" target="_blank">こちら</a></span></div>
				<div><span class="help-block">・&nbsp;アマゾンの商品情報取得方法は▶<a href="https://www.youtube.com" target="_blank">こちら</a></span></div>
				<div><span class="help-block">・&nbsp;JANコードから商品情報取得方法は▶<a href="https://www.youtube.com" target="_blank">こちら</a></span></div>
			</div>
			<div class="modal_body_section" id="preview_area">
			</div>
			<input type="hidden" id="temp_referrer_mode" value="">
			<input type="hidden" id="temp_referrer_id" value="">
		</div>
		<div class="modal_footer">
			
		</div>
	</div>
</div>
<div class="ball-pulse-area"><div class="ball-pulse"><div></div><div></div><div></div></div></div>
`;
var preview_html = `
<div id="preview_area_body">
	<div id="preview_url_area">
	</div>
	<div id="preview_title_area">
		タイトル
		<div id="preview_title_mst">
			原文<br>
			<input type="text" value="" id="preview_title_mst_input" readonly>
		</div>
		<div id="preview_title_change">
			反映文<br>
			<input type="text" value="" id="preview_title_input">
		</div>
	</div>
	<div id="preview_comment_area">
		コメント
		<div id="preview_comment_html">
			<iframe id="preview_comment_html_body"></iframe>
		</div>
		<div id="preview_comment_mst">
			原文<br>
			<textarea id="preview_comment_mst_input" readonly></textarea>
		</div>
		<div id="preview_comment_change">
			反映文
			&nbsp;&nbsp;
			<input type="button" value="HTMLタグ削除" id="delete_html_tag_action"><br>
			<textarea id="preview_comment_input"></textarea>
		</div>
	</div>
	<div id="preview_image_area">
		イメージ<br />
		<input type="checkbox" class="check_all_image" id="check_all_image" /><label for="check_all_image">すべてを選択する</label> 
		<div id="preview_image_list">
		</div>
	</div>
	<input type="hidden" id="preview_product_price" value="">
	<div id="preview_set_button_area">
		<input type="button" name="preview_set_button_input" id="preview_set_button_input" value="内容をセットする">
	</div>
</div>
`;

var uploder_html = `
<div id="uploader_area_body">
	<input type="file" name="upload_file" id="upload_file" multiple>
	<input type="button" name="upload_file_button" id="upload_file_button" value="画像のアップロード">
</div>
`;


function setGetProductUI(){
	console.log("setGetProductUI()");
	//自身のscriptタグを取得
	let currentScript = (function() {
		if (document.currentScript) {
			return document.currentScript;
		}
		let scripts = document.getElementsByTagName('script');
		return scripts[scripts.length - 1];
	})();

	//モーダル起動ボタン追加
	if(!($('.input_open_modal_button').length)){
		//取得ボタンの追加
		$('.get_product_info_area').append(open_modal_button_html);
	}

	if(!($('.modal_area').length)){
		//モーダルの追加
		$('body').append(modal_html);
	}
}

function setUploaderUI(){
	console.log("setUploadUI()");
	//自身のscriptタグを取得
	let currentScript = (function() {
		if (document.currentScript) {
			return document.currentScript;
		}
		let scripts = document.getElementsByTagName('script');
		return scripts[scripts.length - 1];
	})();

	//ボタン追加
	if(!($('.uploader_area_body').length)){
		//取得ボタンの追加
		$('.get_uploder_area').append(uploder_html);
	}
}


var post_data = {
	mode:'',
	url:''
}


var arr_preview_image_list = [];
const _regex = new RegExp('https?:\/\/');

function get_product_info_request(arr_post_data){
	$('.ball-pulse-area').show();
	console.log('get_product_info_request');
	console.log(arr_post_data);
	$.ajax({
		type:'POST',
		url:'/api/get_product_info',
		data:arr_post_data,
		dataType: 'json',
		success: function(data) {
			console.log(data);
			if (data['status'] !== undefined) {
				if (data['status']=='ok') {
					$('#preview_area').empty();
					$('#preview_area').append(preview_html);

					if (data['mode'] !== undefined) {
						if (data['url'] !== undefined) {
							$('#preview_url_area').append(`<a href="`+data['url']+`" target="_brank">`+data['url']+`</a>`);
							_target_url = data['url'].replace(/https?:\/\//,'');
							//_target_url = _target_url.replaceAll(/\//gi,'_');
						} else {
							_target_url = '';
						}
					}

					if (data['product'] !== undefined) {
						if (data['product']['product_title'] !== undefined) {
							$('#preview_title_mst_input').val(data['product']['product_title']);
							$('#preview_title_input').val(data['product']['product_title']);
						}
						if (data['product']['product_comment'] !== undefined) {
							//if( $('#preview_comment_html') ).css('display') == 'block') {
							//	$('#preview_comment_html').contents().find('body').append(data['product']['product_comment']);
							//}
							let brTag = data['product']['product_comment'];
							brTag = brTag.replace(/\n/g, "<br>");
							$('#preview_comment_mst_input').val(brTag);
							$('#preview_comment_input').val(brTag);
						}
						if (data['product']['product_price'] !== undefined) {
							$('#preview_product_price').val(data['product']['product_price']);
						} else {
							$('#preview_product_price').val("");
						}
						$('#check_all_image').prop('checked',false);
						if (data['product']['product_image'] !== undefined) {
							arr_preview_image_list = data['product']['product_image'];
							$('#check_all_image').prop('checked',true);
							$.each(data['product']['product_image'], function(index, row) {
								if( row['url'] !== undefined ) {
									var image_selected = '';
									var image_checked = '';
									if( index==0 ){
										image_selected = 'selected';
									}
									image_checked = 'checked';
									var image_unit_html = `
										<div class="preview_image_list_unit `+image_selected+`">
											<label>
												<div class="preview_image_list_unit_selecter">
													<input type="checkbox" name="preview_image_input" value="`+index+`" `+image_checked+`>
												</div>
												<div class="preview_image_list_unit_thumbnail">
													<img src="`+row['url']+`">
												</div>
											</label>
										</div>
									`;
									$('#preview_image_list').append(image_unit_html);
								}
							});
						}
					}
					
				} else {
					$('.ball-pulse-area').hide();
					alert('error.');
					return;
				}
			} else {
				$('.ball-pulse-area').hide();
				alert('error.');
				return;
			}
		},
		error: function() {
			$('.ball-pulse-area').hide();
		},
		complete: function() {
			$('.ball-pulse-area').hide();
		}
	});
}
var _target_url = '';
var _target_mode = '';
var _target_jancode = '';
$(function() {

	//ヤフオク request
	$(document).on('click', '.input_get_product_button_y', function () {
		console.log('input_get_product_button_y');
		if( $('INPUT[name=input_get_product_url_y]').val()!="" ){
			let strUrl = $('INPUT[name=input_get_product_url_y]').val();
			try{
				let url = new URL(strUrl);
				console.log(url);
				let path = url.pathname;
				strUrl = path.replace('/jp/auction/', "");
			}catch{
				
			}
			post_data = {
				mode: 'yahoo',
				url: strUrl
			};
			get_product_info_request(post_data);
			$('#temp_referrer_mode').val("yahoo");
			$('#temp_referrer_id').val($('INPUT[name=input_get_product_url_y]').val());
		}
		$('')
	});


	//メルカリ request
	$(document).on('click', '.input_get_product_button_m', function () {
		console.log('input_get_product_button_m');
		if( $('INPUT[name=input_get_product_url_m]').val()!="" ){
			let strUrl = $('INPUT[name=input_get_product_url_m]').val();
			try{
				let url = new URL(strUrl);
				console.log(url);
				let path = url.pathname;
				strUrl = path.replace('/item/', "");
			}catch{
				
			}
			post_data = {
				mode: 'mercari',
				url: strUrl
			};
			get_product_info_request(post_data);
			$('#temp_referrer_mode').val("mercari");
			$('#temp_referrer_id').val($('INPUT[name=input_get_product_url_m]').val());
		}
	});


	//Amazon request
	$(document).on('click', '.input_get_product_button_a', function () {
		console.log('input_get_product_button_a');
		if( $('INPUT[name=input_get_product_url_a]').val()!="" ){
			let strUrl = $('INPUT[name=input_get_product_url_a]').val();
			try{
				let url = new URL(strUrl);
				console.log(url);
				let path = url.pathname;
				strUrl = path.replace('/dp/', "");
				strUrl = strUrl.replace('/gp/product/', "");
			}catch{
				
			}
			post_data = {
				mode: 'amazon',
				url: strUrl
			};
			get_product_info_request(post_data);
			$('#temp_referrer_mode').val("amazon");
			$('#temp_referrer_id').val($('INPUT[name=input_get_product_url_a]').val());
		}
	});
	
	//JANCODE request yahooショッピング
	$(document).on('click', '.input_get_product_button_j', function () {
		console.log('input_get_product_button_j');
		if( $('INPUT[name=input_get_product_url_j]').val()!="" ){
			post_data = {
				mode: 'jancode',
				url: $('INPUT[name=input_get_product_url_j]').val()
			};
			_target_mode = 'jancode';
			_target_jancode = $('INPUT[name=input_get_product_url_j]').val();
			
			get_product_info_request(post_data);
			$('#temp_referrer_mode').val("jancode");
			$('#temp_referrer_id').val($('INPUT[name=input_get_product_url_j]').val());
		}
	});

	$(document).on('click', '#upload_file_button', function () {
		let $upfile = $('input[name="upload_file"]');
		
		if($upfile.prop('files').length > 0){
			let product_image_filenames = [];
			$.each($upfile.prop('files'), function(idx,row){
				let fd = new FormData();
				fd.append("upfile", row);
				fd.append("idx",idx);
				$.ajax({
					url:'/api/file_upload',
					type:'post',
					data: fd,
					processData: false,
					contentType: false,
					cache: false,
				}).done(function (data) {
					console.log(data);
					if (data['status'] !== undefined) {
						if (data['status']=='ok') {
							if (data['filename']!='') {
								$('input[name="image_path"]').val( data['filename'] );
							}
							//+++++++++++++++++++++++++++++++++++++
							if (data['url'] !== undefined) {
								product_image_url = data['url'];
							}
							if (data['path'] !== undefined) {
								product_image_path = data['path'];
							}
							//+++++++++++++++++++++++++++++++++++++
							const tempUrl = product_image_url;  
							const tempMatchedFileName = tempUrl.match(/^(?:[^:\/?#]+:)?(?:\/\/[^\/?#]*)?(?:([^?#]*\/)([^\/?#]*))?(\?[^#]*)?(?:#.*)?$/) ?? [];
							const [, dir, tempFileName, query] = tempMatchedFileName.map(match => match ?? '');
							const product_image_filename = "images/" + tempFileName;
							//+++++++++++++++++++++++++++++++++++++
							console.log(get_product_info_config['image_set_selecter']);
							console.log(product_image_filename);
							console.log("test["+$(get_product_info_config['image_set_selecter']).val()+"]");
							product_image_filenames.push(product_image_filename);
							$(get_product_info_config['image_set_selecter']).val(product_image_filenames.join(','));
							console.log("test["+$(get_product_info_config['image_set_selecter']).val()+"]");
							
							if(idx == 0){
								$(get_product_info_config['image_sample_set_selecter']).empty();
							}
							if(tempUrl != ''){
								$(get_product_info_config['image_sample_set_selecter']).append('<div class="sample_image_display_thumbnail" data-url="'+product_image_filename+'"><div class="sample_image_display_thumbnail_delete" data-url="'+product_image_filename+'">Ｘ</div><img class="sample_image_display" src="'+ tempUrl +'"></div>');
							}
							//+++++++++++++++++++++++++++++++++++++
						}
					}
					$upfile.val('');
				}).fail(function() {
					alert("file upload error.");
					$upfile.val('');
				});
			});
		} else {
			
		}
	});


	$(document).on('click', '.input_open_modal_button', function () {
		$('.modal_area').show();
	});


	$(document).on('click', '.modal_area_back', function () {
		$('.modal_area').hide();
	});


	$(document).on('click', '.modal_header_close', function () {
		$('.modal_area').hide();
	});


	$(document).on('click', '.select_tab', function () {
		$('.select_tab').removeClass('selected');
		$(this).addClass('selected');
		var id_name=$(this).attr('id');
		if(id_name=='yahoo_tab'){
			$('#yahoo_area').show();
			$('#mercari_area').hide();
			$('#amazon_area').hide();
			$('#jancode_area').hide();
		}
		if(id_name=='mercari_tab'){
			$('#yahoo_area').hide();
			$('#mercari_area').show();
			$('#amazon_area').hide();
			$('#jancode_area').hide();

		}
		if(id_name=='amazon_tab'){
			$('#yahoo_area').hide();
			$('#mercari_area').hide();
			$('#amazon_area').show();
			$('#jancode_area').hide();
		}
		if(id_name=='jancode_tab'){
			$('#yahoo_area').hide();
			$('#mercari_area').hide();
			$('#amazon_area').hide();
			$('#jancode_area').show();
		}
	});


	$(document).on('click', '#delete_html_tag_action', function () {
		var tempstring = $('#preview_comment_input').val();
		tempstring = tempstring.replace(/<("[^"]*"|'[^']*'|[^'">])*>/g,'');
		console.log(tempstring);
		$('#preview_comment_input').val(tempstring);
	});


	$(document).on('change', 'input[name="preview_image_input"]', function () {
		$('.preview_image_list_unit').removeClass('selected');
		$(this).parents('.preview_image_list_unit').addClass('selected');
	});


	$(document).on('click', '#preview_set_button_input', function () {
		var product_title = $('#preview_title_input').val();
		var product_comment = $('#preview_comment_input').val();

		var select_image_index = $('input:checkbox[name="preview_image_input"]:checked').map(function(){
			$(this).prop('disabled',true);
			return $(this).val();
		}).get();
		
		$('select[name=referer_service]').val($('#temp_referrer_mode').val()).trigger('change');
		$('input[name=referer_id]').val($('#temp_referrer_id').val());
		$('input[name=referer_price]').val($('#preview_product_price').val());
		
		$(get_product_info_config['image_sample_set_selecter']).empty();
		
		let product_image_filenames = [];
		
		$.each(select_image_index,function(idx,image_index){
			var temp_image_url = arr_preview_image_list[image_index]['url'];
			var temp_image_path = arr_preview_image_list[image_index]['path'];

			var product_image_url = arr_preview_image_list[image_index]['url'];
			var product_image_path = arr_preview_image_list[image_index]['path'];
			
			$.ajax({
				type:'POST',
				url:'/api/move_product_image',
				data:{'url':temp_image_url,'path':temp_image_path},
				dataType: 'json',
				async: false,
				success: function(data) {
					if (data['status'] !== undefined) {
						if (data['status']=='ok') {
							if (data['url'] !== undefined) {
								product_image_url = data['url'];
							}
							if (data['path'] !== undefined) {
								product_image_path = data['path'];
							}
							const tempUrl = product_image_url;  
							const tempMatchedFileName = tempUrl.match(/^(?:[^:\/?#]+:)?(?:\/\/[^\/?#]*)?(?:([^?#]*\/)([^\/?#]*))?(\?[^#]*)?(?:#.*)?$/) ?? [];
							const [, dir, tempFileName, query] = tempMatchedFileName.map(match => match ?? '');
							const product_image_filename = "images/" + tempFileName;
							
							$(get_product_info_config['title_set_selecter']).val(product_title);
							
							var editor = CKEDITOR.instances['product_description'];
							var dataForEditor = $('#preview_comment_input').val();
							editor.setData(dataForEditor);
							
							product_image_filenames.push(product_image_filename);
							$(get_product_info_config['image_set_selecter']).val(product_image_filenames.join(','));
							
							// SKUの自動生成
							$.ajax({
								type: 'POST',
								url: '/api/generate_sku',
								data: {
									title: product_title,
									mode: $('#temp_referrer_mode').val(),
									url: $('#temp_referrer_id').val()
								},
								dataType: 'json',
								async: false,
								success: function(response) {
									if (response.status === 'ok') {
										$(get_product_info_config['sku_set_selecter']).val(response.sku);
									} else {
										// エラー時は日時ベースのSKUを生成
										$(get_product_info_config['sku_set_selecter']).val(new Date().toLocaleDateString("ja-JP", {year: "numeric",month: "2-digit",day: "2-digit"}) + '/' + new Date().toLocaleTimeString('ja-JP', {hour12:false}));
									}
								},
								error: function() {
									// エラー時は日時ベースのSKUを生成
									$(get_product_info_config['sku_set_selecter']).val(new Date().toLocaleDateString("ja-JP", {year: "numeric",month: "2-digit",day: "2-digit"}) + '/' + new Date().toLocaleTimeString('ja-JP', {hour12:false}));
								}
							});
							
							if(tempUrl != ''){
								$(get_product_info_config['image_sample_set_selecter']).append('<div class="sample_image_display_thumbnail" data-url="'+product_image_filename+'"><div class="sample_image_display_thumbnail_delete" data-url="'+product_image_filename+'">Ｘ</div><img class="sample_image_display" src="'+ tempUrl +'"></div>');
							}
							
							$('.modal_area').hide();
							$('#preview_area').empty();
						}
					}
				},
				error: function() {
				},
				complete: function() {
				}
			});
		});
	});
});


$(function() {
	console.log("test[" + get_product_info_config['image_set_selecter'] + "]");
	$(get_product_info_config['image_set_selecter']).each(function() {
		var input_elem = $(this);
		var temp_url = $(this).val();
		console.log("test[" + temp_url + "]");
		$(get_product_info_config['image_sample_set_selecter']).empty();
		if(temp_url != ''){
			temp_url = '/uploads/' + temp_url;
			console.log("test[" + temp_url + "]");
			$(get_product_info_config['image_sample_set_selecter']).append('<div class="sample_image_display_thumbnail" data-url="'+temp_url+'"><div class="sample_image_display_thumbnail_delete" data-url="'+temp_url+'">Ｘ</div><img class="sample_image_display" src="'+ temp_url +'"></div>');
		}
	});

	$(document).on('change', get_product_info_config['image_set_selecter'], function () {
		console.log("test[" + get_product_info_config['image_set_selecter'] + "]");
		var input_elem = $(this);
		var temp_url = $(this).val();
		console.log("test[" + temp_url + "]");
		$(get_product_info_config['image_sample_set_selecter']).empty();
		if(temp_url != ''){
			temp_url = '/uploads/' + temp_url;
			console.log("test[" + temp_url + "]");
			$(get_product_info_config['image_sample_set_selecter']).append('<div class="sample_image_display_thumbnail" data-url="'+temp_url+'"><div class="sample_image_display_thumbnail_delete" data-url="'+temp_url+'">Ｘ</div><img class="sample_image_display" src="'+ temp_url +'"></div>');
		}
	});
	
	$(document).on('change', '#check_all_image', function(){
		let r = $('#check_all_image').is(':checked');
		if(r){
			$('input[name=preview_image_input]').prop('checked',true);
		} else {
			$('input[name=preview_image_input]').prop('checked',false);
		}
	});
});


function setDisplayImage(){
	console.log("setDisplayImage");
	$(get_product_info_config['image_set_selecter']).each(function() {
		var input_elem = $(this);
		var temp_url = $(this).val();
		console.log("test[" + temp_url + "]");
		$(get_product_info_config['image_sample_set_selecter']).empty();
		$.each(temp_url.split(','),function(idx,url){
			if(url != ''){
				url = '/uploads/' + url;
				console.log("test[" + url + "]");
				$(get_product_info_config['image_sample_set_selecter']).append('<div class="sample_image_display_thumbnail" data-url="'+url+'"><div class="sample_image_display_thumbnail_delete" data-url="'+url+'">Ｘ</div><img class="sample_image_display" src="'+ url +'"></div>');
			}
		});
	});
}


var htmlToNode = function(htmlStr) {
	if (!htmlStr || typeof htmlStr !== 'string') return;
	var tmpElmt = document.createElement('div');
	tmpElmt.innerHTML = htmlStr;
	return tmpElmt.childNodes;
};


function appendAspects(category_id,token,marketplace_id,condition = 0,default_values = {}){
	let values = default_values;
	$.post('/api/ebay_category_aspects/',{
		id: category_id,
		token: token,
		marketplace_id: marketplace_id,
	}, function(data2) {
		console.log(values);
		console.log(data2);
		if (data2.result == 'success') {
			$('div.aspect').remove();
			$('div.original-aspect').remove();
			
			let condition_policy = data2.data.condition_policies.itemConditionPolicies[0];
			if(condition_policy) {
				let required = condition_policy.itemConditionRequired;
				if(required){
					$('#condition').attr({required:1});
					$('label.condition').addClass('asterisk');
				} else {
					$('#condition').attr({required:0});
					$('label.condition').removeClass('asterisk');
				}
				$('#condition > option').remove();
				
				$.each(condition_policy.itemConditions,function(idx,row){
					let is_append = true;
					if(category_id == 183050 || category_id == 183454 || category_id == 261328){
						if(row.conditionId == 3000) {
							is_append = false;
						}
					}
					if(is_append){
						$('#condition').append($('<option>').attr({value: row.conditionId}).text(row.conditionDescription));
					}
				});
				if(condition != 0){
					$('#condition').val(condition);
				}
				
				$('#condition').select2({
					allowClear: required,
					language: 'ja',
					placeholder: 'Condition',
				});
				
				/* コンディション選択画面 TCG系がやったら特殊(Non-Sport Trading Card Singles,CCG Individual Card,Sports Trading Card Singles)だけは例外的にやらないとだめ */
				if(category_id == 183050 || category_id == 183454 || category_id == 261328){
					$(document).on('change','#condition',function(){
						$('.tcg-condition').hide();
						let selected_condition = $(this).val();
						$.each(condition_policy.itemConditions,function(idx,row){
							if(selected_condition == row.conditionId){
								if(!row.hasOwnProperty('conditionDescriptors')) {
									return;
								}
								$('.tcg-condition').show();
								$('#additional-conditions').empty();
								$.each(row.conditionDescriptors, function(idx2,row2){
									let conditionDescriptorName = row2.conditionDescriptorName;
									let parent = $('<div class="form-group">');
									let label = $('<label class="col-sm-4 control-label">'+conditionDescriptorName+'</label>');
									
									parent.append(label);
									let is_required = false;
									let selection = $('<select name="form_condition_descriptors['+idx2+'][values]" class="col-sm-8">');
									if(row2.conditionDescriptorConstraint.mode == "SELECTION_ONLY"){
										let hidden = $('<input type="hidden" name="form_condition_descriptors['+idx2+'][name]" value="'+row2.conditionDescriptorId+'">');
										$.each(row2.conditionDescriptorValues, function(idx3,row3){
											selection.append($('<option>').attr({value:row3.conditionDescriptorValueId}).text(row3.conditionDescriptorValueName));
										});
										parent.append(hidden);
										
										parent.append(selection);
										$('#additional-conditions').append(parent);
										
										if((row2.conditionDescriptorConstraint).hasOwnProperty('usage') && row2.conditionDescriptorConstraint.usage == "REQUIRED"){
											label.addClass('asterisk');
											selection.attr({'required':1});
											is_required = true;
										} else {
											label.removeClass('asterisk');
											selection.attr({'required':0});
										}
										
										selection.select2({
											allowClear: is_required,
											language: 'ja',
											placeholder: conditionDescriptorName,
										});
									} else {
										let hidden = $('<input type="hidden" name="form_condition_descriptors['+idx2+'][name]" value="'+row2.conditionDescriptorId+'">');
										let input = $('<div class="input-group"><input type="text" name="form_condition_descriptors['+idx2+'][values]" class="form-control col-sm-8"></div>');
										if((row2.conditionDescriptorConstraint).hasOwnProperty('usage') && row2.conditionDescriptorConstraint.usage == "REQUIRED"){
											label.addClass('asterisk');
											input.attr({'required':1});
										} else {
											label.removeClass('asterisk');
											input.attr({'required':0});
										}
										parent.append(hidden);
										parent.append(input);
										
										$('#additional-conditions').append(parent);
									}
								});
							}
						});
					});
					
					if(values){
						$('#condition').trigger('change');
					}
				}
			}
			
			let aspects = data2.data.aspects;
			let dependents = data2.data.dependents;
			
			let aspect_names = [];
			
			let next = $('div.tcg-condition');
			
			$.each(aspects, function(idx,row){
				let name = row.name;
				aspect_names.push(name);
				
				let required = row.required;
				let is_dependent = false;
				if(name in dependents){
					/* 依存する情報があるらしい 例えばゲーム機でBrandをAtariにしたらAtari2600しか表示せんし、MSならXBOX系しか表示しないみたいな */
					is_dependent = true;
				}
				
				let display_name = name;
				
				let default_value = "";
				// デフォルト値あるっぽい
				if(name in values){
					default_value = values[name][0];
				}
				name = name.replace(/ /g, '_');
				
				
				let div = $('<div class="form-group aspect">');
				let label = $('<label for="'+name+'" class="col-sm-2 control-label">'+display_name+'</label>');
				let inner = $('<div class="input-group">');
				let inner2 = $('<div class="col-sm-8">');
				let input, list, marker;
				
				if(row.mode == 'FREE_TEXT' && row.itemToAspectCardinality == 'SINGLE'){
					/* テキスト入力+aspectValuesがあったらdatalistで。選べるのは一つだけ */
					if(required && !default_value){
						default_value = 'N/A';
					}
					marker = $('<span class="input-group-addon"><i class="fa fa-pencil fa-fw"></i></span>');
					if(row.aspectValues.length > 0){
						input = $('<input id="'+name+'" class="form-control '+name+'" name="form_aspects['+name+'][]" list="list-'+name+'" value="'+default_value+'">');
						list = $('<span class="input-group-addon clearfix">');
						let list2 = $('<datalist id="list-'+name+'">');
						$.each(row.aspectValues,function(idx2,row2){
							list2.append($('<option>').attr({value:row2}).text(row2));
						});
						list.append(list2);
						
						//if(is_dependent){
						//	// リスト部分を再構成する用
						//	$(input).on('change',function(){
						//		let val = $(this).val();
						//		let dependent_list = dependents[name];
						//		// 入力値が除外リストにあったら
						//		if(val in dependent_list){
						//			let targets = dependent_list[val];
						//			$.each(targets, function(idx,row3){
						//				let target = $('#'+idx);
						//				let options;
						//				if(target.is('input')){
						//					options = $('#list-'+name+' > option');
						//				} else {
						//					options = $('#'+name+' > option');
						//				}
						//				options.empty();
						//				$.each(dependent_list[val][idx], function(row4){
						//					options.append($('<option>').attr({value:row4}).text(row4));
						//				});
						//			});
						//		} else {
						//			let reset_list = [];
						//			$.each(dependent_list, function(idx, row3){
						//				for(key in row3){
						//					reset_list.push(key);
						//				}
						//			});
						//			
						//			$.each(reset_list, function(idx){
						//				let target = $('#'+idx);
						//				let options;
						//				if(target.is('input')){
						//					$('#list-'+name+' > option').empty();
						//					options = $('#list-'+name+'');
						//				} else {
						//					$('#'+name+' > option').empty();
						//					options = $('#'+name).empty();
						//				}
						//				options.empty();
						//				$.each(aspects, function(row4){
						//					if(row4.name == idx){
						//						$.each(row4.aspectValues, function(row5){
						//							options.append($('<option>').attr({value:row5}).text(row5));
						//						});
						//					}
						//				});
						//			});
						//		}
						//	});
						//}
					} else {
						input = $('<input id="'+name+'" class="form-control '+name+'" name="form_aspects['+name+'][]" value="'+default_value+'">');
					}
					inner.append(marker);
					inner.append(input);
					inner.append(list);
				} else if(row.mode == 'FREE_TEXT' && row.itemToAspectCardinality == 'MULTI'){
					if(required && !default_value){
						default_value = 'N/A';
					}
					/* 複数選べるから追加･削除の挙動が必要 */
					marker = $('<span class="input-group-addon"><i class="fa fa-pencil fa-fw"></i></span>');
					if(row.aspectValues.length > 0){
						input = $('<input id="'+name+'" class="form-control '+name+'" name="form_aspects['+name+'][]" list="list-'+name+'" value="'+default_value+'">');
						list = $('<span class="input-group-addon clearfix">');
						let list2 = $('<datalist id="list-'+name+'">');
						$.each(row.aspectValues,function(idx2,row2){
							list2.append($('<option>').attr({value:row2}).text(row2));
						});
						list.append(list2);
						
						//if(is_dependent){
						//	// リスト部分を再構成する用
						//	$(input).on('change',function(){
						//		let dependent_list = dependents[name];
						//		let target = $('#'+name);
						//		let options;
						//		if(target.is('input')){
						//			options = $('#list-'+name+' > option');
						//		} else {
						//			options = $('#'+name+' > option');
						//		}
						//		options.empty();
						//		
						//		if($(this).val() in dependent_list){
						//			$.each(dependents[name][$(this).val()], function(row3){
						//				options.append($('<option>').attr({value:row3}).text(row3));
						//			});
						//		} else {
						//			options.empty();
						//			$.each(row.aspectValues,function(idx2,row2){
						//				options.append($('<option>').attr({value:row2}).text(row2));
						//			});
						//		}
						//	});
						//}
					} else {
						input = $('<input id="'+name+'" class="form-control '+name+'" name="form_aspects['+name+'][]" value="'+default_value+'">');
					}
					inner.append(marker);
					inner.append(input);
					inner.append(list);
				} else if(row.mode == 'SELECTION_ONLY' && row.itemToAspectCardinality == 'SINGLE'){
					/* SELECTION_ONLY はセレクトボックス */
					if(row.aspectValues.length > 0){
						input = $('<select id="'+name+'" class="form-control '+name+'" name="form_aspects['+name+'][]">');
						input.append($('<option>').attr({value:''}).text(''));
						$.each(row.aspectValues,function(idx2,row2){
							input.append($('<option>').attr({value:row2}).text(row2));
						});
						
						//if(is_dependent){
						//	// リスト部分を再構成する用
						//	$(input).on('change',function(){
						//		let dependent_list = dependents[name];
						//		let target = $('#'+name);
						//		let options;
						//		if(target.is('input')){
						//			options = $('#list-'+name+' > option');
						//		} else {
						//			options = $('#'+name+' > option');
						//		}
						//		options.empty();
						//		
						//		if($(this).val() in dependent_list){
						//			$.each(dependents[name][$(this).val()], function(row3){
						//				options.append($('<option>').attr({value:row3}).text(row3));
						//			});
						//		} else {
						//			options.empty();
						//			$.each(row.aspectValues,function(idx2,row2){
						//				options.append($('<option>').attr({value:row2}).text(row2));
						//			});
						//		}
						//	});
						//}
						if(input.find('option[value="'+default_value+'"]').length > 0){
							input.find('option[value="'+default_value+'"]').prop('selected', true);
						}
						
					}
					inner.append(input);
				} else if(row.mode == 'SELECTION_ONLY' && row.itemToAspectCardinality == 'MULTI'){
					/* マルチセレクトで */
					if(row.aspectValues.length > 0){
						input = $('<select id="'+name+'" class="form-control '+name+'" name="form_aspects['+name+'][]">');
						input.append($('<option>').attr({value:''}).text(''));
						$.each(row.aspectValues,function(idx2,row2){
							input.append($('<option>').attr({value:row2}).text(row2));
						});
						
						//if(is_dependent){
						//	// リスト部分を再構成する用
						//	$(input).on('change',function(){
						//		let dependent_list = dependents[name];
						//		let target = $('#'+name);
						//		let options;
						//		if(target.is('input')){
						//			options = $('#list-'+name+' > option');
						//		} else {
						//			options = $('#'+name+' > option');
						//		}
						//		options.empty();
						//		
						//		if($(this).val() in dependent_list){
						//			$.each(dependents[name][$(this).val()], function(row3){
						//				options.append($('<option>').attr({value:row3}).text(row3));
						//			});
						//		} else {
						//			options.empty();
						//			$.each(row.aspectValues,function(idx2,row2){
						//				options.append($('<option>').attr({value:row2}).text(row2));
						//			});
						//		}
						//	});
						//}
						
						if(input.find('option[value="'+default_value+'"]').length > 0){
							input.find('option[value="'+default_value+'"]').prop('selected', true);
						}
					}
					inner.append(input);
				}
				
				if(required){
					label.addClass('asterisk');
					input.attr({required: 1});
				}
				inner2.append(inner);
				div.append(label);
				div.append(inner2);
				next.after(div);
				next = div;
				if(row.mode == 'SELECTION_ONLY'){
					input.select2({
						allowClear: true,
						language: 'ja',
						placeholder: row.name,
					});
				}
			});
			
			
			let addArea = $('<div class="form-group original-aspect">');
			addArea.append($('<button type="button" class="btn" onclick="addOriginalAspects();">追加</button>'));
			
			next.after(addArea);
			nextOriginalAsspects = addArea;
			
			// オリジナル要素が存在するかチェック
			$.each(values, function(idx,row){
				if(aspect_names.indexOf(idx) == -1){
					nextOriginalAsspects = addOriginalAspects(idx,row[0]);
				}
			});
		} else {
			alert('eBayからの情報取得に失敗しました。\n' + data2.data.errors[0].message);
		}
	});
}
var nextOriginalAsspects;
var originalAsspectIndex = 0;

function addOriginalAspects(key="",value=""){
	let originalDiv = $('<div class="form-group original-aspect">');
	let originalKey = $('<input type="text" class="col-sm-2 form-control" name="original_aspect_keys['+originalAsspectIndex+']" value="'+key+'" style="width:25%;">');
	let originalInner = $('<div class="input-group col-sm-8">');
	let originalInner2 = $('<div class="col-sm-10">');
	let originalValues = $('<input type="text" class="form-control" name="original_aspect_values['+originalAsspectIndex+']" list="" value="'+value+'" style="width:60%;">');
	let originalDelete = $('<button type="button" class="btn col-sm-2" style="margin-left:1em;">削除</button>');
	
	originalInner2.append(originalValues);
	originalInner2.append(originalDelete);
	originalInner.append(originalInner2);
	originalInner.append(originalInner2);
	originalDiv.append(originalKey);
	originalDiv.append(originalInner);
	
	nextOriginalAsspects.after(originalDiv);
	
	originalDelete.on("click",function(){
		originalDiv.remove();
		nextOriginalAsspects = $('.original-aspect').last();
	});
	
	originalAsspectIndex++;
	
	return originalDiv;
}

var ebay_token = '';
function refreshUserToken(){
    let csrf = $('meta[name="csrf-token"]').attr("content");
    
	return new Promise((resolve, reject) => {
        $.ajax({
            type: 'POST',
            url: '/admin/auth/refresh_token',
            headers: {
                'X-CSRF-TOKEN': csrf,
            },
            async: false,
            success: function(data) {
                ebay_token = data.token;
                resolve(ebay_token);
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error(xhr.responseText);
                reject(new Error('Failed to refresh user token.'));
            }
        });
    });
}
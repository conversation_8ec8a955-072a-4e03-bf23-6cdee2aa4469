<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\StripeWebhookController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/test', 'App\Http\Controllers\TestController@index');
Route::get('/test/checkprogram', 'App\Http\Controllers\TestController@checkProgram');
Route::post('/test/checkprogram', 'App\Http\Controllers\TestController@checkProgram');

Route::group(['prefix' => 'contact'], function () {
    Route::get('/', 'App\Http\Controllers\ContactController@index')->name('contact.index');
    Route::post('/confirm', 'App\Http\Controllers\ContactController@confirm')->name('contact.confirm');
    Route::post('/thanks', 'App\Http\Controllers\ContactController@send')->name('contact.send');
});

Route::group(['prefix' => 'admin'], function () {
    // Laravel-admin側にリダイレクト
    Route::redirect('/login', '/admin/auth/login')->name('login');

    Route::get('/register', 'App\Http\Controllers\AdminAuth\RegisterController@showRegistrationForm')->name('register');
    Route::post('/register', 'App\Http\Controllers\AdminAuth\RegisterController@register');
    Route::get('/sendmail', 'App\Http\Controllers\AdminAuth\RegisterController@showSendedMail');

    Route::get('/email-verification/error', 'App\Http\Controllers\AdminAuth\RegisterController@getVerificationError')->name('email-verification.error');
    Route::get('/email-verification/check/{token}', 'App\Http\Controllers\AdminAuth\RegisterController@getVerification')->name('email-verification.check');

    // 認証関連
    Route::get('/auth/success', 'App\Admin\Controllers\AuthController@oauthRedirectSuccess');
    Route::get('/auth/success-legacy', 'App\Admin\Controllers\AuthController@oauthRedirectSuccessLegacy');
    Route::get('/auth/failed', 'App\Admin\Controllers\AuthController@oauthRedirectFailed');
    Route::get('/auth/checkprogram', 'App\Admin\Controllers\AuthController@checkProgram');
    Route::get('/auth/user-info', [App\Admin\Controllers\EbayController::class, 'getUserInfo']);

    // Stripe関連ルート（GETとPOST両方に対応）
    Route::match(['get', 'post'], '/stripe/create-checkout-session', [StripeWebhookController::class, 'createCheckoutSession'])->name('admin.stripe.checkout');
    Route::get('/stripe/trial-status', [StripeWebhookController::class, 'getTrialStatus']);

    // eBay関連ルート（強化版）
    Route::post('/ebay/pre-check', [App\Admin\Controllers\EbayController::class, 'preCheckEbayAccount']);
    Route::post('/ebay/confirm-connection', [App\Admin\Controllers\EbayController::class, 'confirmEbayConnection']);
    Route::post('/ebay/trial-check', [App\Admin\Controllers\EbayController::class, 'checkTrialImpact']);
    Route::post('/ebay/trial-impact-status', [App\Admin\Controllers\EbayController::class, 'getTrialImpactStatus']);
    Route::post('/ebay/disconnect', [App\Admin\Controllers\EbayController::class, 'disconnect']);
});

Route::group(['prefix' => 'users'], function () {
    Route::get('/register', 'App\Http\Controllers\AdminAuth\RegisterController@showRegistrationForm')->name('user.view.register');
    Route::post('/register', 'App\Http\Controllers\AdminAuth\RegisterController@register')->name('user.post.register');
    Route::get('/sendmail', 'App\Http\Controllers\AdminAuth\RegisterController@showSendedMail')->name('user.view.sendmail');
});

//決済結果通知受取用
Route::get('/settlement/result', [App\Http\Controllers\SettlementController::class, 'result'])->name('settlement.result');
Route::get('/settlement/result_auto', [App\Http\Controllers\SettlementController::class, 'result_auto'])->name('settlement.result_auto');

Route::match(['get', 'post'], "/about",       "App\Http\Controllers\PageController@about")->name('about');
Route::match(['get', 'post'], "/company",       "App\Http\Controllers\PageController@company")->name('company');
Route::match(['get', 'post'], "/faq",       "App\Http\Controllers\PageController@faq")->name('faq');
Route::match(['get', 'post'], "/sitemap",       "App\Http\Controllers\PageController@sitemap")->name('sitemap');
Route::match(['get', 'post'], "/terms_of_service",       "App\Http\Controllers\PageController@terms_of_service")->name('terms_of_service');
Route::match(['get', 'post'], "/privacy_policy",       "App\Http\Controllers\PageController@privacy_policy")->name('privacy_policy');
Route::match(['get', 'post'], "/commercial_transaction",       "App\Http\Controllers\PageController@commercial_transaction")->name('commercial_transaction');

// Public Stripe関連ルート
Route::get('stripe/checkout', [StripeWebhookController::class, 'createCheckoutSession'])->name('stripe.checkout');
Route::post('stripe/webhook', [StripeWebhookController::class, 'handle']);
Route::get('stripe/success', [StripeWebhookController::class, 'success'])->name('stripe.success');
Route::get('stripe/cancel', [StripeWebhookController::class, 'cancel'])->name('stripe.cancel');

// OAuth スコープチェック用のルート
Route::post('/admin/exhibits/check-oauth-scopes', [ExhibitController::class, 'checkOAuthScopes']);
// routes/web.php
Route::post('/admin/exhibits/test-saved-tokens', [ExhibitController::class, 'testSavedTokens']);

// ハイブリッドAPI関連ルート
Route::post('/admin/exhibits/test-hybrid-api', [App\Admin\Controllers\ExhibitController::class, 'testHybridApi']);
Route::post('/admin/exhibits/api-status', [App\Admin\Controllers\ExhibitController::class, 'getApiStatus']);

<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ApiController;

use App\Admin\Controllers\ExhibitController;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::match(['get', 'post'], '/get_product_info', [ApiController::class, 'getProductInfo'])->name('get_product_info.post');
Route::match(['get', 'post'], '/move_product_image', [ApiController::class, 'moveProductImage'])->name('move_product_image.post');
Route::match(['get', 'post'], '/file_upload', [ApiController::class, 'fileUpload'])->name('file_upload.post');

Route::match(['get', 'post'], '/translation', [ApiController::class, 'getTranslation'])->name('get_translation.post');
Route::match(['get', 'post'], '/mercari_product', [ApiController::class, 'getMercariProduct'])->name('get_mercari_product.post');
Route::match(['get', 'post'], '/description_template_content/{id}', [ApiController::class, 'getDescriptionTemplateContent'])->name('get_description_template.post');
Route::match(['get', 'post'], '/ebay_description', [ApiController::class, 'getEBayDescription'])->name('get_ebay_description.post');
Route::match(['get', 'post'], '/ebay_item', [ExhibitController::class, 'getEbayItem'])->name('get_ebay_item.post');
Route::match(['get', 'post'], '/ebay_category_aspects', [ExhibitController::class, 'getCategoryAspect'])->name('get_category_aspects.post');
Route::match(['get', 'post'], '/categories', [ApiController::class, 'getEBayCategories'])->name('get_categories.post');

Route::post('/generate_sku', 'Api\ApiController@generateSKU');

Route::post('/getProductInfo', [ApiController::class, 'getProductInfo']);
Route::post('/get-yahoo-shopping-product', [ApiController::class, 'getYahooShoppingProduct']);
Route::post('/debug-yahoo-images', [ApiController::class, 'debugYahooImages']);


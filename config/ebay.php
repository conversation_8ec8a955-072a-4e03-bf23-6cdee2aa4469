<?php

return [
	'client_id' => env('EBAY_CLIENT_ID'),
	'client_secret' => env('EBAY_CLIENT_SECRET'),
	'dev_name' => env('EBAY_DEV_NAME'),
	'ru_name' => env('EBAY_RU_NAME'),
	'api_endpoint' => env('EBAY_API_ENDPOINT'),
	'auth_endpoint'=> env('EBAY_AUTH_ENDPOINT'),
	'apiz_endpoint'=> env('EBAY_APIZ_ENDPOINT'),
	'traditional' => [
		'api' => env('EBAY_TRADITIONAL_API_ENDPOINT'),
		'auth' => env('EBAY_TRADITIONAL_AUTH_ENDPOINT'),
		'level' => env('EBAY_TRADITIONAL_LEVEL'),
	],
	// Trading API用の設定を追加
	'dev_id' => env('EBAY_DEV_NAME'),
	'app_id' => env('EBAY_CLIENT_ID'),
	'cert_id' => env('EBAY_CLIENT_SECRET'),
];



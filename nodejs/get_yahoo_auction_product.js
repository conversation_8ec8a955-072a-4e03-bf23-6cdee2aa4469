const puppeteer = require('puppeteer');

function log(level, message, data = {}) {
    console.error(JSON.stringify({
        timestamp: new Date().toISOString(),
        level,
        message,
        ...data
    }));
}

(async () => {
    let productData = {
        product_title: '',
        product_comment: '',
        price: '',
        images: [],
        state: 0
    };

    if (process.argv.length < 3) {
        log('error', 'No URL provided');
        console.log(JSON.stringify(productData));
        return;
    }

    const target = process.argv[2];
    log('info', 'Starting browser for Yahoo Auction', {
        url: target
    });

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    const page = await browser.newPage();

    // User-Agentを設定してブロックを回避
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

    try {
        log('info', 'Navigating to page', {
            url: target
        });
        await page.goto(target, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        log('info', 'Waiting for page to load');
        await page.waitForTimeout(3000);

        log('info', 'Starting page evaluation');
        productData = await page.evaluate(() => {
            // 商品タイトル取得
            const titleElement = document.querySelector('#itemTitle');

            // 商品説明取得
            const descriptionElement = document.querySelector('#description');

            // 価格取得（現在価格）
            let priceElement = document.querySelector('.Price__value, .u-textRed, .Price');
            if (!priceElement) {
                // 他の価格セレクターを試す
                const priceElements = document.querySelectorAll('[class*="price"], [class*="Price"], .u-textRed');
                priceElement = Array.from(priceElements).find(el =>
                    el.textContent.includes('円') || /\d{1,3}(,\d{3})*/.test(el.textContent)
                );
            }

            // 画像取得
            let images = [];

            // メイン画像 (sc-1f0603b0-2 kxUAXU クラス)
            const mainImages = document.querySelectorAll('.sc-1f0603b0-2.kxUAXU img');
            mainImages.forEach(img => {
                const src = img.getAttribute('src') || img.getAttribute('data-src');
                if (src && !images.includes(src)) {
                    images.push(src);
                }
            });

            // slick-track内の画像
            const slickTrackImages = document.querySelectorAll('.slick-track img');
            slickTrackImages.forEach(img => {
                const src = img.getAttribute('src') || img.getAttribute('data-src');
                if (src && !images.includes(src)) {
                    images.push(src);
                }
            });

            // その他の商品画像も取得
            const otherImages = document.querySelectorAll('.ProductImage img, [class*="image"] img, [class*="Image"] img');
            otherImages.forEach(img => {
                const src = img.getAttribute('src') || img.getAttribute('data-src');
                if (src && !images.includes(src) && src.includes('https')) {
                    images.push(src);
                }
            });

            // 相対URLを絶対URLに変換
            images = images.map(src => {
                if (src.startsWith('//')) {
                    return 'https:' + src;
                } else if (src.startsWith('/')) {
                    return 'https://auctions.yahoo.co.jp' + src;
                }
                return src;
            }).filter(src => src.includes('https')); // HTTPSのURLのみ

            // 価格から数字のみ抽出
            let priceText = '';
            if (priceElement) {
                priceText = priceElement.textContent.replace(/[^\d]/g, '');
            }

            // 成功判定
            const state = (titleElement && titleElement.textContent.trim()) ? 1 : 0;

            return {
                product_title: titleElement ? titleElement.textContent.trim() : '',
                product_comment: descriptionElement ? descriptionElement.textContent.trim() : '',
                price: priceText,
                images: images,
                state: state
            };
        });

        log('info', 'Page evaluation completed', {
            has_title: !!productData.product_title,
            has_description: !!productData.product_comment,
            has_price: !!productData.price,
            image_count: productData.images.length,
            state: productData.state
        });

        // デバッグ用：取得したデータの詳細をログ出力
        log('debug', 'Scraped data details', {
            title_length: productData.product_title.length,
            description_length: productData.product_comment.length,
            price: productData.price,
            images: productData.images
        });

    } catch (e) {
        log('error', 'Error during scraping', {
            error: e.message,
            stack: e.stack
        });
    } finally {
        log('info', 'Closing browser');
        await browser.close();
    }

    console.log(JSON.stringify(productData));
})();

const puppeteer = require('puppeteer');

(async () => {
    let productData = {
        product_title: '',
        product_comment: '',
        price: 0,
        images: [],
        state: 0,
        asin: '',
        brand: '',
        category: ''
    };

    if (process.argv.length < 3) {
        console.log(JSON.stringify(productData));
        return;
    }

    const target = process.argv[2];
    const browser = await puppeteer.launch({
        defaultViewport: {
            width: 1440,
            height: 900,
        },
        headless: 'new',
        timeout: 30000,
    });

    const page = await browser.newPage();

    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
    await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Accept-Language': 'ja',
        'Content-Type': 'text/html;charset=UTF-8',
    });

    let html = '';

    try {
        await page.goto(target, { waitUntil: 'networkidle2' });
        html = await page.content();

        // ASINの抽出
        const asinMatch = target.match(/\/dp\/([A-Z0-9]{10})/);
        const asin = asinMatch ? asinMatch[1] : '';

        await page.waitForSelector('#productTitle', { timeout: 10000 });

        productData = await page.evaluate(() => {
            const titleElement = document.querySelector('#productTitle');
            const priceWhole = document.querySelector('.a-price-whole');
            const priceFraction = document.querySelector('.a-price-fraction');
            const descriptionElement = document.querySelector('#productDescription');
            const brandElement = document.querySelector('#bylineInfo');
            const categoryElement = document.querySelector('#wayfinding-breadcrumbs_feature_div');

            const product_title = titleElement ? titleElement.textContent.trim() : '';
            const price = priceWhole && priceFraction
                ? `${priceWhole.textContent.trim()}.${priceFraction.textContent.trim()}`
                : priceWhole ? priceWhole.textContent.trim() : '';

            const product_comment = descriptionElement ? descriptionElement.textContent.trim() : '';
            const brand = brandElement ? brandElement.textContent.trim().replace('ブランド: ', '') : '';
            const category = categoryElement ? categoryElement.textContent.trim() : '';

            const imageThumbs = document.querySelectorAll('#altImages .a-spacing-small.item.imageThumbnail.a-declarative img');
            const images = Array.from(imageThumbs)
                .map(img => img.getAttribute('src') || img.getAttribute('data-src'))
                .filter(src => src && src.startsWith('http'))
                .map(src => src.replace(/_US\d+_\.jpg/, '_UL1500_.jpg')); // 高画質変換

            const state = product_title && price ? 1 : 0;

            return {
                product_title,
                product_comment,
                price,
                images,
                state,
                brand,
                category
            };
        });

        productData.asin = asin;

    } catch (e) {
        console.error('Error:', e.message);
    } finally {
        await browser.close();
    }

    if (!productData.product_title && !productData.product_comment && !productData.price) {
        productData = {
            product_title: '',
            product_comment: html,
            price: 0,
            images: [],
            state: 0,
            asin: '',
            brand: '',
            category: ''
        };
    }

    console.log(JSON.stringify(productData));
})();

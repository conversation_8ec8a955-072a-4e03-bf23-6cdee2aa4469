const puppeteer = require('puppeteer');

function log(level, message, data = {}) {
    console.error(JSON.stringify({
        timestamp: new Date().toISOString(),
        level,
        message,
        ...data
    }));
}

(async () => {
    let productData = {
        product_title: '',
        product_comment: '',
        price: '',
        images: [],
        state: 0
    };

    if (process.argv.length < 2) {
        log('error', 'No URL provided');
        console.log(JSON.stringify(productData));
        return;
    }

    const target = process.argv[2];
    log('info', 'Starting browser for Yahoo Shopping', { url: target });
    
    const browser = await puppeteer.launch({ headless: 'new' });
    const page = await browser.newPage();
    
    try {
        log('info', 'Navigating to page', { url: target });
        await page.goto(target, { waitUntil: 'networkidle2', timeout: 20000 });
        
        log('info', 'Waiting for body element');
        await page.waitForSelector('body', { timeout: 10000 });
        
        log('info', 'Starting page evaluation');
        productData = await page.evaluate(() => {
            const titleElement = document.querySelector('.elName, .ProductTitle__title, h1');
            const priceElement = document.querySelector('.elPriceNumber, .Price__value');
            const descriptionElement = document.querySelector('.elDescription, .ProductDescription__body, .ProductExplanation__commentBody');
            
            let images = [];
            const mainImage = document.querySelector('.elMain img, .ProductImage__image');
            if (mainImage) {
                images.push(mainImage.getAttribute('src'));
            }
            
            document.querySelectorAll('.elThumbnail img, .ProductImage__thumbnail img').forEach(img => {
                const src = img.getAttribute('src');
                if (src && !images.includes(src)) images.push(src);
            });
            
            const state = (titleElement && priceElement) ? 1 : 0;
            
            return {
                product_title: titleElement ? titleElement.textContent.trim() : '',
                product_comment: descriptionElement ? descriptionElement.textContent.trim() : '',
                price: priceElement ? priceElement.textContent.replace(/[^\d]/g, '') : '',
                images,
                state
            };
        });
        
        log('info', 'Page evaluation completed', {
            has_title: !!productData.product_title,
            has_price: !!productData.price,
            image_count: productData.images.length,
            state: productData.state
        });
        
    } catch (e) {
        log('error', 'Error during scraping', {
            error: e.message,
            stack: e.stack
        });
    } finally {
        log('info', 'Closing browser');
        await browser.close();
    }
    
    console.log(JSON.stringify(productData));
})();



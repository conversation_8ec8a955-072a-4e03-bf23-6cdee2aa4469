const puppeteer = require('puppeteer');

(async () => {
	var productData = {
		product_title: '',
		product_comment: '',
		price: '',
		images: [],
		state: 0
	};

	if (process.argv.length < 3) {
		console.log(JSON.stringify(productData));
		return;
	}

	const target = process.argv[2];
	const browser = await puppeteer.launch({ headless: "new" });
	const page = await browser.newPage();

	try {
		await page.goto(target, { waitUntil: 'networkidle2', timeout: 20000 });
		await page.waitForSelector('div.merButton', { timeout: 10000 });

		productData = await page.evaluate(() => {
			const titleElement = document.querySelector('h1');
			const priceElement = document.querySelector('div[data-testid=price] > span:last-child');
			const descriptionElement = document.querySelector('pre[data-testid=description]');
			const state = ((document.querySelector('div[data-testid="checkout-button"]'))?.getAttribute('name') === 'purchase') ? 1 : 0;
			let images = [];

			// slick-list配下の画像を優先的に取得
			const slickList = document.querySelector('.slick-list');
			if (slickList) {
				const imageNodes = slickList.querySelectorAll('img');
				images = Array.from(imageNodes).map(img => {
					if (img.getAttribute('srcset')) {
						return img.getAttribute('srcset').split(' ')[0];
					}
					return img.getAttribute('data-src') || img.getAttribute('src') || '';
				}).filter(src => src && src.startsWith('http'));
			}

			// slick-listで画像が取得できない場合の代替手段
			if (images.length === 0) {
				const section = document.querySelector('section.sc-d4b82f4-7');
				if (section) {
					const imageNodes = section.querySelectorAll('img');
					images = Array.from(imageNodes).map(img => {
						if (img.getAttribute('srcset')) {
							return img.getAttribute('srcset').split(' ')[0];
						}
						return img.getAttribute('data-src') || img.getAttribute('src') || '';
					}).filter(src => src && src.startsWith('http'));
				}
			}

			// 更なる代替手段：より一般的なセレクタで画像を探す
			if (images.length === 0) {
				const allImageNodes = document.querySelectorAll('img[src*="mercari"], img[data-src*="mercari"], img[srcset*="mercari"]');
				images = Array.from(allImageNodes).map(img => {
					if (img.getAttribute('srcset')) {
						return img.getAttribute('srcset').split(' ')[0];
					}
					return img.getAttribute('data-src') || img.getAttribute('src') || '';
				}).filter(src => src && src.startsWith('http'));
			}

			return {
				product_title: titleElement?.textContent.trim() || '',
				product_comment: descriptionElement?.textContent.trim() || '',
				price: priceElement?.textContent.trim() || '',
				images,
				state
			};
		});
	} catch (e) {
	} finally {
		await browser.close();
	}

	console.log(JSON.stringify(productData));
	process.exit(0);
})();


{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "aws/aws-sdk-php": "^3.263", "encore/laravel-admin": "^1.8", "guzzlehttp/guzzle": "^7.2", "jrean/laravel-user-verification": "dev-master", "kub-at/php-simple-html-dom-parser": "^1.9", "laravel-admin-ext/ckeditor": "^1.0", "laravel/framework": "^10.0", "laravel/helpers": "^1.6", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "laravel/ui": "^4.2", "stripe/stripe-php": "^17.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "orangehill/iseed": "^2.3", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}
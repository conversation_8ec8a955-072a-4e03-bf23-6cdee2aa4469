<?php
echo "=== Web Scraper Debug Test ===\n";

$url = 'https://jp.mercari.com/item/m52411109247';

// Test 1: Check if node is accessible from PHP
echo "1. Testing node access from PHP:\n";
$cmd1 = 'which node 2>&1';
$output1 = shell_exec($cmd1);
echo "which node: " . ($output1 ?: 'NULL') . "\n";

// Test 2: Check node version from PHP
echo "2. Testing node version from PHP:\n";
$cmd2 = 'node --version 2>&1';
$output2 = shell_exec($cmd2);
echo "node --version: " . ($output2 ?: 'NULL') . "\n";

// Test 3: Test working directory
echo "3. Testing working directory:\n";
$cmd3 = 'pwd 2>&1';
$output3 = shell_exec($cmd3);
echo "pwd: " . ($output3 ?: 'NULL') . "\n";

// Test 4: Test the new command format
echo "4. Testing new scraper command:\n";
$cmd4 = 'cd /srv/app && node nodejs/get_mercari_product.js ' . escapeshellarg($url) . ' 2>&1';
echo "Command: $cmd4\n";
$output4 = shell_exec($cmd4);
echo "Output: " . ($output4 ?: 'NULL') . "\n";

// Test 5: Test if we can access the script file
echo "5. Testing script file access:\n";
$cmd5 = 'ls -la /srv/app/nodejs/get_mercari_product.js 2>&1';
$output5 = shell_exec($cmd5);
echo "ls -la script: " . ($output5 ?: 'NULL') . "\n";

// Test 6: Test node_modules
echo "6. Testing node_modules:\n";
$cmd6 = 'ls -la /srv/app/node_modules/puppeteer 2>&1';
$output6 = shell_exec($cmd6);
echo "puppeteer module: " . ($output6 ?: 'NULL') . "\n";

echo "\n=== Test Complete ===\n";
?>

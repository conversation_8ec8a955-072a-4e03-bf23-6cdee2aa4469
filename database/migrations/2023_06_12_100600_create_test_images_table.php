<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('test_images', function (Blueprint $table) {
            $table->bigInteger('id',true)->unsigned()->nullable(false);
            $table->bigInteger('tests_id')->nullable(false);
            $table->string('image_url', 255)->nullable(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('test_images');
    }
};

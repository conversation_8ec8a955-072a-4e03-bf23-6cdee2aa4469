<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // ebay_usage_history テーブル作成
        if (!Schema::hasTable('ebay_usage_history')) {
            Schema::create('ebay_usage_history', function (Blueprint $table) {
                $table->id();
                $table->string('ebay_id')->index();
                $table->string('ebay_username')->nullable();
                $table->unsignedBigInteger('user_id');
                $table->timestamp('first_connected_at');
                $table->timestamp('last_connected_at')->nullable();
                $table->boolean('trial_cancelled')->default(false);
                $table->timestamp('trial_cancelled_at')->nullable();
                $table->timestamps();

                $table->foreign('user_id')->references('id')->on('admin_users')->onDelete('cascade');
                $table->unique(['ebay_id', 'user_id'], 'ebay_user_unique');
                $table->index(['ebay_id', 'trial_cancelled']);
            });
        }

        // カラム追加（事前に取得してから処理）
        $columns = Schema::getColumnListing('admin_users');

        Schema::table('admin_users', function (Blueprint $table) use ($columns) {
            if (!in_array('trial_started_at', $columns)) {
                $table->timestamp('trial_started_at')->nullable()->after('stripe_customer_id');
            }
            if (!in_array('trial_ends_at', $columns)) {
                $table->timestamp('trial_ends_at')->nullable()->after('trial_started_at');
            }
            if (!in_array('has_used_trial', $columns)) {
                $table->boolean('has_used_trial')->default(false)->after('trial_ends_at');
            }
            if (!in_array('subscription_id', $columns)) {
                $table->string('subscription_id')->nullable()->after('has_used_trial');
            }
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admin_users', function (Blueprint $table) {
            $table->dropColumn(['trial_started_at', 'trial_ends_at', 'has_used_trial', 'subscription_id']);
        });

        Schema::dropIfExists('ebay_usage_history');
    }
};
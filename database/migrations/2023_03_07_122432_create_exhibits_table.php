<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exhibits', function (Blueprint $table) {
            $table->id();
			$table->string('title');
			$table->string('sku')->comment('SKU(管理番号)');
			$table->unsignedInteger('stock_quantity')->default(0)->comment('在庫数');
			$table->unsignedInteger('price')->default(0)->comment('価格');
			$table->string('product_image')->default(null)->comment('商品画像');
			$table->unsignedTinyInteger('is_scale')->default(0)->comment('拡大許可');
			$table->string('color',10)->comment('色');
			$table->string('ca_prop_65')->comment('カリフォルニア州プロポジション65');
			$table->unsignedTinyInteger('is_lot_sales')->default(0)->comment('ロット販売');
			$table->unsignedTinyInteger('product_type')->default(0)->comment('商品種別');
			$table->unsignedInteger('production_area')->default(0)->comment('製造地域');
			$table->dateTime('origin_date')->default(null)->comment('原産日');
			$table->text('product_description')->comment('商品説明');
			
			$table->unsignedTinyInteger('fixed_format')->default(0)->comment('フォーマット');
			$table->datetime('fixed_listing_end_at')->comment('出品期間');
			$table->datetime('fixed_listing_start_at')->comment('出品開始日');
			$table->unsignedInteger('fixed_buyout_price')->comment('即決価格');
			$table->unsignedTinyInteger('is_fixed_negotiable_price')->default(0)->comment('価格交渉可能か');
			$table->text('fixed_negotiable_price_message')->comment('価格交渉についてメッセージ');
			$table->unsignedInteger('fixed_quantity')->default(0)->comment('数量');
			
			$table->unsignedTinyInteger('auction_format')->default(0)->comment('フォーマット');
			$table->datetime('auction_listing_end_at')->comment('出品期間');
			$table->datetime('auction_listing_start_at')->comment('出品開始日');
			$table->unsignedInteger('auction_start_price')->default(0)->comment('開始価格');
			$table->unsignedInteger('auction_buyout_price')->default(0)->comment('即決価格');
			$table->unsignedInteger('auction_lowest_price')->default(0)->comment('最低落札額');
			$table->unsignedTinyInteger('is_auction_negotiable_price')->default(0)->comment('価格交渉可能か');
			$table->text('auction_negotiable_price_message')->comment('価格交渉についてメッセージ');
			$table->unsignedInteger('auction_quantity')->default(0)->comment('数量');
			
			$table->unsignedTinyInteger('is_private_listing')->default(0)->comment('Private listing');
			$table->unsignedTinyInteger('is_charity')->default(0)->comment('チャリティ');
			$table->unsignedInteger('tax')->default(0)->comment('税金');
			//$table
			
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exhibits');
    }
};

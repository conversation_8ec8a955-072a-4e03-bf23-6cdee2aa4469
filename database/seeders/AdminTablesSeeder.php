<?php

namespace Database\Seeders;

use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Auth\Database\Role;
use Encore\Admin\Auth\Database\Permission;
use Encore\Admin\Auth\Database\Menu;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminTablesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // create a user.
        Administrator::truncate();
        Administrator::insert([
			[
				'username' => 'admin',
				'password' => Hash::make('admin'),
				'name'     => 'Administrator',
				'permission_type' => 1,
			],
			[
				'username' => 'worker',
				'password' => Hash::make('worker'),
				'name'     => 'Worker',
				'permission_type' => 2,
			],
			[
				'username' => 'member',
				'password' => Hash::make('member'),
				'name'     => 'Member',
				'permission_type' => 3,
			],
        ]);

        // create a role.
        Role::truncate();
        Role::insert([
			[
				'name' => '管理者',
				'slug' => 'administrator',
			],
			[
				'name' => '作業者',
				'slug' => 'worker',
			],
			[
				'name' => '利用者',
				'slug' => 'member',
			],
        ]);

        //// add role to user.
		// Administrator
        Administrator::first()->roles()->save(Role::first());
		// Worker
		Administrator::where('username','worker')->first()->roles()->save(Role::where('slug','worker')->first());
		// Member
		Administrator::where('username','member')->first()->roles()->save(Role::where('slug','member')->first());

        //create a permission
        Permission::truncate();
        Permission::insert([
            [
                'name'        => 'All permission',
                'slug'        => '*',
                'http_method' => '',
                'http_path'   => '*',
            ],
            [
                'name'        => 'Dashboard',
                'slug'        => 'dashboard',
                'http_method' => 'GET',
                'http_path'   => '/',
            ],
            [
                'name'        => 'Login',
                'slug'        => 'auth.login',
                'http_method' => '',
                'http_path'   => "/auth/login\r\n/auth/logout",
            ],
            [
                'name'        => 'User setting',
                'slug'        => 'auth.setting',
                'http_method' => 'GET,PUT',
                'http_path'   => '/auth/setting',
            ],
            [
                'name'        => 'Auth management',
                'slug'        => 'auth.management',
                'http_method' => '',
                'http_path'   => "/auth/roles\r\n/auth/permissions\r\n/auth/menu\r\n/auth/logs",
            ],
            [
                'name'        => 'Ebay Controll',
                'slug'        => 'ebays',
                'http_method' => '',
                'http_path'   => "/exhibits*\r\n/templates",
            ],
            [
                'name'        => 'Auth members',
                'slug'        => 'members',
                'http_method' => '',
                'http_path'   => "/member*",
            ],
        ]);
		
		// Administratorのパーミッション
        Role::first()->permissions()->save(Permission::first());
		
		// Memberのパーミッション
		foreach(Permission::whereIn('slug',['dashboard','auth.login','auth.setting','ebays'])->get() as $row){
			Role::where('slug','member')->first()->permissions()->save($row);
		}
		
		// Workerのパーミッション
		foreach(Permission::whereIn('slug',['dashboard','auth.login','auth.setting','ebays','members'])->get() as $row){
			Role::where('slug','worker')->first()->permissions()->save($row);
		}

        // add default menus.
        Menu::truncate();
        Menu::insert([
            [
                'parent_id' => 0,
                'order'     => 1,
                'title'     => 'Dashboard',
                'icon'      => 'fa-bar-chart',
                'uri'       => '/',
            ],
            [
                'parent_id' => 0,
                'order'     => 9,
                'title'     => 'Admin',
                'icon'      => 'fa-tasks',
                'uri'       => '',
            ],
            [
                'parent_id' => 0,
                'order'     => 2,
                'title'     => '管理ユーザ管理',
                'icon'      => 'fa-user-secret',
                'uri'       => 'users',
            ],
            [
                'parent_id' => 2,
                'order'     => 10,
                'title'     => 'ロール',
                'icon'      => 'fa-user',
                'uri'       => 'auth/roles',
            ],
            [
                'parent_id' => 2,
                'order'     => 11,
                'title'     => '権限',
                'icon'      => 'fa-ban',
                'uri'       => 'auth/permissions',
            ],
            [
                'parent_id' => 2,
                'order'     => 12,
                'title'     => 'Menu',
                'icon'      => 'fa-bars',
                'uri'       => 'auth/menu',
            ],
            [
                'parent_id' => 2,
                'order'     => 13,
                'title'     => 'Operation log',
                'icon'      => 'fa-history',
                'uri'       => 'auth/logs',
            ],
			[
				'parent_id' => 0,
				'order'     => 3,
				'title'     => '会員ユーザ管理',
				'icon'      => 'fa-users',
				'uri'       => 'member',
			],
			[
				'parent_id' => 0,
				'order'     => 4,
				'title'     => 'eBay出品',
				'icon'      => 'fa-cart-plus',
				'uri'       => 'exhibits/create',
			],
			[
				'parent_id' => 0,
				'order'     => 5,
				'title'     => 'マイページ',
				'icon'      => 'fa-bars',
				'uri'       => null,
			],
			[
				'parent_id' => 10,
				'order'     => 6,
				'title'     => 'ユーザ情報',
				'icon'      => 'fa-user',
				'uri'       => 'auth/setting',
			],
			[
				'parent_id' => 10,
				'order'     => 7,
				'title'     => '出品情報テンプレート',
				'icon'      => 'fa-newspaper-o',
				'uri'       => 'templates',
			],
			[
				'parent_id' => 0,
				'order'     => 8,
				'title'     => '出品履歴',
				'icon'      => 'fa-history',
				'uri'       => 'exhibits',
			],
			
			
        ]);

        //// add role to menu.
		// Admin以外は管理ユーザ管理とAdminメニュー見れる
        Menu::find(2)->roles()->save(Role::first());
		Menu::find(3)->roles()->save(Role::first());
		
		// 会員ユーザ管理はAdminとWorkerのみ
		Menu::find(8)->roles()->save(Role::first());
		Menu::find(8)->roles()->save(Role::find(2));
		
    }
}
